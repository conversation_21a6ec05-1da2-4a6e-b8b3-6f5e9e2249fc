kill @e[type=interaction,tag=lobby_chair]
kill @e[type=block_display,tag=lobby_chair]

execute positioned 11 1.5 94 run function mcm:util/chair/queue_chair
execute positioned 11 1.5 95 run function mcm:util/chair/queue_chair
execute positioned 11 1.5 96 run function mcm:util/chair/queue_chair
execute positioned 11 1.5 112 run function mcm:util/chair/queue_chair
execute positioned 11 1.5 113 run function mcm:util/chair/queue_chair
execute positioned 11 1.5 114 run function mcm:util/chair/queue_chair
execute positioned 10 1.5 98 run function mcm:util/chair/queue_chair
execute positioned 10 1.5 99 run function mcm:util/chair/queue_chair
execute positioned 10 1.5 100 run function mcm:util/chair/queue_chair
execute positioned 10 1.5 108 run function mcm:util/chair/queue_chair
execute positioned 10 1.5 109 run function mcm:util/chair/queue_chair
execute positioned 10 1.5 110 run function mcm:util/chair/queue_chair
execute positioned 9 1.5 103 run function mcm:util/chair/queue_chair
execute positioned 9 1.5 104 run function mcm:util/chair/queue_chair
execute positioned 9 1.5 105 run function mcm:util/chair/queue_chair
execute positioned 9 1.5 94 run function mcm:util/chair/queue_chair
execute positioned 9 1.5 95 run function mcm:util/chair/queue_chair
execute positioned 9 1.5 96 run function mcm:util/chair/queue_chair
execute positioned 9 1.5 112 run function mcm:util/chair/queue_chair
execute positioned 9 1.5 113 run function mcm:util/chair/queue_chair
execute positioned 9 1.5 114 run function mcm:util/chair/queue_chair
execute positioned 8 1.5 98 run function mcm:util/chair/queue_chair
execute positioned 8 1.5 99 run function mcm:util/chair/queue_chair
execute positioned 8 1.5 100 run function mcm:util/chair/queue_chair
execute positioned 8 1.5 108 run function mcm:util/chair/queue_chair
execute positioned 8 1.5 109 run function mcm:util/chair/queue_chair
execute positioned 8 1.5 110 run function mcm:util/chair/queue_chair
execute positioned 7 1.5 103 run function mcm:util/chair/queue_chair
execute positioned 7 1.5 104 run function mcm:util/chair/queue_chair
execute positioned 7 1.5 105 run function mcm:util/chair/queue_chair
execute positioned 5 0.5 94 run function mcm:util/chair/queue_chair
execute positioned 5 0.5 95 run function mcm:util/chair/queue_chair
execute positioned 5 0.5 96 run function mcm:util/chair/queue_chair
execute positioned 5 0.5 112 run function mcm:util/chair/queue_chair
execute positioned 5 0.5 113 run function mcm:util/chair/queue_chair
execute positioned 5 0.5 114 run function mcm:util/chair/queue_chair
execute positioned 4 0.5 98 run function mcm:util/chair/queue_chair
execute positioned 4 0.5 99 run function mcm:util/chair/queue_chair
execute positioned 4 0.5 100 run function mcm:util/chair/queue_chair
execute positioned 4 0.5 108 run function mcm:util/chair/queue_chair
execute positioned 4 0.5 109 run function mcm:util/chair/queue_chair
execute positioned 4 0.5 110 run function mcm:util/chair/queue_chair
execute positioned 3 0.5 103 run function mcm:util/chair/queue_chair
execute positioned 3 0.5 104 run function mcm:util/chair/queue_chair
execute positioned 3 0.5 105 run function mcm:util/chair/queue_chair
execute positioned 3 0.5 94 run function mcm:util/chair/queue_chair
execute positioned 3 0.5 95 run function mcm:util/chair/queue_chair
execute positioned 3 0.5 96 run function mcm:util/chair/queue_chair
execute positioned 3 0.5 112 run function mcm:util/chair/queue_chair
execute positioned 3 0.5 113 run function mcm:util/chair/queue_chair
execute positioned 3 0.5 114 run function mcm:util/chair/queue_chair
execute positioned 2 0.5 98 run function mcm:util/chair/queue_chair
execute positioned 2 0.5 99 run function mcm:util/chair/queue_chair
execute positioned 2 0.5 100 run function mcm:util/chair/queue_chair
execute positioned 2 0.5 108 run function mcm:util/chair/queue_chair
execute positioned 2 0.5 109 run function mcm:util/chair/queue_chair
execute positioned 2 0.5 110 run function mcm:util/chair/queue_chair
execute positioned 1 0.5 103 run function mcm:util/chair/queue_chair
execute positioned 1 0.5 104 run function mcm:util/chair/queue_chair
execute positioned 1 0.5 105 run function mcm:util/chair/queue_chair
execute positioned -1 -0.5 94 run function mcm:util/chair/queue_chair
execute positioned -1 -0.5 95 run function mcm:util/chair/queue_chair
execute positioned -1 -0.5 96 run function mcm:util/chair/queue_chair
execute positioned -1 -0.5 112 run function mcm:util/chair/queue_chair
execute positioned -1 -0.5 113 run function mcm:util/chair/queue_chair
execute positioned -1 -0.5 114 run function mcm:util/chair/queue_chair
execute positioned -2 -0.5 98 run function mcm:util/chair/queue_chair
execute positioned -2 -0.5 99 run function mcm:util/chair/queue_chair
execute positioned -2 -0.5 100 run function mcm:util/chair/queue_chair
execute positioned -2 -0.5 108 run function mcm:util/chair/queue_chair
execute positioned -2 -0.5 109 run function mcm:util/chair/queue_chair
execute positioned -2 -0.5 110 run function mcm:util/chair/queue_chair
execute positioned -3 -0.5 103 run function mcm:util/chair/queue_chair
execute positioned -3 -0.5 104 run function mcm:util/chair/queue_chair
execute positioned -3 -0.5 105 run function mcm:util/chair/queue_chair
execute positioned -3 -0.5 94 run function mcm:util/chair/queue_chair
execute positioned -3 -0.5 95 run function mcm:util/chair/queue_chair
execute positioned -3 -0.5 96 run function mcm:util/chair/queue_chair
execute positioned -3 -0.5 112 run function mcm:util/chair/queue_chair
execute positioned -3 -0.5 113 run function mcm:util/chair/queue_chair
execute positioned -3 -0.5 114 run function mcm:util/chair/queue_chair
execute positioned -4 -0.5 98 run function mcm:util/chair/queue_chair
execute positioned -4 -0.5 99 run function mcm:util/chair/queue_chair
execute positioned -4 -0.5 100 run function mcm:util/chair/queue_chair
execute positioned -4 -0.5 108 run function mcm:util/chair/queue_chair
execute positioned -4 -0.5 109 run function mcm:util/chair/queue_chair
execute positioned -4 -0.5 110 run function mcm:util/chair/queue_chair
execute positioned -5 -0.5 103 run function mcm:util/chair/queue_chair
execute positioned -5 -0.5 104 run function mcm:util/chair/queue_chair
execute positioned -5 -0.5 105 run function mcm:util/chair/queue_chair
execute positioned -7 -1.5 94 run function mcm:util/chair/queue_chair
execute positioned -7 -1.5 95 run function mcm:util/chair/queue_chair
execute positioned -7 -1.5 96 run function mcm:util/chair/queue_chair
execute positioned -7 -1.5 112 run function mcm:util/chair/queue_chair
execute positioned -7 -1.5 113 run function mcm:util/chair/queue_chair
execute positioned -7 -1.5 114 run function mcm:util/chair/queue_chair
execute positioned -8 -1.5 98 run function mcm:util/chair/queue_chair
execute positioned -8 -1.5 99 run function mcm:util/chair/queue_chair
execute positioned -8 -1.5 100 run function mcm:util/chair/queue_chair
execute positioned -8 -1.5 108 run function mcm:util/chair/queue_chair
execute positioned -8 -1.5 109 run function mcm:util/chair/queue_chair
execute positioned -8 -1.5 110 run function mcm:util/chair/queue_chair
execute positioned -9 -1.5 103 run function mcm:util/chair/queue_chair
execute positioned -9 -1.5 104 run function mcm:util/chair/queue_chair
execute positioned -9 -1.5 105 run function mcm:util/chair/queue_chair
execute positioned -9 -1.5 94 run function mcm:util/chair/queue_chair
execute positioned -9 -1.5 95 run function mcm:util/chair/queue_chair
execute positioned -9 -1.5 96 run function mcm:util/chair/queue_chair
execute positioned -9 -1.5 112 run function mcm:util/chair/queue_chair
execute positioned -9 -1.5 113 run function mcm:util/chair/queue_chair
execute positioned -9 -1.5 114 run function mcm:util/chair/queue_chair
execute positioned -10 -1.5 98 run function mcm:util/chair/queue_chair
execute positioned -10 -1.5 99 run function mcm:util/chair/queue_chair
execute positioned -10 -1.5 100 run function mcm:util/chair/queue_chair
execute positioned -10 -1.5 108 run function mcm:util/chair/queue_chair
execute positioned -10 -1.5 109 run function mcm:util/chair/queue_chair
execute positioned -10 -1.5 110 run function mcm:util/chair/queue_chair
execute positioned -11 -1.5 103 run function mcm:util/chair/queue_chair
execute positioned -11 -1.5 104 run function mcm:util/chair/queue_chair
execute positioned -11 -1.5 105 run function mcm:util/chair/queue_chair
execute positioned -13 -2.5 94 run function mcm:util/chair/queue_chair
execute positioned -13 -2.5 95 run function mcm:util/chair/queue_chair
execute positioned -13 -2.5 96 run function mcm:util/chair/queue_chair
execute positioned -13 -2.5 112 run function mcm:util/chair/queue_chair
execute positioned -13 -2.5 113 run function mcm:util/chair/queue_chair
execute positioned -13 -2.5 114 run function mcm:util/chair/queue_chair
execute positioned -14 -2.5 98 run function mcm:util/chair/queue_chair
execute positioned -14 -2.5 99 run function mcm:util/chair/queue_chair
execute positioned -14 -2.5 100 run function mcm:util/chair/queue_chair
execute positioned -14 -2.5 108 run function mcm:util/chair/queue_chair
execute positioned -14 -2.5 109 run function mcm:util/chair/queue_chair
execute positioned -14 -2.5 110 run function mcm:util/chair/queue_chair
execute positioned -15 -2.5 103 run function mcm:util/chair/queue_chair
execute positioned -15 -2.5 104 run function mcm:util/chair/queue_chair
execute positioned -15 -2.5 105 run function mcm:util/chair/queue_chair
execute positioned -15 -2.5 94 run function mcm:util/chair/queue_chair
execute positioned -15 -2.5 95 run function mcm:util/chair/queue_chair
execute positioned -15 -2.5 96 run function mcm:util/chair/queue_chair
execute positioned -15 -2.5 112 run function mcm:util/chair/queue_chair
execute positioned -15 -2.5 113 run function mcm:util/chair/queue_chair
execute positioned -15 -2.5 114 run function mcm:util/chair/queue_chair
execute positioned -16 -2.5 98 run function mcm:util/chair/queue_chair
execute positioned -16 -2.5 99 run function mcm:util/chair/queue_chair
execute positioned -16 -2.5 100 run function mcm:util/chair/queue_chair
execute positioned -16 -2.5 108 run function mcm:util/chair/queue_chair
execute positioned -16 -2.5 109 run function mcm:util/chair/queue_chair
execute positioned -16 -2.5 110 run function mcm:util/chair/queue_chair
execute positioned -17 -2.5 103 run function mcm:util/chair/queue_chair
execute positioned -17 -2.5 104 run function mcm:util/chair/queue_chair
execute positioned -17 -2.5 105 run function mcm:util/chair/queue_chair
execute positioned -19 -3.5 94 run function mcm:util/chair/queue_chair
execute positioned -19 -3.5 95 run function mcm:util/chair/queue_chair
execute positioned -19 -3.5 96 run function mcm:util/chair/queue_chair
execute positioned -19 -3.5 112 run function mcm:util/chair/queue_chair
execute positioned -19 -3.5 113 run function mcm:util/chair/queue_chair
execute positioned -19 -3.5 114 run function mcm:util/chair/queue_chair
execute positioned -20 -3.5 98 run function mcm:util/chair/queue_chair
execute positioned -20 -3.5 99 run function mcm:util/chair/queue_chair
execute positioned -20 -3.5 100 run function mcm:util/chair/queue_chair
execute positioned -20 -3.5 108 run function mcm:util/chair/queue_chair
execute positioned -20 -3.5 109 run function mcm:util/chair/queue_chair
execute positioned -20 -3.5 110 run function mcm:util/chair/queue_chair
execute positioned -21 -3.5 103 run function mcm:util/chair/queue_chair
execute positioned -21 -3.5 104 run function mcm:util/chair/queue_chair
execute positioned -21 -3.5 105 run function mcm:util/chair/queue_chair
execute positioned -21 -3.5 94 run function mcm:util/chair/queue_chair
execute positioned -21 -3.5 95 run function mcm:util/chair/queue_chair
execute positioned -21 -3.5 96 run function mcm:util/chair/queue_chair
execute positioned -21 -3.5 112 run function mcm:util/chair/queue_chair
execute positioned -21 -3.5 113 run function mcm:util/chair/queue_chair
execute positioned -21 -3.5 114 run function mcm:util/chair/queue_chair
execute positioned -22 -3.5 98 run function mcm:util/chair/queue_chair
execute positioned -22 -3.5 99 run function mcm:util/chair/queue_chair
execute positioned -22 -3.5 100 run function mcm:util/chair/queue_chair
execute positioned -22 -3.5 108 run function mcm:util/chair/queue_chair
execute positioned -22 -3.5 109 run function mcm:util/chair/queue_chair
execute positioned -22 -3.5 110 run function mcm:util/chair/queue_chair
execute positioned -23 -3.5 103 run function mcm:util/chair/queue_chair
execute positioned -23 -3.5 104 run function mcm:util/chair/queue_chair
execute positioned -23 -3.5 105 run function mcm:util/chair/queue_chair

execute positioned 11 13.5 94 run function mcm:util/chair/spectator_chair
execute positioned 11 13.5 95 run function mcm:util/chair/spectator_chair
execute positioned 11 13.5 96 run function mcm:util/chair/spectator_chair
execute positioned 11 13.5 112 run function mcm:util/chair/spectator_chair
execute positioned 11 13.5 113 run function mcm:util/chair/spectator_chair
execute positioned 11 13.5 114 run function mcm:util/chair/spectator_chair
execute positioned 10 13.5 98 run function mcm:util/chair/spectator_chair
execute positioned 10 13.5 99 run function mcm:util/chair/spectator_chair
execute positioned 10 13.5 100 run function mcm:util/chair/spectator_chair
execute positioned 10 13.5 108 run function mcm:util/chair/spectator_chair
execute positioned 10 13.5 109 run function mcm:util/chair/spectator_chair
execute positioned 10 13.5 110 run function mcm:util/chair/spectator_chair
execute positioned 9 13.5 103 run function mcm:util/chair/spectator_chair
execute positioned 9 13.5 104 run function mcm:util/chair/spectator_chair
execute positioned 9 13.5 105 run function mcm:util/chair/spectator_chair
execute positioned 9 13.5 94 run function mcm:util/chair/spectator_chair
execute positioned 9 13.5 95 run function mcm:util/chair/spectator_chair
execute positioned 9 13.5 96 run function mcm:util/chair/spectator_chair
execute positioned 9 13.5 112 run function mcm:util/chair/spectator_chair
execute positioned 9 13.5 113 run function mcm:util/chair/spectator_chair
execute positioned 9 13.5 114 run function mcm:util/chair/spectator_chair
execute positioned 8 13.5 98 run function mcm:util/chair/spectator_chair
execute positioned 8 13.5 99 run function mcm:util/chair/spectator_chair
execute positioned 8 13.5 100 run function mcm:util/chair/spectator_chair
execute positioned 8 13.5 108 run function mcm:util/chair/spectator_chair
execute positioned 8 13.5 109 run function mcm:util/chair/spectator_chair
execute positioned 8 13.5 110 run function mcm:util/chair/spectator_chair
execute positioned 7 13.5 103 run function mcm:util/chair/spectator_chair
execute positioned 7 13.5 104 run function mcm:util/chair/spectator_chair
execute positioned 7 13.5 105 run function mcm:util/chair/spectator_chair
execute positioned 5 12.5 94 run function mcm:util/chair/spectator_chair
execute positioned 5 12.5 95 run function mcm:util/chair/spectator_chair
execute positioned 5 12.5 96 run function mcm:util/chair/spectator_chair
execute positioned 5 12.5 112 run function mcm:util/chair/spectator_chair
execute positioned 5 12.5 113 run function mcm:util/chair/spectator_chair
execute positioned 5 12.5 114 run function mcm:util/chair/spectator_chair
execute positioned 4 12.5 98 run function mcm:util/chair/spectator_chair
execute positioned 4 12.5 99 run function mcm:util/chair/spectator_chair
execute positioned 4 12.5 100 run function mcm:util/chair/spectator_chair
execute positioned 4 12.5 108 run function mcm:util/chair/spectator_chair
execute positioned 4 12.5 109 run function mcm:util/chair/spectator_chair
execute positioned 4 12.5 110 run function mcm:util/chair/spectator_chair
execute positioned 3 12.5 103 run function mcm:util/chair/spectator_chair
execute positioned 3 12.5 104 run function mcm:util/chair/spectator_chair
execute positioned 3 12.5 105 run function mcm:util/chair/spectator_chair
execute positioned 3 12.5 94 run function mcm:util/chair/spectator_chair
execute positioned 3 12.5 95 run function mcm:util/chair/spectator_chair
execute positioned 3 12.5 96 run function mcm:util/chair/spectator_chair
execute positioned 3 12.5 112 run function mcm:util/chair/spectator_chair
execute positioned 3 12.5 113 run function mcm:util/chair/spectator_chair
execute positioned 3 12.5 114 run function mcm:util/chair/spectator_chair
execute positioned 2 12.5 98 run function mcm:util/chair/spectator_chair
execute positioned 2 12.5 99 run function mcm:util/chair/spectator_chair
execute positioned 2 12.5 100 run function mcm:util/chair/spectator_chair
execute positioned 2 12.5 108 run function mcm:util/chair/spectator_chair
execute positioned 2 12.5 109 run function mcm:util/chair/spectator_chair
execute positioned 2 12.5 110 run function mcm:util/chair/spectator_chair
execute positioned 1 12.5 103 run function mcm:util/chair/spectator_chair
execute positioned 1 12.5 104 run function mcm:util/chair/spectator_chair
execute positioned 1 12.5 105 run function mcm:util/chair/spectator_chair
