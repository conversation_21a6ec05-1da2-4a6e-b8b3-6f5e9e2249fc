data merge storage mcm:library {books:[]}
data modify storage mcm:library books append value {pos:" 925 174 1073 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 922 174 1079 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 925 174 1079 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 922 176 1073 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 924 176 1079 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 927 177 1090 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 922 174 1127 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 925 174 1133 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 925 175 1127 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 922 175 1133 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 939 162 1083 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 932 163 1077 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 940 163 1077 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 933 163 1080 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 938 163 1080 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 933 163 1083 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 931 165 1077 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 931 165 1080 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 930 165 1083 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 940 165 1083 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 942 166 1080 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 932 166 1083 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 935 173 1074 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 943 174 1074 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 938 174 1085 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 943 175 1085 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 934 175 1086 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 931 175 1087 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 935 177 1074 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 939 177 1074 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 938 179 1074 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 935 179 1086 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 942 151 1094 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 937 152 1090 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 937 153 1094 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 942 155 1090 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 936 162 1090 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 931 163 1090 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 941 163 1090 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 931 163 1093 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 932 163 1093 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 936 163 1093 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 942 163 1093 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 940 165 1090 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 931 165 1093 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 935 165 1093 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 940 165 1093 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 931 166 1090 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 937 151 1116 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 939 152 1112 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 942 153 1112 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 942 153 1116 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 938 156 1112 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 942 156 1112 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 933 162 1113 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 941 162 1113 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 932 162 1116 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 931 163 1113 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 936 163 1113 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 943 163 1113 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 930 163 1116 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 937 163 1116 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 941 163 1116 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 937 165 1113 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 942 165 1113 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 931 165 1116 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 935 165 1116 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 942 165 1116 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 930 142 1120 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 932 142 1124 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 932 143 1120 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 944 163 1077 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 945 163 1080 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 944 163 1083 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 945 166 1077 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 947 174 1074 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 947 174 1085 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 946 177 1074 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 947 177 1085 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 944 179 1074 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 946 152 1090 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 950 152 1090 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 951 152 1094 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 946 153 1094 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 950 155 1090 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 950 155 1094 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 946 152 1112 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 945 152 1116 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 951 152 1116 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 950 153 1112 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 946 155 1116 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 964 173 1074 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 970 174 1074 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 974 174 1074 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 961 175 1074 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 964 178 1074 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 969 178 1074 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 960 179 1074 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 973 179 1074 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 966 152 1090 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 970 152 1090 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 966 152 1094 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 970 152 1094 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 975 153 1090 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 975 153 1094 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 962 155 1090 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 961 155 1094 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 966 155 1094 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 966 157 1090 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 966 151 1112 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 970 152 1112 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 973 152 1112 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 966 152 1116 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 962 153 1112 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 962 153 1116 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 971 153 1116 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 974 153 1116 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 971 155 1112 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 974 156 1112 ",rotation:"[0.0f,0.0f]",offset1:" ~ ~0.3125 ~-0.5 ",offset2:" ~ ~0.125 ~0.85 "}
data modify storage mcm:library books append value {pos:" 926 141 1094 ",rotation:"[270.0f,0.0f]",offset1:" ~-0.5 ~0.5 ~ ",offset2:" ~0.85 ~0.125 ~ "}
data modify storage mcm:library books append value {pos:" 922 141 1095 ",rotation:"[270.0f,0.0f]",offset1:" ~-0.5 ~0.5 ~ ",offset2:" ~0.85 ~0.125 ~ "}
data modify storage mcm:library books append value {pos:" 922 142 1098 ",rotation:"[270.0f,0.0f]",offset1:" ~-0.5 ~0.5 ~ ",offset2:" ~0.85 ~0.125 ~ "}
data modify storage mcm:library books append value {pos:" 926 174 1091 ",rotation:"[270.0f,0.0f]",offset1:" ~-0.5 ~0.5 ~ ",offset2:" ~0.85 ~0.125 ~ "}
data modify storage mcm:library books append value {pos:" 926 141 1111 ",rotation:"[270.0f,0.0f]",offset1:" ~-0.5 ~0.5 ~ ",offset2:" ~0.85 ~0.125 ~ "}
data modify storage mcm:library books append value {pos:" 927 175 1118 ",rotation:"[270.0f,0.0f]",offset1:" ~-0.5 ~0.5 ~ ",offset2:" ~0.85 ~0.125 ~ "}
data modify storage mcm:library books append value {pos:" 934 141 1097 ",rotation:"[270.0f,0.0f]",offset1:" ~-0.5 ~0.5 ~ ",offset2:" ~0.85 ~0.125 ~ "}
data modify storage mcm:library books append value {pos:" 930 142 1097 ",rotation:"[270.0f,0.0f]",offset1:" ~-0.5 ~0.5 ~ ",offset2:" ~0.85 ~0.125 ~ "}
data modify storage mcm:library books append value {pos:" 942 143 1094 ",rotation:"[270.0f,0.0f]",offset1:" ~-0.5 ~0.5 ~ ",offset2:" ~0.85 ~0.125 ~ "}
data modify storage mcm:library books append value {pos:" 930 141 1113 ",rotation:"[270.0f,0.0f]",offset1:" ~-0.5 ~0.5 ~ ",offset2:" ~0.85 ~0.125 ~ "}
data modify storage mcm:library books append value {pos:" 942 141 1113 ",rotation:"[270.0f,0.0f]",offset1:" ~-0.5 ~0.5 ~ ",offset2:" ~0.85 ~0.125 ~ "}
data modify storage mcm:library books append value {pos:" 934 143 1111 ",rotation:"[270.0f,0.0f]",offset1:" ~-0.5 ~0.5 ~ ",offset2:" ~0.85 ~0.125 ~ "}
data modify storage mcm:library books append value {pos:" 942 144 1111 ",rotation:"[270.0f,0.0f]",offset1:" ~-0.5 ~0.5 ~ ",offset2:" ~0.85 ~0.125 ~ "}
data modify storage mcm:library books append value {pos:" 936 163 1127 ",rotation:"[270.0f,0.0f]",offset1:" ~-0.5 ~0.5 ~ ",offset2:" ~0.85 ~0.125 ~ "}
data modify storage mcm:library books append value {pos:" 942 163 1127 ",rotation:"[270.0f,0.0f]",offset1:" ~-0.5 ~0.5 ~ ",offset2:" ~0.85 ~0.125 ~ "}
data modify storage mcm:library books append value {pos:" 930 163 1130 ",rotation:"[270.0f,0.0f]",offset1:" ~-0.5 ~0.5 ~ ",offset2:" ~0.85 ~0.125 ~ "}
data modify storage mcm:library books append value {pos:" 942 164 1130 ",rotation:"[270.0f,0.0f]",offset1:" ~-0.5 ~0.5 ~ ",offset2:" ~0.85 ~0.125 ~ "}
data modify storage mcm:library books append value {pos:" 950 140 1094 ",rotation:"[270.0f,0.0f]",offset1:" ~-0.5 ~0.5 ~ ",offset2:" ~0.85 ~0.125 ~ "}
data modify storage mcm:library books append value {pos:" 958 141 1097 ",rotation:"[270.0f,0.0f]",offset1:" ~-0.5 ~0.5 ~ ",offset2:" ~0.85 ~0.125 ~ "}
data modify storage mcm:library books append value {pos:" 950 143 1097 ",rotation:"[270.0f,0.0f]",offset1:" ~-0.5 ~0.5 ~ ",offset2:" ~0.85 ~0.125 ~ "}
data modify storage mcm:library books append value {pos:" 950 141 1111 ",rotation:"[270.0f,0.0f]",offset1:" ~-0.5 ~0.5 ~ ",offset2:" ~0.85 ~0.125 ~ "}
data modify storage mcm:library books append value {pos:" 958 143 1113 ",rotation:"[270.0f,0.0f]",offset1:" ~-0.5 ~0.5 ~ ",offset2:" ~0.85 ~0.125 ~ "}
data modify storage mcm:library books append value {pos:" 948 163 1126 ",rotation:"[270.0f,0.0f]",offset1:" ~-0.5 ~0.5 ~ ",offset2:" ~0.85 ~0.125 ~ "}
data modify storage mcm:library books append value {pos:" 976 163 1085 ",rotation:"[270.0f,0.0f]",offset1:" ~-0.5 ~0.5 ~ ",offset2:" ~0.85 ~0.125 ~ "}
data modify storage mcm:library books append value {pos:" 976 163 1121 ",rotation:"[270.0f,0.0f]",offset1:" ~-0.5 ~0.5 ~ ",offset2:" ~0.85 ~0.125 ~ "}
data modify storage mcm:library books append value {pos:" 925 174 1075 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 922 174 1081 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 922 175 1075 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 924 176 1081 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 926 177 1117 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 923 174 1129 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 924 174 1135 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 922 175 1135 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 922 176 1129 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 925 176 1129 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 931 163 1079 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 932 163 1082 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 937 163 1082 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 933 163 1085 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 939 163 1085 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 942 163 1085 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 940 165 1079 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 933 165 1082 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 937 165 1082 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 936 165 1085 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 936 166 1079 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 940 166 1082 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 943 166 1082 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 933 166 1085 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 938 174 1076 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 935 177 1076 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 938 179 1076 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 938 151 1096 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 943 152 1092 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 937 152 1096 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 937 153 1092 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 942 153 1092 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 942 155 1092 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 939 155 1096 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 939 156 1096 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 942 156 1096 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 938 157 1096 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 943 157 1096 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 931 162 1092 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 936 162 1092 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 942 162 1095 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 932 163 1092 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 941 163 1092 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 932 163 1095 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 936 163 1095 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 940 163 1095 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 930 165 1092 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 938 165 1092 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 937 165 1095 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 941 165 1095 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 935 166 1092 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 935 166 1095 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 937 152 1114 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 943 152 1114 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 942 152 1118 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 938 153 1118 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 942 155 1114 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 931 162 1115 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 938 162 1118 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 937 163 1115 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 938 163 1115 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 942 163 1115 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 930 163 1118 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 937 163 1118 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 941 163 1118 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 935 165 1115 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 941 165 1115 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 933 165 1118 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 932 166 1115 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 942 166 1118 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 930 141 1122 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 931 141 1126 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 932 142 1122 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 933 143 1126 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 930 174 1120 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 936 174 1122 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 939 174 1123 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 943 174 1123 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 931 178 1121 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 935 178 1122 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 938 178 1123 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 935 179 1122 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 944 163 1079 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 944 163 1082 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 945 163 1085 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 945 165 1079 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 945 165 1082 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 944 165 1085 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 944 174 1076 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 946 175 1076 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 944 178 1076 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 947 179 1076 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 946 152 1092 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 950 152 1092 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 945 152 1096 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 950 152 1096 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 946 153 1092 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 946 153 1096 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 946 155 1096 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 950 152 1114 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 946 153 1114 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 945 153 1118 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 950 153 1118 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 951 155 1114 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 946 155 1118 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 946 157 1118 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 947 177 1123 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 972 163 1077 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 961 174 1076 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 969 174 1076 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 972 174 1076 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 964 175 1076 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 965 177 1076 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 973 177 1076 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 961 178 1076 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 969 179 1076 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 970 151 1092 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 962 152 1092 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 966 152 1092 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 970 152 1092 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 975 152 1092 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 966 152 1096 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 970 152 1096 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 962 153 1092 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 962 153 1096 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 975 153 1096 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 966 155 1092 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 967 155 1092 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 970 155 1092 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 970 156 1096 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 974 156 1096 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 971 157 1096 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 965 162 1089 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 963 163 1089 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 966 151 1114 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 962 152 1114 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 970 152 1114 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 975 152 1114 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 970 152 1118 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 967 153 1114 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 975 153 1114 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 962 153 1118 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 966 153 1118 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 974 153 1118 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 962 155 1118 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 966 157 1118 ",rotation:"[180.0f,0.0f]",offset1:" ~ ~0.3125 ~0.5 ",offset2:" ~ ~0.125 ~-0.85 "}
data modify storage mcm:library books append value {pos:" 924 142 1097 ",rotation:"[90.0f,0.0f]",offset1:" ~0.5 ~0.5 ~ ",offset2:" ~-0.85 ~0.125 ~ "}
data modify storage mcm:library books append value {pos:" 932 140 1095 ",rotation:"[90.0f,0.0f]",offset1:" ~0.5 ~0.5 ~ ",offset2:" ~-0.85 ~0.125 ~ "}
data modify storage mcm:library books append value {pos:" 928 141 1095 ",rotation:"[90.0f,0.0f]",offset1:" ~0.5 ~0.5 ~ ",offset2:" ~-0.85 ~0.125 ~ "}
data modify storage mcm:library books append value {pos:" 932 142 1097 ",rotation:"[90.0f,0.0f]",offset1:" ~0.5 ~0.5 ~ ",offset2:" ~-0.85 ~0.125 ~ "}
data modify storage mcm:library books append value {pos:" 938 142 1098 ",rotation:"[90.0f,0.0f]",offset1:" ~0.5 ~0.5 ~ ",offset2:" ~-0.85 ~0.125 ~ "}
data modify storage mcm:library books append value {pos:" 938 144 1095 ",rotation:"[90.0f,0.0f]",offset1:" ~0.5 ~0.5 ~ ",offset2:" ~-0.85 ~0.125 ~ "}
data modify storage mcm:library books append value {pos:" 928 141 1111 ",rotation:"[90.0f,0.0f]",offset1:" ~0.5 ~0.5 ~ ",offset2:" ~-0.85 ~0.125 ~ "}
data modify storage mcm:library books append value {pos:" 932 141 1113 ",rotation:"[90.0f,0.0f]",offset1:" ~0.5 ~0.5 ~ ",offset2:" ~-0.85 ~0.125 ~ "}
data modify storage mcm:library books append value {pos:" 932 142 1110 ",rotation:"[90.0f,0.0f]",offset1:" ~0.5 ~0.5 ~ ",offset2:" ~-0.85 ~0.125 ~ "}
data modify storage mcm:library books append value {pos:" 938 142 1114 ",rotation:"[90.0f,0.0f]",offset1:" ~0.5 ~0.5 ~ ",offset2:" ~-0.85 ~0.125 ~ "}
data modify storage mcm:library books append value {pos:" 932 163 1127 ",rotation:"[90.0f,0.0f]",offset1:" ~0.5 ~0.5 ~ ",offset2:" ~-0.85 ~0.125 ~ "}
data modify storage mcm:library books append value {pos:" 938 163 1129 ",rotation:"[90.0f,0.0f]",offset1:" ~0.5 ~0.5 ~ ",offset2:" ~-0.85 ~0.125 ~ "}
data modify storage mcm:library books append value {pos:" 954 141 1095 ",rotation:"[90.0f,0.0f]",offset1:" ~0.5 ~0.5 ~ ",offset2:" ~-0.85 ~0.125 ~ "}
data modify storage mcm:library books append value {pos:" 946 142 1094 ",rotation:"[90.0f,0.0f]",offset1:" ~0.5 ~0.5 ~ ",offset2:" ~-0.85 ~0.125 ~ "}
data modify storage mcm:library books append value {pos:" 954 144 1098 ",rotation:"[90.0f,0.0f]",offset1:" ~0.5 ~0.5 ~ ",offset2:" ~-0.85 ~0.125 ~ "}
data modify storage mcm:library books append value {pos:" 954 142 1114 ",rotation:"[90.0f,0.0f]",offset1:" ~0.5 ~0.5 ~ ",offset2:" ~-0.85 ~0.125 ~ "}
data modify storage mcm:library books append value {pos:" 946 143 1110 ",rotation:"[90.0f,0.0f]",offset1:" ~0.5 ~0.5 ~ ",offset2:" ~-0.85 ~0.125 ~ "}
data modify storage mcm:library books append value {pos:" 954 144 1110 ",rotation:"[90.0f,0.0f]",offset1:" ~0.5 ~0.5 ~ ",offset2:" ~-0.85 ~0.125 ~ "}
data modify storage mcm:library books append value {pos:" 950 163 1126 ",rotation:"[90.0f,0.0f]",offset1:" ~0.5 ~0.5 ~ ",offset2:" ~-0.85 ~0.125 ~ "}
data modify storage mcm:library books append value {pos:" 944 163 1127 ",rotation:"[90.0f,0.0f]",offset1:" ~0.5 ~0.5 ~ ",offset2:" ~-0.85 ~0.125 ~ "}
data modify storage mcm:library books append value {pos:" 944 164 1129 ",rotation:"[90.0f,0.0f]",offset1:" ~0.5 ~0.5 ~ ",offset2:" ~-0.85 ~0.125 ~ "}
data modify storage mcm:library books append value {pos:" 962 142 1098 ",rotation:"[90.0f,0.0f]",offset1:" ~0.5 ~0.5 ~ ",offset2:" ~-0.85 ~0.125 ~ "}
data modify storage mcm:library books append value {pos:" 962 143 1094 ",rotation:"[90.0f,0.0f]",offset1:" ~0.5 ~0.5 ~ ",offset2:" ~-0.85 ~0.125 ~ "}
data modify storage mcm:library books append value {pos:" 962 142 1111 ",rotation:"[90.0f,0.0f]",offset1:" ~0.5 ~0.5 ~ ",offset2:" ~-0.85 ~0.125 ~ "}
