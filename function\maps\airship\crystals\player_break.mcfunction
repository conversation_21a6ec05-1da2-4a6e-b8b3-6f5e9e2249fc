#> Give crystal
# TODO: Make this an amethyst shard eventually
execute store result score $temp math run clear @s carrot_on_a_stick[item_model="minecraft:amethyst_shard"] 0
execute if score $temp math matches 1.. run tellraw @s ["", {"text":"| ","bold":true,"color":"dark_gray"},{"translate":"mcm.airship.crystal.heavy","underlined":false,"color":"red"}]
execute if score $temp math matches 0 run give @s carrot_on_a_stick[custom_data={NoDrop:1b},item_model="minecraft:amethyst_shard",custom_name='{"translate":"item.minecraft.amethyst_shard","italic":false}']
execute if score $temp math matches 0 at @e[type=interaction,tag=airship_crystal_spawn,limit=1,sort=nearest] run setblock ~ ~ ~ air destroy

#> Remove interactor and reset score
execute if score $temp math matches 0 as @e[type=interaction,tag=airship_crystal_spawn,limit=1,sort=nearest] run scoreboard players set @s airship_crystal_growth 0
