#> Forceload the Vineyard map
#forceload add 2111 2095 1937 1911

#> Kill potential leftover entities
kill @e[tag=MapEntity]

#> Set Player Spawnpoints
execute positioned 1961 66 1986 run function mcm:game/markers/playerspawn
execute positioned 1966 66 1959 run function mcm:game/markers/playerspawn
execute positioned 1987 69 1993 run function mcm:game/markers/playerspawn
execute positioned 2004 70 1987 run function mcm:game/markers/playerspawn
execute positioned 1985 66 1956 run function mcm:game/markers/playerspawn
execute positioned 2012 66 1942 run function mcm:game/markers/playerspawn
execute positioned 2030 65 1926 run function mcm:game/markers/playerspawn
execute positioned 2059 65 1926 run function mcm:game/markers/playerspawn
execute positioned 2042 66 1933 run function mcm:game/markers/playerspawn
execute positioned 2083 66 1934 run function mcm:game/markers/playerspawn
execute positioned 2096 68 1958 run function mcm:game/markers/playerspawn
execute positioned 2077 68 1948 run function mcm:game/markers/playerspawn
execute positioned 2043 68 1947 run function mcm:game/markers/playerspawn
execute positioned 2051 71 1965 run function mcm:game/markers/playerspawn
execute positioned 2025 70 1975 run function mcm:game/markers/playerspawn
execute positioned 2048 78 2007 run function mcm:game/markers/playerspawn
execute positioned 2048 78 2042 run function mcm:game/markers/playerspawn
execute positioned 2067 87 2011 run function mcm:game/markers/playerspawn
execute positioned 2048 87 2036 run function mcm:game/markers/playerspawn
execute positioned 2048 87 2005 run function mcm:game/markers/playerspawn
execute positioned 2102 78 1997 run function mcm:game/markers/playerspawn
execute positioned 2102 78 2058 run function mcm:game/markers/playerspawn
execute positioned 2081 78 2072 run function mcm:game/markers/playerspawn
execute positioned 2048 78 2080 run function mcm:game/markers/playerspawn
execute positioned 2027 78 2072 run function mcm:game/markers/playerspawn
execute positioned 2066 78 2036 run function mcm:game/markers/playerspawn
execute positioned 2068 78 1991 run function mcm:game/markers/playerspawn
execute positioned 1995 70 2059 run function mcm:game/markers/playerspawn
execute positioned 1995 70 2039 run function mcm:game/markers/playerspawn
execute positioned 1996 70 2010 run function mcm:game/markers/playerspawn
execute positioned 1988 69 2043 run function mcm:game/markers/playerspawn
execute positioned 1981 68 2016 run function mcm:game/markers/playerspawn
execute positioned 1975 67 2006 run function mcm:game/markers/playerspawn
execute positioned 1974 67 2050 run function mcm:game/markers/playerspawn
execute positioned 2076 95 2027 run function mcm:game/markers/playerspawn
execute positioned 2056 95 2034 run function mcm:game/markers/playerspawn
execute positioned 2074 95 2039 run function mcm:game/markers/playerspawn
execute positioned 2054 95 2015 run function mcm:game/markers/playerspawn
execute positioned 2038 95 2017 run function mcm:game/markers/playerspawn
execute positioned 2038 95 2034 run function mcm:game/markers/playerspawn
execute positioned 2048 95 2045 run function mcm:game/markers/playerspawn
execute positioned 2072 95 2016 run function mcm:game/markers/playerspawn
execute positioned 2072 87 1994 run function mcm:game/markers/playerspawn
execute positioned 2066 87 2047 run function mcm:game/markers/playerspawn
execute positioned 2037 87 2015 run function mcm:game/markers/playerspawn
execute positioned 2037 87 2042 run function mcm:game/markers/playerspawn
execute positioned 2056 104 2033 run function mcm:game/markers/playerspawn

#> Set Scrap spawnpoints
execute positioned 1949 66 1925 run function mcm:game/markers/scrapspawn
execute positioned 1951 66 1999 run function mcm:game/markers/scrapspawn
execute positioned 2033 59 1953 run function mcm:game/markers/scrapspawn
execute positioned 1990 56 2080 run function mcm:game/markers/scrapspawn
execute positioned 1982 56 2019 run function mcm:game/markers/scrapspawn
execute positioned 2020 56 2049 run function mcm:game/markers/scrapspawn
execute positioned 2032 60 2011 run function mcm:game/markers/scrapspawn
execute positioned 2008 59 2000 run function mcm:game/markers/scrapspawn
execute positioned 1977 56 1983 run function mcm:game/markers/scrapspawn
execute positioned 1962 56 1953 run function mcm:game/markers/scrapspawn
execute positioned 2005 61 1979 run function mcm:game/markers/scrapspawn
execute positioned 2029 61 1967 run function mcm:game/markers/scrapspawn
execute positioned 2012 72 2026 run function mcm:game/markers/scrapspawn
execute positioned 2003 70 2084 run function mcm:game/markers/scrapspawn
execute positioned 2002 65 1921 run function mcm:game/markers/scrapspawn

#> Sound markers
execute positioned 1972 74 2034 run function mcm:game/markers/soundmarker
execute positioned 2046 75 1948 run function mcm:game/markers/soundmarker
execute positioned 2054 73 1925 run function mcm:game/markers/soundmarker
execute positioned 2047 73 1934 run function mcm:game/markers/soundmarker
execute positioned 1974 75 2002 run function mcm:game/markers/soundmarker
execute positioned 2086 76 1949 run function mcm:game/markers/soundmarker
execute positioned 1984 75 2001 run function mcm:game/markers/soundmarker
execute positioned 2066 73 1926 run function mcm:game/markers/soundmarker
execute positioned 2039 73 1931 run function mcm:game/markers/soundmarker
execute positioned 1972 74 2012 run function mcm:game/markers/soundmarker
execute positioned 2033 75 1944 run function mcm:game/markers/soundmarker
execute positioned 1997 78 2033 run function mcm:game/markers/soundmarker
execute positioned 1974 75 2053 run function mcm:game/markers/soundmarker
execute positioned 2052 76 1947 run function mcm:game/markers/soundmarker
execute positioned 1972 74 2044 run function mcm:game/markers/soundmarker
execute positioned 2033 72 1924 run function mcm:game/markers/soundmarker
execute positioned 2071 75 1939 run function mcm:game/markers/soundmarker
execute positioned 2057 74 1937 run function mcm:game/markers/soundmarker
execute positioned 1986 76 2036 run function mcm:game/markers/soundmarker
execute positioned 1993 77 2050 run function mcm:game/markers/soundmarker
execute positioned 1978 75 2028 run function mcm:game/markers/soundmarker
execute positioned 1993 77 2026 run function mcm:game/markers/soundmarker
execute positioned 1982 75 2010 run function mcm:game/markers/soundmarker
execute positioned 2082 74 1940 run function mcm:game/markers/soundmarker
execute positioned 1992 77 2007 run function mcm:game/markers/soundmarker
execute positioned 2065 75 1948 run function mcm:game/markers/soundmarker
execute positioned 2080 73 1925 run function mcm:game/markers/soundmarker
execute positioned 1981 75 2046 run function mcm:game/markers/soundmarker

#> Spectator spawnpoint
execute positioned 1983 96 1932 run function mcm:game/markers/spectatorspawn

#tellraw @a ["","\n",{"text":"| ","bold":true,"color":"dark_gray"},{"text":"The game is ready to play!","underlined":true,"color":"green"},"\n",{"text":"| ","bold":true,"color":"dark_gray"},"\n",{"text":"| ","bold":true,"color":"dark_gray"},{"text":"Selected Map: ","color":"gray"},{"text":"Vineyard","color":"dark_green","hoverEvent":{"action":"show_text","value":[{"text":"Well, ","color":"aqua"},{"text":"that","color":"aqua","italic":true},{"text":" aged like fine wine","color":"aqua"},{"text":"\n    --------","color":"dark_gray"},{"text":"\nConcept by: ","color":"dark_aqua"},{"text":"_topaz, Brownie1111","color":"green"}]}},"\n",{"text":"| ","bold":true,"color":"dark_gray"},{"text":"Directed by: ","color":"gray"},{"text":"_topaz, Brownie1111\n","color":"dark_green"}]

tellraw @a ["\n",{"text":"| ","bold":true,"color":"dark_gray"},{"translate":"mcm.map.ready","underlined":true,"color":"green","bold":false}]
tellraw @a [{"text":"| ","bold":true,"color":"dark_gray"}]
tellraw @a [{"text":"| ","bold":true,"color":"dark_gray"},{"translate":"mcm.map.selected","color":"gray","bold":false,"with":[{"translate":"mcm.vineyard.name","color":"dark_green","hoverEvent":{"action":"show_text","value":[{"translate":"mcm.vineyard.hover","color":"aqua", "italic":false, "with":[{"translate":"mcm.vineyard.hover.italic", "italic":true}]},{"text":"\n    --------\n","color":"dark_gray"},{"translate":"mcm.map.concept","color":"dark_aqua", "with":[ {"translate":"mcm.map.list.2", "color":"green", "with":[ "_topaz", "Brownie1111" ]} ]}]}}]}]
tellraw @a [{"text":"| ","bold":true,"color":"dark_gray"},{"translate":"mcm.map.directed","color":"gray","bold":false, "with":[ {"translate":"mcm.map.list.2", "color":"dark_green", "with": ["_topaz", "Brownie1111"]} ]}, "\n"]
