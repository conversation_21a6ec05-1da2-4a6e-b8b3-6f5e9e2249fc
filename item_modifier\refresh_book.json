[{"function": "set_book_cover", "author": "Bagel Buddies", "generation": 0, "title": "How to Play"}, {"function": "set_written_book_pages", "pages": [[{"translate": "mcm.book.table", "color": "dark_gray", "bold": true, "italic": false, "underlined": false}, "\n\n", {"translate": "mcm.book.table.what", "color": "dark_aqua", "bold": false, "italic": false, "underlined": true, "clickEvent": {"action": "change_page", "value": "2"}}, "\n", {"translate": "mcm.book.table.how", "color": "dark_aqua", "bold": false, "italic": false, "underlined": true, "clickEvent": {"action": "change_page", "value": "3"}}, "\n", {"translate": "mcm.book.table.options", "color": "dark_aqua", "bold": false, "italic": false, "underlined": true, "clickEvent": {"action": "change_page", "value": "17"}}, "\n", {"translate": "mcm.book.table.cosmetics", "color": "dark_aqua", "bold": false, "italic": false, "underlined": true, "clickEvent": {"action": "change_page", "value": "20"}}, "\n\n\n\n", {"translate": "mcm.book.table.cinema", "color": "dark_green", "bold": false, "italic": false, "underlined": true, "clickEvent": {"action": "run_command", "value": "/trigger stuck"}}], [{"translate": "mcm.book.what", "color": "dark_gray", "bold": true, "italic": false, "underlined": true}, "\n\n", {"translate": "mcm.book.what.contents", "color": "black", "bold": false, "italic": false, "underlined": false}], [{"translate": "mcm.book.roles", "color": "dark_gray", "bold": true, "italic": false, "underlined": true}, "\n\n", {"translate": "mcm.book.roles.innocent.contents", "color": "black", "bold": false, "italic": false, "underlined": false, "with": [{"translate": "mcm.book.roles.innocent", "color": "light_purple", "bold": false, "italic": false, "underlined": false}, {"translate": "mcm.book.roles.spyglass", "color": "gold", "bold": false, "italic": false, "underlined": false}, {"translate": "mcm.book.roles.scrap", "color": "gold", "bold": false, "italic": false, "underlined": false}, {"translate": "mcm.book.roles.innocent.murderer", "color": "red", "bold": false, "italic": false, "underlined": false}, {"translate": "mcm.book.roles.gun", "color": "gold", "bold": false, "italic": false, "underlined": false}]}, "\n"], [{"translate": "mcm.book.roles.gunner.contents", "color": "black", "bold": false, "italic": false, "underlined": false, "with": [{"translate": "mcm.book.roles.gunner", "color": "dark_aqua", "bold": false, "italic": false, "underlined": false}, {"translate": "mcm.book.roles.spyglass", "color": "gold", "bold": false, "italic": false, "underlined": false}, {"translate": "mcm.book.roles.gun", "color": "gold", "bold": false, "italic": false, "underlined": false}, {"translate": "mcm.book.roles.innocent.murderer", "color": "red", "bold": false, "italic": false, "underlined": false}, {"translate": "mcm.book.roles.gunner.contents.red", "color": "dark_red", "bold": false, "italic": false, "underlined": false}]}, "\n"], [{"translate": "mcm.book.roles", "color": "dark_gray", "bold": true, "italic": false, "underlined": true}, "\n\n", {"translate": "mcm.book.roles.murderer.contents", "color": "black", "bold": false, "italic": false, "underlined": false, "with": [{"translate": "mcm.book.roles.murderer", "color": "red", "bold": false, "italic": false, "underlined": false}, {"translate": "mcm.book.roles.spyglass", "color": "gold", "bold": false, "italic": false, "underlined": false}, {"translate": "mcm.book.roles.knife", "color": "gold", "bold": false, "italic": false, "underlined": false}, {"translate": "mcm.book.roles.murderer.contents.red", "color": "dark_red", "bold": false, "italic": false, "underlined": false}]}, "\n", {"translate": "mcm.book.roles.murderer.nextpage", "color": "dark_gray", "bold": false, "italic": false, "underlined": false}], [{"translate": "mcm.book.roles.murderer.tools", "color": "dark_gray", "bold": false, "italic": false, "underlined": false}, "\n\n", {"translate": "mcm.book.roles.playertracker.desc", "color": "black", "bold": false, "italic": false, "underlined": false, "with": [{"translate": "mcm.book.roles.playertracker", "color": "gold", "bold": false, "italic": false, "underlined": false}]}, "\n\n", {"translate": "mcm.book.roles.adrenaline.desc", "color": "black", "bold": false, "italic": false, "underlined": false, "with": [{"translate": "mcm.book.roles.adrenaline", "color": "gold", "bold": false, "italic": false, "underlined": false}, {"translate": "mcm.book.roles.adrenaline.desc.red", "color": "dark_red", "bold": false, "italic": false, "underlined": false}]}], [{"translate": "mcm.book.roles.playerteleporter.desc", "color": "black", "bold": false, "italic": false, "underlined": false, "with": [{"translate": "mcm.book.roles.playerteleporter", "color": "gold", "bold": false, "italic": false, "underlined": false}, {"translate": "mcm.book.roles.playerteleporter.desc.red", "color": "dark_red", "bold": false, "italic": false, "underlined": false}]}, "\n\n", {"translate": "mcm.book.roles.scrap.desc", "color": "black", "bold": false, "italic": false, "underlined": false, "with": [{"translate": "mcm.book.roles.scrap.big", "color": "gold", "bold": false, "italic": false, "underlined": false}, {"translate": "mcm.book.roles.scrap.desc.green", "color": "dark_green", "bold": false, "italic": false, "underlined": false}]}], [{"translate": "mcm.book.roles.kniferecall.desc", "color": "black", "bold": false, "italic": false, "underlined": false, "with": [{"translate": "mcm.book.roles.kniferecall", "color": "gold", "bold": false, "italic": false, "underlined": false}, {"translate": "mcm.book.roles.kniferecall.desc.red", "color": "dark_red", "bold": false, "italic": false, "underlined": false}]}, "\n"], [{"translate": "mcm.book.other", "color": "dark_gray", "bold": true, "italic": false, "underlined": false}, "\n\n", {"translate": "mcm.book.other.p1", "color": "black", "bold": false, "italic": false, "underlined": false}], [{"translate": "mcm.book.other.p2", "color": "black", "bold": false, "italic": false, "underlined": false, "with": [{"translate": "mcm.book.other.p2.red", "color": "dark_red", "bold": false, "italic": false, "underlined": false}, {"translate": "mcm.book.other.p2.blocks", "color": "dark_gray", "bold": false, "italic": false, "underlined": false}]}, "\n"], [{"translate": "mcm.book.voting", "color": "dark_gray", "bold": true, "italic": false, "underlined": false}, "\n\n", {"translate": "mcm.book.voting.desc", "color": "black", "bold": false, "italic": false, "underlined": false, "with": [{"translate": "mcm.book.voting.cinema", "color": "dark_purple", "bold": false, "italic": false, "underlined": false}]}], [{"translate": "mcm.book.voting.desc.p2", "color": "black", "bold": false, "italic": false, "underlined": false, "with": [{"translate": "mcm.book.voting.posters", "color": "dark_purple", "bold": false, "italic": false, "underlined": false}, {"translate": "mcm.book.voting.right", "color": "dark_purple", "bold": false, "italic": false, "underlined": false}]}], [{"translate": "mcm.book.voting.desc.p3", "color": "black", "bold": false, "italic": false, "underlined": false, "with": [{"translate": "mcm.book.voting.theater", "color": "dark_purple", "bold": false, "italic": false, "underlined": false}, {"translate": "mcm.book.voting.seat", "color": "dark_purple", "bold": false, "italic": false, "underlined": false}, {"translate": "mcm.book.voting.3d", "color": "dark_purple", "bold": false, "italic": false, "underlined": false}]}], [{"translate": "mcm.book.autoqueue.desc", "color": "black", "bold": false, "italic": false, "underlined": false, "with": [{"translate": "mcm.book.autoqueue.ticket", "color": "dark_purple", "bold": false, "italic": false, "underlined": false}, {"translate": "mcm.book.autoqueue", "color": "dark_purple", "bold": false, "italic": false, "underlined": false}]}], [{"translate": "mcm.book.autostart.desc", "color": "black", "bold": false, "italic": false, "underlined": false, "with": [{"translate": "mcm.book.autostart", "color": "light_purple", "bold": false, "italic": false, "underlined": false}, {"translate": "mcm.book.autostart.usher", "color": "dark_purple", "bold": false, "italic": false, "underlined": false}]}], [{"translate": "mcm.book.spectate.desc", "color": "black", "bold": false, "italic": false, "underlined": false, "with": [{"translate": "mcm.book.spectate", "color": "dark_purple", "bold": false, "italic": false, "underlined": false}, {"translate": "mcm.book.spectate.elevator", "color": "dark_purple", "bold": false, "italic": false, "underlined": false}, {"translate": "mcm.book.spectate.balcony", "color": "dark_purple", "bold": false, "italic": false, "underlined": false}]}], [{"translate": "mcm.book.options", "color": "dark_gray", "bold": true, "italic": false, "underlined": false}, "\n", {"translate": "mcm.book.options.autostart", "color": "black", "bold": false, "italic": false, "underlined": false, "with": [{"translate": "mcm.book.options.on", "color": "dark_green", "bold": false, "italic": false, "underlined": false, "clickEvent": {"action": "run_command", "value": "/trigger player_rule_update set 9"}}, {"translate": "mcm.book.options.off", "color": "dark_green", "bold": false, "italic": false, "underlined": false, "clickEvent": {"action": "run_command", "value": "/trigger player_rule_update set 10"}}]}, "\n\n", {"translate": "mcm.book.options.num.murderers", "color": "black", "bold": false, "italic": false, "underlined": false, "with": [{"translate": "mcm.book.options.decr", "color": "dark_green", "bold": false, "italic": false, "underlined": false, "clickEvent": {"action": "run_command", "value": "/trigger player_rule_update set 4"}}, {"score": {"objective": "GameRules", "name": "$murderers"}, "color": "dark_green", "bold": false, "italic": false, "underlined": false}, {"translate": "mcm.book.options.incr", "color": "dark_green", "bold": false, "italic": false, "underlined": false, "clickEvent": {"action": "run_command", "value": "/trigger player_rule_update set 3"}}, {"translate": "mcm.book.options.smart", "color": "dark_green", "bold": false, "italic": false, "underlined": false, "clickEvent": {"action": "run_command", "value": "/trigger player_rule_update set 11"}, "hoverEvent": {"action": "show_text", "value": {"translate": "mcm.book.options.smart.hover"}}}]}, "\n\n", {"translate": "mcm.book.options.round.length", "color": "black", "bold": false, "italic": false, "underlined": false}, "\n", {"translate": "mcm.book.options.round.len.min", "color": "dark_green", "bold": false, "italic": false, "underlined": false, "with": [{"translate": "mcm.book.options.decr", "color": "dark_green", "bold": false, "italic": false, "underlined": false, "clickEvent": {"action": "run_command", "value": "/trigger player_rule_update set 2"}}, {"score": {"objective": "GameRules", "name": "$roundtimer"}, "color": "dark_green", "bold": false, "italic": false, "underlined": false}, {"translate": "mcm.book.options.incr", "color": "dark_green", "bold": false, "italic": false, "underlined": false, "clickEvent": {"action": "run_command", "value": "/trigger player_rule_update set 1"}}]}, "\n\n", {"translate": "mcm.book.options.animations", "color": "black", "bold": false, "italic": false, "underlined": false}, "\n", {"translate": "mcm.book.options.onoff", "color": "dark_green", "bold": false, "italic": false, "underlined": false, "with": [{"translate": "mcm.book.options.on", "color": "dark_green", "bold": false, "italic": false, "underlined": false, "clickEvent": {"action": "run_command", "value": "/trigger player_rule_update set 7"}}, {"translate": "mcm.book.options.off", "color": "dark_green", "bold": false, "italic": false, "underlined": false, "clickEvent": {"action": "run_command", "value": "/trigger player_rule_update set 8"}}]}], [{"translate": "mcm.book.options.scrapstart", "color": "black", "bold": false, "italic": false, "underlined": false}, "\n", {"translate": "mcm.book.options.onoff", "color": "dark_green", "bold": false, "italic": false, "underlined": false, "with": [{"translate": "mcm.book.options.on", "color": "dark_green", "bold": false, "italic": false, "underlined": false, "clickEvent": {"action": "run_command", "value": "/trigger player_rule_update set 5"}}, {"translate": "mcm.book.options.off", "color": "dark_green", "bold": false, "italic": false, "underlined": false, "clickEvent": {"action": "run_command", "value": "/trigger player_rule_update set 6"}}]}, "\n\n", {"translate": "mcm.book.options.friendlyfire", "color": "black", "bold": false, "italic": false, "underlined": false}, "\n", {"translate": "mcm.book.options.onoff", "color": "dark_green", "bold": false, "italic": false, "underlined": false, "with": [{"translate": "mcm.book.options.on", "color": "dark_green", "bold": false, "italic": false, "underlined": false, "clickEvent": {"action": "run_command", "value": "/trigger player_rule_update set 12"}}, {"translate": "mcm.book.options.off", "color": "dark_green", "bold": false, "italic": false, "underlined": false, "clickEvent": {"action": "run_command", "value": "/trigger player_rule_update set 13"}}]}, "\n\n", {"translate": "mcm.book.options.tips", "color": "black", "bold": false, "italic": false, "underlined": false}, "\n", {"translate": "mcm.book.options.onoff", "color": "dark_green", "bold": false, "italic": false, "underlined": false, "with": [{"translate": "mcm.book.options.on", "color": "dark_green", "bold": false, "italic": false, "underlined": false, "clickEvent": {"action": "run_command", "value": "/trigger disableTips set 0"}}, {"translate": "mcm.book.options.off", "color": "dark_green", "bold": false, "italic": false, "underlined": false, "clickEvent": {"action": "run_command", "value": "/trigger disableTips set 1"}}]}, "\n\n", {"translate": "mcm.book.options.darkness", "color": "black", "bold": false, "italic": false, "underlined": false}, "\n", {"translate": "mcm.book.options.onoff", "color": "dark_green", "bold": false, "italic": false, "underlined": false, "with": [{"translate": "mcm.book.options.on", "color": "dark_green", "bold": false, "italic": false, "underlined": false, "clickEvent": {"action": "run_command", "value": "/trigger player_rule_update set 14"}}, {"translate": "mcm.book.options.off", "color": "dark_green", "bold": false, "italic": false, "underlined": false, "clickEvent": {"action": "run_command", "value": "/trigger player_rule_update set 15"}}]}], [{"translate": "mcm.book.options.updatebossbar", "color": "black", "bold": false, "italic": false, "underlined": false}, "\n", {"translate": "mcm.book.options.onoff", "color": "dark_green", "bold": false, "italic": false, "underlined": false, "with": [{"translate": "mcm.book.options.on", "color": "dark_green", "bold": false, "italic": false, "underlined": false, "clickEvent": {"action": "run_command", "value": "/trigger player_rule_update set 18"}}, {"translate": "mcm.book.options.off", "color": "dark_green", "bold": false, "italic": false, "underlined": false, "clickEvent": {"action": "run_command", "value": "/trigger player_rule_update set 19"}}]}, "\n\n", {"translate": "mcm.book.options.scrapstart", "color": "black", "bold": false, "italic": false, "underlined": false, "hoverEvent": {"action": "show_text", "value": {"translate": "mcm.book.options.destroyguns.hover"}}}, "\n", {"translate": "mcm.book.options.onoff", "color": "dark_green", "bold": false, "italic": false, "underlined": false, "with": [{"translate": "mcm.book.options.on", "color": "dark_green", "bold": false, "italic": false, "underlined": false, "clickEvent": {"action": "run_command", "value": "/trigger player_rule_update set 16"}}, {"translate": "mcm.book.options.off", "color": "dark_green", "bold": false, "italic": false, "underlined": false, "clickEvent": {"action": "run_command", "value": "/trigger player_rule_update set 17"}}]}], [{"translate": "mcm.book.cosmetics", "color": "dark_gray", "bold": true, "italic": false, "underlined": false}, "\n\n", {"translate": "mcm.book.cosmetics.equip", "color": "dark_green", "bold": false, "italic": false, "underlined": true, "clickEvent": {"action": "run_command", "value": "/trigger hatchange set 1"}, "with": [{"translate": "mcm.item.rainbow_hat"}]}, "\n", {"translate": "mcm.book.cosmetics.equip", "color": "dark_green", "bold": false, "italic": false, "underlined": true, "clickEvent": {"action": "run_command", "value": "/trigger hatchange set 2"}, "with": [{"translate": "mcm.item.lance_hat"}]}, "\n", {"translate": "mcm.book.cosmetics.equip", "color": "dark_green", "bold": false, "italic": false, "underlined": true, "clickEvent": {"action": "run_command", "value": "/trigger hatchange set 3"}, "with": [{"translate": "mcm.item.space_helmet"}]}, "\n", {"translate": "mcm.book.cosmetics.equip", "color": "dark_green", "bold": false, "italic": false, "underlined": true, "clickEvent": {"action": "run_command", "value": "/trigger hatchange set 4"}, "with": [{"translate": "mcm.item.dark3dglasses"}]}, "\n", {"translate": "mcm.book.cosmetics.equip", "color": "dark_green", "bold": false, "italic": false, "underlined": true, "clickEvent": {"action": "run_command", "value": "/trigger hatchange set 5"}, "with": [{"translate": "mcm.item.bell"}]}, "\n", {"translate": "mcm.book.cosmetics.equip", "color": "dark_green", "bold": false, "italic": false, "underlined": true, "clickEvent": {"action": "run_command", "value": "/trigger hatchange set 6"}, "with": [{"translate": "mcm.item.bowlerhat"}]}, "\n", {"translate": "mcm.book.cosmetics.equip", "color": "dark_green", "bold": false, "italic": false, "underlined": true, "clickEvent": {"action": "run_command", "value": "/trigger hatchange set 7"}, "with": [{"translate": "mcm.item.skipperhat"}]}, "\n", {"translate": "mcm.book.cosmetics.equip", "color": "dark_green", "bold": false, "italic": false, "underlined": true, "clickEvent": {"action": "run_command", "value": "/trigger hatchange set 8"}, "with": [{"translate": "mcm.item.aviatorcap"}]}, "\n", {"translate": "mcm.book.cosmetics.equip", "color": "dark_green", "bold": false, "italic": false, "underlined": true, "clickEvent": {"action": "run_command", "value": "/trigger hatchange set 9"}, "with": [{"translate": "mcm.item.fancy"}]}], [{"translate": "mcm.book.cosmetics.equip", "color": "dark_green", "bold": false, "italic": false, "underlined": true, "clickEvent": {"action": "run_command", "value": "/trigger hatchange set 10"}, "with": [{"translate": "mcm.item.miner_hat"}]}, "\n", {"translate": "mcm.book.cosmetics.equip", "color": "dark_green", "bold": false, "italic": false, "underlined": true, "clickEvent": {"action": "run_command", "value": "/trigger hatchange set 11"}, "with": [{"translate": "mcm.item.winter_hat"}]}, "\n", {"translate": "mcm.book.cosmetics.equip", "color": "dark_green", "bold": false, "italic": false, "underlined": true, "clickEvent": {"action": "run_command", "value": "/trigger hatchange set 12"}, "with": [{"translate": "mcm.item.hard_hat"}]}, "\n", {"translate": "mcm.book.cosmetics.equip", "color": "dark_green", "bold": false, "italic": false, "underlined": true, "clickEvent": {"action": "run_command", "value": "/trigger hatchange set 13"}, "with": [{"translate": "mcm.item.warden_ears"}]}, "\n\n", {"translate": "mcm.book.cosmetics.remove", "color": "dark_green", "bold": false, "italic": false, "underlined": true, "clickEvent": {"action": "run_command", "value": "/trigger hatchange set -1"}}]], "mode": "replace_all"}, {"function": "set_name", "name": {"translate": "mcm.book.title", "bold": true, "italic": false, "underlined": false, "color": "green"}}]