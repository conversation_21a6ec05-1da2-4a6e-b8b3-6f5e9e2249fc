function mcm:items/set_loadout_defaults

data merge storage mcm:args {slot:1,nbt:{murderer:1b}}
function mcm:items/give_loadout_item {item:knife}


data merge storage mcm:args {slot:2,nbt:{murderer:1b}}
function mcm:items/give_loadout_item {item:1}

data merge storage mcm:args {slot:3,nbt:{murderer:1b}}
function mcm:items/give_loadout_item {item:2}

data merge storage mcm:args {slot:4,nbt:{murderer:1b}}
function mcm:items/give_loadout_item {item:3}