execute as @e[type=block_display,tag=canyon_windmill,tag=1] run data merge entity @s {interpolation_duration:1,start_interpolation:0,transformation:[0.008660254f,0f,-0.4f,0.19566987f,0.0049726097f,-0.3135854f,0.689025f,-0.40971586f,-0.00052264234f,-2.9835656f,-0.07241944f,-0.5602421f,0f,0f,0f,1f] }
execute as @e[type=block_display,tag=canyon_windmill,tag=2] run data merge entity @s {interpolation_duration:1,start_interpolation:0,transformation:[0.008660254f,0f,-0.4f,0.19566987f,0.0045677274f,1.22021f,0.6329229f,-0.074703306f,0.0020336832f,-2.7406363f,0.2817954f,-0.69004184f,0f,0f,0f,1f] }
execute as @e[type=block_display,tag=canyon_windmill,tag=3] run data merge entity @s {interpolation_duration:1,start_interpolation:0,transformation:[0.008660254f,0f,-0.4f,0.19566987f,0.0029389262f,2.427051f,0.40722957f,0.28032595f,0.004045085f,-1.7633557f,0.5605034f,-0.6349454f,0f,0f,0f,1f] }
execute as @e[type=block_display,tag=canyon_windmill,tag=4] run data merge entity @s {interpolation_duration:1,start_interpolation:0,transformation:[0.008660254f,0f,-0.4f,0.19566987f,0.00052264234f,2.9835656f,0.07241944f,0.5602421f,0.0049726097f,-0.3135854f,0.689025f,-0.40971586f,0f,0f,0f,1f] }
execute as @e[type=block_display,tag=canyon_windmill,tag=5] run data merge entity @s {interpolation_duration:1,start_interpolation:0,transformation:[0.008660254f,0f,-0.4f,0.19566987f,-0.0020336832f,2.7406363f,-0.2817954f,0.69004184f,0.0045677274f,1.22021f,0.6329229f,-0.074703306f,0f,0f,0f,1f] }
execute as @e[type=block_display,tag=canyon_windmill,tag=6] run data merge entity @s {interpolation_duration:1,start_interpolation:0,transformation:[0.008660254f,0f,-0.4f,0.19566987f,-0.004045085f,1.7633557f,-0.5605034f,0.6349454f,0.0029389262f,2.427051f,0.40722957f,0.28032595f,0f,0f,0f,1f] }
execute as @e[type=block_display,tag=canyon_windmill,tag=7] run data merge entity @s {interpolation_duration:1,start_interpolation:0,transformation:[0.008660254f,0f,-0.4f,0.19566987f,-0.0049726097f,0.3135854f,-0.689025f,0.40971586f,0.00052264234f,2.9835656f,0.07241944f,0.5602421f,0f,0f,0f,1f] }
execute as @e[type=block_display,tag=canyon_windmill,tag=8] run data merge entity @s {interpolation_duration:1,start_interpolation:0,transformation:[0.008660254f,0f,-0.4f,0.19566987f,-0.0045677274f,-1.22021f,-0.6329229f,0.074703306f,-0.0020336832f,2.7406363f,-0.2817954f,0.69004184f,0f,0f,0f,1f] }
execute as @e[type=block_display,tag=canyon_windmill,tag=9] run data merge entity @s {interpolation_duration:1,start_interpolation:0,transformation:[0.008660254f,0f,-0.4f,0.19566987f,-0.0029389262f,-2.427051f,-0.40722957f,-0.28032595f,-0.004045085f,1.7633557f,-0.5605034f,0.6349454f,0f,0f,0f,1f] }
execute as @e[type=block_display,tag=canyon_windmill,tag=10] run data merge entity @s {interpolation_duration:1,start_interpolation:0,transformation:[0.008660254f,0f,-0.4f,0.19566987f,-0.00052264234f,-2.9835656f,-0.07241944f,-0.5602421f,-0.0049726097f,0.3135854f,-0.689025f,0.40971586f,0f,0f,0f,1f] }
execute as @e[type=block_display,tag=canyon_windmill,tag=11] run data merge entity @s {interpolation_duration:1,start_interpolation:0,transformation:[0.008660254f,0f,-0.4f,0.19566987f,0.0020336832f,-2.7406363f,0.2817954f,-0.69004184f,-0.0045677274f,-1.22021f,-0.6329229f,0.074703306f,0f,0f,0f,1f] }
execute as @e[type=block_display,tag=canyon_windmill,tag=12] run data merge entity @s {interpolation_duration:1,start_interpolation:0,transformation:[0.008660254f,0f,-0.4f,0.19566987f,0.004045085f,-1.7633557f,0.5605034f,-0.6349454f,-0.0029389262f,-2.427051f,-0.40722957f,-0.28032595f,0f,0f,0f,1f] }
