#> Kill potential leftover entities
kill @e[tag=MapEntity]

#> Set Player Spawnpoints
execute positioned 912 157 1077 run function mcm:game/markers/playerspawn
execute positioned 916 140 1104 run function mcm:game/markers/playerspawn
execute positioned 927 162 1104 run function mcm:game/markers/playerspawn
execute positioned 921 151 1135 run function mcm:game/markers/playerspawn
execute positioned 923 162 1125 run function mcm:game/markers/playerspawn
execute positioned 920 173 1131 run function mcm:game/markers/playerspawn
execute positioned 933 162 1079 run function mcm:game/markers/playerspawn
execute positioned 934 173 1080 run function mcm:game/markers/playerspawn
execute positioned 929 140 1095 run function mcm:game/markers/playerspawn
execute positioned 943 151 1093 run function mcm:game/markers/playerspawn
execute positioned 940 162 1092 run function mcm:game/markers/playerspawn
execute positioned 932 140 1123 run function mcm:game/markers/playerspawn
execute positioned 958 140 1085 run function mcm:game/markers/playerspawn
execute positioned 944 173 1072 run function mcm:game/markers/playerspawn
execute positioned 948 140 1109 run function mcm:game/markers/playerspawn
execute positioned 944 151 1115 run function mcm:game/markers/playerspawn
execute positioned 945 162 1130 run function mcm:game/markers/playerspawn
execute positioned 958 173 1133 run function mcm:game/markers/playerspawn
execute positioned 962 151 1084 run function mcm:game/markers/playerspawn
execute positioned 974 173 1072 run function mcm:game/markers/playerspawn
execute positioned 972 140 1091 run function mcm:game/markers/playerspawn
execute positioned 964 162 1090 run function mcm:game/markers/playerspawn
execute positioned 963 140 1118 run function mcm:game/markers/playerspawn
execute positioned 964 151 1110 run function mcm:game/markers/playerspawn
execute positioned 975 140 1124 run function mcm:game/markers/playerspawn
execute positioned 962 151 1124 run function mcm:game/markers/playerspawn
execute positioned 974 162 1129 run function mcm:game/markers/playerspawn
execute positioned 975 173 1127 run function mcm:game/markers/playerspawn
execute positioned 985 140 1119 run function mcm:game/markers/playerspawn

#> Scrap spawns
execute positioned 927 131 1098 run function mcm:game/markers/scrapspawn
execute positioned 940 131 1118 run function mcm:game/markers/scrapspawn
execute positioned 955 131 1084 run function mcm:game/markers/scrapspawn

#> Sound markers
execute positioned 924 142 1098 run function mcm:game/markers/soundmarker {tag:page}
execute positioned 913 174 1100 run function mcm:game/markers/soundmarker {tag:page}
execute positioned 925 178 1092 run function mcm:game/markers/soundmarker {tag:page}
execute positioned 918 174 1108 run function mcm:game/markers/soundmarker {tag:page}
execute positioned 927 176 1118 run function mcm:game/markers/soundmarker {tag:page}
execute positioned 925 174 1135 run function mcm:game/markers/soundmarker {tag:page}
execute positioned 922 176 1127 run function mcm:game/markers/soundmarker {tag:page}
execute positioned 934 140 1083 run function mcm:game/markers/soundmarker {tag:page}
execute positioned 931 165 1077 run function mcm:game/markers/soundmarker {tag:page}
execute positioned 939 165 1079 run function mcm:game/markers/soundmarker {tag:page}
execute positioned 933 167 1085 run function mcm:game/markers/soundmarker {tag:page}
execute positioned 939 177 1085 run function mcm:game/markers/soundmarker {tag:page}
execute positioned 934 142 1096 run function mcm:game/markers/soundmarker {tag:page}
execute positioned 937 153 1090 run function mcm:game/markers/soundmarker {tag:page}
execute positioned 931 163 1095 run function mcm:game/markers/soundmarker {tag:page}
execute positioned 931 164 1090 run function mcm:game/markers/soundmarker {tag:page}
execute positioned 942 164 1095 run function mcm:game/markers/soundmarker {tag:page}
execute positioned 941 166 1090 run function mcm:game/markers/soundmarker {tag:page}
execute positioned 932 142 1111 run function mcm:game/markers/soundmarker {tag:page}
execute positioned 938 142 1114 run function mcm:game/markers/soundmarker {tag:page}
execute positioned 942 154 1116 run function mcm:game/markers/soundmarker {tag:page}
execute positioned 938 154 1118 run function mcm:game/markers/soundmarker {tag:page}
execute positioned 942 164 1113 run function mcm:game/markers/soundmarker {tag:page}
execute positioned 942 164 1118 run function mcm:game/markers/soundmarker {tag:page}
execute positioned 932 165 1113 run function mcm:game/markers/soundmarker {tag:page}
execute positioned 933 166 1118 run function mcm:game/markers/soundmarker {tag:page}
execute positioned 932 142 1124 run function mcm:game/markers/soundmarker {tag:page}
execute positioned 942 164 1126 run function mcm:game/markers/soundmarker {tag:page}
execute positioned 936 164 1128 run function mcm:game/markers/soundmarker {tag:page}
execute positioned 935 179 1122 run function mcm:game/markers/soundmarker {tag:page}
execute positioned 945 164 1082 run function mcm:game/markers/soundmarker {tag:page}
execute positioned 944 178 1076 run function mcm:game/markers/soundmarker {tag:page}
execute positioned 946 143 1094 run function mcm:game/markers/soundmarker {tag:page}
execute positioned 958 143 1097 run function mcm:game/markers/soundmarker {tag:page}
execute positioned 950 144 1098 run function mcm:game/markers/soundmarker {tag:page}
execute positioned 946 153 1090 run function mcm:game/markers/soundmarker {tag:page}
execute positioned 946 143 1111 run function mcm:game/markers/soundmarker {tag:page}
execute positioned 958 143 1113 run function mcm:game/markers/soundmarker {tag:page}
execute positioned 950 143 1115 run function mcm:game/markers/soundmarker {tag:page}
execute positioned 950 154 1114 run function mcm:game/markers/soundmarker {tag:page}
execute positioned 955 141 1122 run function mcm:game/markers/soundmarker {tag:page}
execute positioned 950 164 1126 run function mcm:game/markers/soundmarker {tag:page}
execute positioned 947 176 1123 run function mcm:game/markers/soundmarker {tag:page}
execute positioned 965 176 1076 run function mcm:game/markers/soundmarker {tag:page}
execute positioned 973 177 1074 run function mcm:game/markers/soundmarker {tag:page}
execute positioned 961 178 1074 run function mcm:game/markers/soundmarker {tag:page}
execute positioned 962 141 1095 run function mcm:game/markers/soundmarker {tag:page}
execute positioned 962 144 1098 run function mcm:game/markers/soundmarker {tag:page}
execute positioned 960 154 1094 run function mcm:game/markers/soundmarker {tag:page}
execute positioned 969 154 1094 run function mcm:game/markers/soundmarker {tag:page}
execute positioned 967 141 1116 run function mcm:game/markers/soundmarker {tag:page}
execute positioned 962 154 1116 run function mcm:game/markers/soundmarker {tag:page}
execute positioned 969 154 1116 run function mcm:game/markers/soundmarker {tag:page}
execute positioned 962 163 1119 run function mcm:game/markers/soundmarker {tag:page}
execute positioned 960 141 1123 run function mcm:game/markers/soundmarker {tag:page}
execute positioned ************ run function mcm:game/markers/soundmarker {tag:page}
execute positioned ************ run function mcm:game/markers/soundmarker {tag:page}
execute positioned ************ run function mcm:game/markers/soundmarker {tag:page}
execute positioned ************ run function mcm:game/markers/soundmarker {tag:page}

#> Spectator spawnpoint
execute positioned ************ run function mcm:game/markers/spectatorspawn

#> Chandelier entity
summon marker ************ {Tags:["MapEntity","Chandelier"]}

tellraw @a ["\n",{"text":"| ","bold":true,"color":"dark_gray"},{"translate":"mcm.map.ready","underlined":true,"color":"green","bold":false}]
tellraw @a [{"text":"| ","bold":true,"color":"dark_gray"}]
tellraw @a [{"text":"| ","bold":true,"color":"dark_gray"},{"translate":"mcm.map.selected","color":"gray","bold":false,"with":[{"translate":"mcm.library.name","color":"dark_green","hoverEvent":{"action":"show_text","value":[{"translate":"mcm.library.hover","color":"aqua"},{"text":"\n    --------\n","color":"dark_gray"},{"translate":"mcm.map.concept","color":"dark_aqua", "with":[ {"translate":"mcm.map.list.1", "color":"green", "with":[ "_topaz" ]} ]}]}}]}]
tellraw @a [{"text":"| ","bold":true,"color":"dark_gray"},{"translate":"mcm.map.directed","color":"gray","bold":false, "with":[ {"translate":"mcm.map.list.1", "color":"dark_green", "with": ["_topaz"]} ]}, "\n"]

#> Spawn the books for basement stairs unlock
function mcm:maps/library2/spawn_books

#> Clear any values the player might somehow have leftover from previous games
scoreboard players reset @a flipbook
