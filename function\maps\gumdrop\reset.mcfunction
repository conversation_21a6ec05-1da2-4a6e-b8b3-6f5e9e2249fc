execute unless block 889 33 -3026 minecraft:cherry_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] run setblock 889 33 -3026 minecraft:cherry_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] replace
execute unless block 890 33 -3026 minecraft:pink_bed[facing=south,part=foot,occupied=true] run setblock 890 33 -3026 minecraft:pink_bed[facing=south,part=foot,occupied=true] replace
execute unless block 892 33 -3026 minecraft:pink_bed[facing=south,part=foot,occupied=true] run setblock 892 33 -3026 minecraft:pink_bed[facing=south,part=foot,occupied=true] replace
execute unless block 893 33 -3026 minecraft:cherry_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock 893 33 -3026 minecraft:cherry_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block 890 33 -3025 minecraft:pink_bed[facing=south,part=head,occupied=true] run setblock 890 33 -3025 minecraft:pink_bed[facing=south,part=head,occupied=true] replace
execute unless block 892 33 -3025 minecraft:pink_bed[facing=south,part=head,occupied=true] run setblock 892 33 -3025 minecraft:pink_bed[facing=south,part=head,occupied=true] replace
execute unless block 900 33 -3056 minecraft:red_bed[facing=west,part=head,occupied=true] run setblock 900 33 -3056 minecraft:red_bed[facing=west,part=head,occupied=true] replace
execute unless block 901 33 -3056 minecraft:red_bed[facing=west,part=foot,occupied=true] run setblock 901 33 -3056 minecraft:red_bed[facing=west,part=foot,occupied=true] replace
execute unless block 901 33 -3052 minecraft:mangrove_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock 901 33 -3052 minecraft:mangrove_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block 899 34 -3055 minecraft:potted_crimson_roots run setblock 899 34 -3055 minecraft:potted_crimson_roots replace
execute unless block 901 34 -3052 minecraft:mangrove_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock 901 34 -3052 minecraft:mangrove_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block 901 35 -3048 minecraft:mangrove_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] run setblock 901 35 -3048 minecraft:mangrove_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] replace
execute unless block 901 35 -3047 minecraft:mangrove_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] run setblock 901 35 -3047 minecraft:mangrove_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] replace
execute unless block 901 35 -3046 minecraft:mangrove_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] run setblock 901 35 -3046 minecraft:mangrove_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] replace
execute unless block 901 35 -3045 minecraft:mangrove_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] run setblock 901 35 -3045 minecraft:mangrove_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] replace
execute unless block 901 35 -3044 minecraft:mangrove_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] run setblock 901 35 -3044 minecraft:mangrove_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] replace
execute unless block 901 37 -3047 minecraft:mangrove_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] run setblock 901 37 -3047 minecraft:mangrove_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] replace
execute unless block 901 37 -3046 minecraft:mangrove_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] run setblock 901 37 -3046 minecraft:mangrove_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] replace
execute unless block 901 37 -3045 minecraft:mangrove_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] run setblock 901 37 -3045 minecraft:mangrove_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] replace
execute unless block 920 33 -3070 minecraft:magenta_bed[facing=east,part=foot,occupied=true] run setblock 920 33 -3070 minecraft:magenta_bed[facing=east,part=foot,occupied=true] replace
execute unless block 921 33 -3070 minecraft:magenta_bed[facing=east,part=head,occupied=true] run setblock 921 33 -3070 minecraft:magenta_bed[facing=east,part=head,occupied=true] replace
execute unless block 914 33 -3069 minecraft:crimson_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] run setblock 914 33 -3069 minecraft:crimson_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] replace
execute unless block 915 33 -3069 minecraft:crimson_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock 915 33 -3069 minecraft:crimson_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block 916 33 -3069 minecraft:crimson_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock 916 33 -3069 minecraft:crimson_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block 914 35 -3068 minecraft:crimson_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] run setblock 914 35 -3068 minecraft:crimson_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] replace
execute unless block 914 35 -3067 minecraft:crimson_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] run setblock 914 35 -3067 minecraft:crimson_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] replace
execute unless block 913 35 -3066 minecraft:crimson_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=bottom] run setblock 913 35 -3066 minecraft:crimson_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=bottom] replace
execute unless block 914 35 -3065 minecraft:crimson_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] run setblock 914 35 -3065 minecraft:crimson_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] replace
execute unless block 915 35 -3065 minecraft:crimson_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] run setblock 915 35 -3065 minecraft:crimson_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] replace
execute unless block 916 35 -3065 minecraft:crimson_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] run setblock 916 35 -3065 minecraft:crimson_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] replace
execute unless block 926 43 -3043 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock 926 43 -3043 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block 927 43 -3043 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock 927 43 -3043 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block 927 45 -3041 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] run setblock 927 45 -3041 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] replace
execute unless block 925 46 -3043 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] run setblock 925 46 -3043 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] replace
execute unless block 926 46 -3043 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] run setblock 926 46 -3043 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] replace
execute unless block 928 45 -3041 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] run setblock 928 45 -3041 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] replace
execute unless block 935 59 -3033 minecraft:acacia_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] run setblock 935 59 -3033 minecraft:acacia_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] replace
execute unless block 939 59 -3033 minecraft:acacia_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] run setblock 939 59 -3033 minecraft:acacia_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] replace
execute unless block 932 59 -3030 minecraft:acacia_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=bottom] run setblock 932 59 -3030 minecraft:acacia_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=bottom] replace
execute unless block 942 59 -3030 minecraft:acacia_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] run setblock 942 59 -3030 minecraft:acacia_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] replace
execute unless block 932 59 -3026 minecraft:acacia_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=bottom] run setblock 932 59 -3026 minecraft:acacia_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=bottom] replace
execute unless block 942 59 -3026 minecraft:acacia_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] run setblock 942 59 -3026 minecraft:acacia_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] replace
execute unless block 935 59 -3023 minecraft:acacia_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] run setblock 935 59 -3023 minecraft:acacia_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] replace
execute unless block 939 59 -3023 minecraft:acacia_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] run setblock 939 59 -3023 minecraft:acacia_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] replace
execute unless block 954 33 -3064 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock 954 33 -3064 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block 955 33 -3064 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock 955 33 -3064 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block 956 33 -3064 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock 956 33 -3064 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block 953 33 -3063 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] run setblock 953 33 -3063 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] replace
execute unless block 957 33 -3063 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock 957 33 -3063 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block 953 33 -3062 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] run setblock 953 33 -3062 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] replace
execute unless block 957 33 -3062 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock 957 33 -3062 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block 953 33 -3061 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] run setblock 953 33 -3061 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] replace
execute unless block 957 33 -3061 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock 957 33 -3061 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block 954 33 -3060 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock 954 33 -3060 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block 955 33 -3060 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock 955 33 -3060 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block 956 33 -3060 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock 956 33 -3060 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block 945 35 -3031 minecraft:warped_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock 945 35 -3031 minecraft:warped_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block 945 35 -3027 minecraft:warped_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock 945 35 -3027 minecraft:warped_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block 945 36 -3031 minecraft:warped_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock 945 36 -3031 minecraft:warped_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block 945 36 -3027 minecraft:warped_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock 945 36 -3027 minecraft:warped_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block 945 37 -3031 minecraft:warped_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock 945 37 -3031 minecraft:warped_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block 945 37 -3027 minecraft:warped_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock 945 37 -3027 minecraft:warped_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block 951 53 -3028 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] run setblock 951 53 -3028 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] replace
execute unless block 952 53 -3028 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] run setblock 952 53 -3028 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] replace
execute unless block 952 54 -3026 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=bottom] run setblock 952 54 -3026 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=bottom] replace
execute unless block 953 54 -3026 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=bottom] run setblock 953 54 -3026 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=bottom] replace
execute unless block 1007 30 -3066 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] run setblock 1007 30 -3066 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] replace
execute unless block 1015 30 -3083 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock 1015 30 -3083 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block 1016 30 -3083 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock 1016 30 -3083 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block 1012 30 -3082 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock 1012 30 -3082 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block 1014 30 -3082 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] run setblock 1014 30 -3082 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] replace
execute unless block 1017 30 -3082 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock 1017 30 -3082 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block 1014 30 -3081 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] run setblock 1014 30 -3081 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] replace
execute unless block 1017 30 -3081 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock 1017 30 -3081 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block 1012 30 -3080 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock 1012 30 -3080 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block 1015 30 -3080 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock 1015 30 -3080 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block 1016 30 -3080 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock 1016 30 -3080 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block 1023 30 -3080 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] run setblock 1023 30 -3080 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] replace
execute unless block 1014 30 -3079 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock 1014 30 -3079 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block 1023 30 -3079 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] run setblock 1023 30 -3079 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] replace
execute unless block 1023 30 -3078 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] run setblock 1023 30 -3078 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] replace
execute unless block 1014 30 -3077 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock 1014 30 -3077 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block 1023 30 -3077 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] run setblock 1023 30 -3077 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] replace
execute unless block 1012 30 -3076 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock 1012 30 -3076 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block 1011 30 -3075 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] run setblock 1011 30 -3075 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] replace
execute unless block 1013 30 -3075 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock 1013 30 -3075 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block 1012 30 -3074 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock 1012 30 -3074 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block 1023 30 -3074 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] run setblock 1023 30 -3074 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] replace
execute unless block 1014 31 -3082 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] run setblock 1014 31 -3082 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] replace
execute unless block 1013 31 -3081 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] run setblock 1013 31 -3081 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] replace
execute unless block 1023 31 -3078 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] run setblock 1023 31 -3078 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] replace
execute unless block 1023 31 -3077 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] run setblock 1023 31 -3077 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] replace
execute unless block 1015 32 -3080 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] run setblock 1015 32 -3080 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] replace
execute unless block 1014 32 -3079 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] run setblock 1014 32 -3079 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] replace
execute unless block 1016 33 -3078 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] run setblock 1016 33 -3078 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] replace
execute unless block 1015 33 -3077 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] run setblock 1015 33 -3077 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] replace
execute unless block 1014 34 -3082 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] run setblock 1014 34 -3082 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] replace
execute unless block 1013 34 -3081 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] run setblock 1013 34 -3081 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] replace
execute unless block 1015 35 -3079 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] run setblock 1015 35 -3079 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] replace
execute unless block 1014 35 -3078 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] run setblock 1014 35 -3078 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] replace
execute unless block 1018 36 -3079 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] run setblock 1018 36 -3079 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] replace
execute unless block 1017 36 -3078 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] run setblock 1017 36 -3078 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] replace
execute unless block 1008 30 -3067 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock 1008 30 -3067 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block 1009 30 -3066 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock 1009 30 -3066 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block 1011 30 -3066 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] run setblock 1011 30 -3066 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] replace
execute unless block 1013 30 -3066 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock 1013 30 -3066 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block 1008 30 -3065 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock 1008 30 -3065 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block 1010 30 -3064 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock 1010 30 -3064 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block 1010 30 -3062 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock 1010 30 -3062 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block 1012 30 -3061 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] run setblock 1012 30 -3061 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] replace
execute unless block 1014 30 -3061 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock 1014 30 -3061 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block 1012 32 -3063 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock 1012 32 -3063 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block 1013 32 -3063 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock 1013 32 -3063 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block 1013 33 -3066 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock 1013 33 -3066 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block 1014 33 -3066 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock 1014 33 -3066 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block 1010 34 -3063 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] run setblock 1010 34 -3063 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] replace
execute unless block 1011 34 -3063 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] run setblock 1011 34 -3063 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] replace
execute unless block 1013 35 -3064 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] run setblock 1013 35 -3064 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] replace
execute unless block 1014 35 -3064 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] run setblock 1014 35 -3064 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] replace
execute unless block 1012 36 -3067 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] run setblock 1012 36 -3067 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] replace
execute unless block 1013 36 -3067 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] run setblock 1013 36 -3067 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] replace
execute unless block 1027 30 -3080 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock 1027 30 -3080 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block 1027 30 -3079 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock 1027 30 -3079 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block 1025 30 -3078 minecraft:brown_bed[facing=north,part=head,occupied=true] run setblock 1025 30 -3078 minecraft:brown_bed[facing=north,part=head,occupied=true] replace
execute unless block 1027 30 -3078 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock 1027 30 -3078 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block 1025 30 -3077 minecraft:brown_bed[facing=north,part=foot,occupied=true] run setblock 1025 30 -3077 minecraft:brown_bed[facing=north,part=foot,occupied=true] replace
execute unless block 1027 30 -3077 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock 1027 30 -3077 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block 1025 30 -3074 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock 1025 30 -3074 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block 1027 31 -3078 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock 1027 31 -3078 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block 1024 32 -3076 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] run setblock 1024 32 -3076 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] replace
execute unless block 1025 32 -3076 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] run setblock 1025 32 -3076 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] replace
execute unless block 1025 33 -3074 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] run setblock 1025 33 -3074 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] replace
execute unless block 1026 33 -3074 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] run setblock 1026 33 -3074 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] replace
execute unless block 1025 34 -3077 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] run setblock 1025 34 -3077 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] replace
execute unless block 1026 34 -3077 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] run setblock 1026 34 -3077 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] replace
execute unless block 1024 35 -3075 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] run setblock 1024 35 -3075 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] replace
execute unless block 1025 35 -3075 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] run setblock 1025 35 -3075 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] replace
execute unless block 1026 36 -3073 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] run setblock 1026 36 -3073 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] replace
execute unless block 1027 36 -3073 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] run setblock 1027 36 -3073 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] replace
execute unless block 1028 30 -3054 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock 1028 30 -3054 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block 1028 30 -3052 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock 1028 30 -3052 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block 1032 30 -3052 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock 1032 30 -3052 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block 1028 30 -3051 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock 1028 30 -3051 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block 1032 30 -3050 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock 1032 30 -3050 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block 1028 30 -3049 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock 1028 30 -3049 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block 1028 30 -3046 minecraft:brown_bed[facing=east,part=foot,occupied=true] run setblock 1028 30 -3046 minecraft:brown_bed[facing=east,part=foot,occupied=true] replace
execute unless block 1029 30 -3046 minecraft:brown_bed[facing=east,part=head,occupied=true] run setblock 1029 30 -3046 minecraft:brown_bed[facing=east,part=head,occupied=true] replace
execute unless block 1028 31 -3051 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock 1028 31 -3051 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block 1028 31 -3049 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock 1028 31 -3049 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block 901 33 -3053 minecraft:mangrove_door[powered=false,open=false,facing=north,half=lower,hinge=left] run setblock 901 33 -3053 minecraft:mangrove_door[powered=false,open=false,facing=north,half=lower,hinge=left] destroy
execute unless block 902 33 -3053 minecraft:mangrove_door[powered=false,open=false,facing=north,half=lower,hinge=right] run setblock 902 33 -3053 minecraft:mangrove_door[powered=false,open=false,facing=north,half=lower,hinge=right] destroy
execute unless block 901 34 -3053 minecraft:mangrove_door[powered=false,open=false,facing=north,half=upper,hinge=left] run setblock 901 34 -3053 minecraft:mangrove_door[powered=false,open=false,facing=north,half=upper,hinge=left] destroy
execute unless block 902 34 -3053 minecraft:mangrove_door[powered=false,open=false,facing=north,half=upper,hinge=right] run setblock 902 34 -3053 minecraft:mangrove_door[powered=false,open=false,facing=north,half=upper,hinge=right] destroy
execute unless block 920 33 -3075 minecraft:crimson_door[powered=false,open=true,facing=south,half=lower,hinge=left] run setblock 920 33 -3075 minecraft:crimson_door[powered=false,open=true,facing=south,half=lower,hinge=left] destroy
execute unless block 920 34 -3075 minecraft:crimson_door[powered=false,open=true,facing=south,half=upper,hinge=left] run setblock 920 34 -3075 minecraft:crimson_door[powered=false,open=true,facing=south,half=upper,hinge=left] destroy
execute unless block 915 33 -3064 minecraft:crimson_door[powered=false,open=true,facing=north,half=lower,hinge=left] run setblock 915 33 -3064 minecraft:crimson_door[powered=false,open=true,facing=north,half=lower,hinge=left] destroy
execute unless block 915 34 -3064 minecraft:crimson_door[powered=false,open=true,facing=north,half=upper,hinge=left] run setblock 915 34 -3064 minecraft:crimson_door[powered=false,open=true,facing=north,half=upper,hinge=left] destroy

forceload remove 867 -2907 1087 -2864
forceload remove 915 -3057 1136 -2864
