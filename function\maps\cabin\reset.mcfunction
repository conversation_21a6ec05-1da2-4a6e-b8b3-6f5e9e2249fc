#> Rain sucks
weather clear

scoreboard players set @a cabin_secret 0

kill @e[type=item_display,tag=cabin_flint]
kill @e[type=interaction,tag=cabin_flint]
summon item_display -1987 104 3014 {item:{id:flint_and_steel,Count:1}, transformation:[0.53033006f,-0.53033006f,0f,-0.25f,0.45927933f,0.45927933f,-0.375f,0.4f,0.26516503f,0.26516503f,0.649519f,0.3f,0f,0f,0f,1f],Tags:["cabin_flint"]}
summon interaction -1986.7 104 3014.75 {width:0.5,height:1,Tags:["cabin_flint"]}

scoreboard players set $generator cabin_secret 0
scoreboard players set $button_timer_1 cabin_secret 0
scoreboard players set $button_timer_2 cabin_secret 0
scoreboard players set $button_timer_3 cabin_secret 0
scoreboard players set $button_timer_4 cabin_secret 0
scoreboard players set $button_timer_5 cabin_secret 0
scoreboard players set $button_timer_6 cabin_secret 0

#> Blocks
setblock -2022 97 3032 minecraft:orange_candle[candles=4,lit=false] replace
setblock -2020 95 3036 minecraft:blast_furnace[lit=false,facing=east]{Lock:"key"}
setblock -2020 95 3029 minecraft:blast_furnace[lit=false,facing=east]{Lock:"key"}

execute unless block -2030 103 3007 minecraft:dark_oak_fence_gate[powered=false,open=true,facing=north,in_wall=false] run setblock -2030 103 3007 minecraft:dark_oak_fence_gate[powered=false,open=true,facing=north,in_wall=false] replace
execute unless block -2029 112 3006 minecraft:dark_oak_fence_gate[powered=false,open=true,facing=south,in_wall=false] run setblock -2029 112 3006 minecraft:dark_oak_fence_gate[powered=false,open=true,facing=south,in_wall=false] replace
execute unless block -1983 110 3043 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] run setblock -1983 110 3043 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] replace
execute unless block -1983 110 3044 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] run setblock -1983 110 3044 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] replace
execute unless block -2024 111 3043 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] run setblock -2024 111 3043 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] replace
execute unless block -1970 100 3001 minecraft:dark_oak_fence_gate[powered=false,open=true,facing=south,in_wall=false] run setblock -1970 100 3001 minecraft:dark_oak_fence_gate[powered=false,open=true,facing=south,in_wall=false] replace
execute unless block -1971 102 3000 minecraft:dark_oak_fence_gate[powered=false,open=false,facing=west,in_wall=true] run setblock -1971 102 3000 minecraft:dark_oak_fence_gate[powered=false,open=false,facing=west,in_wall=true] replace
execute unless block -1975 105 3000 minecraft:dark_oak_fence_gate[powered=false,open=false,facing=north,in_wall=false] run setblock -1975 105 3000 minecraft:dark_oak_fence_gate[powered=false,open=false,facing=north,in_wall=false] replace
execute unless block -1971 109 3002 minecraft:dark_oak_fence_gate[powered=false,open=true,facing=north,in_wall=false] run setblock -1971 109 3002 minecraft:dark_oak_fence_gate[powered=false,open=true,facing=north,in_wall=false] replace
execute unless block -1967 94 3031 minecraft:iron_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] run setblock -1967 94 3031 minecraft:iron_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] replace
execute unless block -1967 94 3032 minecraft:iron_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] run setblock -1967 94 3032 minecraft:iron_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] replace
execute unless block -1967 96 3031 minecraft:iron_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] run setblock -1967 96 3031 minecraft:iron_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] replace
execute unless block -1967 96 3032 minecraft:iron_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] run setblock -1967 96 3032 minecraft:iron_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] replace
execute unless block -1967 98 3031 minecraft:light[level=4,waterlogged=false] run setblock -1967 98 3031 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1967 98 3036 minecraft:light[level=4,waterlogged=false] run setblock -1967 98 3036 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1968 104 3024 minecraft:light[level=4,waterlogged=false] run setblock -1968 104 3024 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1962 104 3024 minecraft:light[level=4,waterlogged=false] run setblock -1962 104 3024 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1956 104 3024 minecraft:light[level=4,waterlogged=false] run setblock -1956 104 3024 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1959 104 3027 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] run setblock -1959 104 3027 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] replace
execute unless block -1957 104 3027 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] run setblock -1957 104 3027 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] replace
execute unless block -1959 104 3028 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] run setblock -1959 104 3028 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] replace
execute unless block -1957 104 3028 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] run setblock -1957 104 3028 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] replace
execute unless block -1955 104 3034 minecraft:light[level=4,waterlogged=false] run setblock -1955 104 3034 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1953 104 3034 minecraft:light[level=4,waterlogged=false] run setblock -1953 104 3034 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1960 105 3029 minecraft:lever[powered=false,facing=west,face=wall] run setblock -1960 105 3029 minecraft:lever[powered=false,facing=west,face=wall] replace
execute unless block -1956 105 3029 minecraft:lever[powered=false,facing=east,face=wall] run setblock -1956 105 3029 minecraft:lever[powered=false,facing=east,face=wall] replace
execute unless block -1961 105 3034 minecraft:light[level=4,waterlogged=false] run setblock -1961 105 3034 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1958 106 3025 minecraft:oak_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=bottom] run setblock -1958 106 3025 minecraft:oak_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=bottom] replace
execute unless block -1959 107 3026 minecraft:iron_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] run setblock -1959 107 3026 minecraft:iron_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1957 107 3026 minecraft:iron_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=bottom] run setblock -1957 107 3026 minecraft:iron_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=bottom] replace
execute unless block -1959 107 3027 minecraft:iron_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] run setblock -1959 107 3027 minecraft:iron_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1957 107 3027 minecraft:iron_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] run setblock -1957 107 3027 minecraft:iron_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1959 107 3028 minecraft:iron_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] run setblock -1959 107 3028 minecraft:iron_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1957 107 3028 minecraft:iron_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] run setblock -1957 107 3028 minecraft:iron_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1963 108 3034 minecraft:light[level=4,waterlogged=false] run setblock -1963 108 3034 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1961 108 3034 minecraft:light[level=4,waterlogged=false] run setblock -1961 108 3034 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1955 108 3034 minecraft:light[level=4,waterlogged=false] run setblock -1955 108 3034 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1953 108 3034 minecraft:light[level=4,waterlogged=false] run setblock -1953 108 3034 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1963 109 3029 minecraft:spruce_fence_gate[powered=false,open=false,facing=west,in_wall=false] run setblock -1963 109 3029 minecraft:spruce_fence_gate[powered=false,open=false,facing=west,in_wall=false] replace
execute unless block -1961 109 3029 minecraft:spruce_fence_gate[powered=false,open=false,facing=west,in_wall=false] run setblock -1961 109 3029 minecraft:spruce_fence_gate[powered=false,open=false,facing=west,in_wall=false] replace
execute unless block -1955 109 3029 minecraft:spruce_fence_gate[powered=false,open=false,facing=west,in_wall=false] run setblock -1955 109 3029 minecraft:spruce_fence_gate[powered=false,open=false,facing=west,in_wall=false] replace
execute unless block -1953 109 3029 minecraft:spruce_fence_gate[powered=false,open=false,facing=west,in_wall=false] run setblock -1953 109 3029 minecraft:spruce_fence_gate[powered=false,open=false,facing=west,in_wall=false] replace
execute unless block -1963 109 3030 minecraft:spruce_fence_gate[powered=false,open=false,facing=west,in_wall=false] run setblock -1963 109 3030 minecraft:spruce_fence_gate[powered=false,open=false,facing=west,in_wall=false] replace
execute unless block -1961 109 3030 minecraft:spruce_fence_gate[powered=false,open=false,facing=west,in_wall=false] run setblock -1961 109 3030 minecraft:spruce_fence_gate[powered=false,open=false,facing=west,in_wall=false] replace
execute unless block -1955 109 3030 minecraft:spruce_fence_gate[powered=false,open=false,facing=west,in_wall=false] run setblock -1955 109 3030 minecraft:spruce_fence_gate[powered=false,open=false,facing=west,in_wall=false] replace
execute unless block -1953 109 3030 minecraft:spruce_fence_gate[powered=false,open=false,facing=west,in_wall=false] run setblock -1953 109 3030 minecraft:spruce_fence_gate[powered=false,open=false,facing=west,in_wall=false] replace
execute unless block -1963 109 3031 minecraft:spruce_fence_gate[powered=false,open=false,facing=west,in_wall=false] run setblock -1963 109 3031 minecraft:spruce_fence_gate[powered=false,open=false,facing=west,in_wall=false] replace
execute unless block -1961 109 3031 minecraft:spruce_fence_gate[powered=false,open=false,facing=west,in_wall=false] run setblock -1961 109 3031 minecraft:spruce_fence_gate[powered=false,open=false,facing=west,in_wall=false] replace
execute unless block -1955 109 3031 minecraft:spruce_fence_gate[powered=false,open=false,facing=west,in_wall=false] run setblock -1955 109 3031 minecraft:spruce_fence_gate[powered=false,open=false,facing=west,in_wall=false] replace
execute unless block -1953 109 3031 minecraft:spruce_fence_gate[powered=false,open=false,facing=west,in_wall=false] run setblock -1953 109 3031 minecraft:spruce_fence_gate[powered=false,open=false,facing=west,in_wall=false] replace
execute unless block -1963 109 3032 minecraft:spruce_fence_gate[powered=false,open=false,facing=west,in_wall=false] run setblock -1963 109 3032 minecraft:spruce_fence_gate[powered=false,open=false,facing=west,in_wall=false] replace
execute unless block -1961 109 3032 minecraft:spruce_fence_gate[powered=false,open=false,facing=west,in_wall=false] run setblock -1961 109 3032 minecraft:spruce_fence_gate[powered=false,open=false,facing=west,in_wall=false] replace
execute unless block -1955 109 3032 minecraft:spruce_fence_gate[powered=false,open=false,facing=west,in_wall=false] run setblock -1955 109 3032 minecraft:spruce_fence_gate[powered=false,open=false,facing=west,in_wall=false] replace
execute unless block -1953 109 3032 minecraft:spruce_fence_gate[powered=false,open=false,facing=west,in_wall=false] run setblock -1953 109 3032 minecraft:spruce_fence_gate[powered=false,open=false,facing=west,in_wall=false] replace
execute unless block -1963 109 3033 minecraft:spruce_fence_gate[powered=false,open=false,facing=west,in_wall=false] run setblock -1963 109 3033 minecraft:spruce_fence_gate[powered=false,open=false,facing=west,in_wall=false] replace
execute unless block -1961 109 3033 minecraft:spruce_fence_gate[powered=false,open=false,facing=west,in_wall=false] run setblock -1961 109 3033 minecraft:spruce_fence_gate[powered=false,open=false,facing=west,in_wall=false] replace
execute unless block -1955 109 3033 minecraft:spruce_fence_gate[powered=false,open=false,facing=west,in_wall=false] run setblock -1955 109 3033 minecraft:spruce_fence_gate[powered=false,open=false,facing=west,in_wall=false] replace
execute unless block -1953 109 3033 minecraft:spruce_fence_gate[powered=false,open=false,facing=west,in_wall=false] run setblock -1953 109 3033 minecraft:spruce_fence_gate[powered=false,open=false,facing=west,in_wall=false] replace
execute unless block -1968 109 3035 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] run setblock -1968 109 3035 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1967 109 3035 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] run setblock -1967 109 3035 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1966 109 3035 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] run setblock -1966 109 3035 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1965 109 3035 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] run setblock -1965 109 3035 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1964 109 3035 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] run setblock -1964 109 3035 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1963 109 3035 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] run setblock -1963 109 3035 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1961 109 3035 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] run setblock -1961 109 3035 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1960 109 3035 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] run setblock -1960 109 3035 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1959 109 3035 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] run setblock -1959 109 3035 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1958 109 3035 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] run setblock -1958 109 3035 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1957 109 3035 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] run setblock -1957 109 3035 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1956 109 3035 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] run setblock -1956 109 3035 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1955 109 3035 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] run setblock -1955 109 3035 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1953 109 3035 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] run setblock -1953 109 3035 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1968 112 3024 minecraft:light[level=4,waterlogged=false] run setblock -1968 112 3024 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1962 112 3024 minecraft:light[level=4,waterlogged=false] run setblock -1962 112 3024 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1956 112 3024 minecraft:light[level=4,waterlogged=false] run setblock -1956 112 3024 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1963 112 3034 minecraft:light[level=4,waterlogged=false] run setblock -1963 112 3034 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1961 112 3034 minecraft:light[level=4,waterlogged=false] run setblock -1961 112 3034 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1955 112 3034 minecraft:light[level=4,waterlogged=false] run setblock -1955 112 3034 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1953 112 3034 minecraft:light[level=4,waterlogged=false] run setblock -1953 112 3034 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1964 116 3024 minecraft:light[level=4,waterlogged=false] run setblock -1964 116 3024 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1956 116 3024 minecraft:light[level=4,waterlogged=false] run setblock -1956 116 3024 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1964 116 3034 minecraft:light[level=4,waterlogged=false] run setblock -1964 116 3034 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1940 109 3009 minecraft:dark_oak_fence_gate[powered=false,open=true,facing=south,in_wall=false] run setblock -1940 109 3009 minecraft:dark_oak_fence_gate[powered=false,open=true,facing=south,in_wall=false] replace
execute unless block -1941 111 3008 minecraft:dark_oak_fence_gate[powered=false,open=false,facing=west,in_wall=true] run setblock -1941 111 3008 minecraft:dark_oak_fence_gate[powered=false,open=false,facing=west,in_wall=true] replace
execute unless block -1945 114 3008 minecraft:dark_oak_fence_gate[powered=false,open=false,facing=north,in_wall=false] run setblock -1945 114 3008 minecraft:dark_oak_fence_gate[powered=false,open=false,facing=north,in_wall=false] replace
execute unless block -1941 118 3010 minecraft:dark_oak_fence_gate[powered=false,open=true,facing=north,in_wall=false] run setblock -1941 118 3010 minecraft:dark_oak_fence_gate[powered=false,open=true,facing=north,in_wall=false] replace
execute unless block -1953 98 3000 minecraft:dark_oak_fence_gate[powered=false,open=false,facing=south,in_wall=false] run setblock -1953 98 3000 minecraft:dark_oak_fence_gate[powered=false,open=false,facing=south,in_wall=false] replace
execute unless block -1967 98 3003 minecraft:dark_oak_fence_gate[powered=false,open=false,facing=south,in_wall=false] run setblock -1967 98 3003 minecraft:dark_oak_fence_gate[powered=false,open=false,facing=south,in_wall=false] replace
execute unless block -2008 100 3004 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -2008 100 3004 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -2007 100 3004 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -2007 100 3004 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -2006 100 3004 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -2006 100 3004 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -2005 100 3004 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -2005 100 3004 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -2004 100 3004 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -2004 100 3004 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -2003 100 3004 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -2003 100 3004 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -2002 100 3004 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -2002 100 3004 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -2001 100 3004 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -2001 100 3004 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -2006 100 3005 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] run setblock -2006 100 3005 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] replace
execute unless block -2004 100 3005 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] run setblock -2004 100 3005 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] replace
execute unless block -2002 100 3005 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] run setblock -2002 100 3005 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] replace
execute unless block -2007 102 3004 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] run setblock -2007 102 3004 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2007 102 3005 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] run setblock -2007 102 3005 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2019 94 3017 minecraft:light[level=4,waterlogged=false] run setblock -2019 94 3017 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2017 94 3017 minecraft:iron_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -2017 94 3017 minecraft:iron_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2017 94 3018 minecraft:iron_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -2017 94 3018 minecraft:iron_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2017 95 3017 minecraft:iron_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -2017 95 3017 minecraft:iron_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2017 95 3018 minecraft:iron_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -2017 95 3018 minecraft:iron_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2018 95 3023 minecraft:potted_bamboo run setblock -2018 95 3023 minecraft:potted_bamboo replace
execute unless block -2017 96 3017 minecraft:iron_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -2017 96 3017 minecraft:iron_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2017 96 3018 minecraft:iron_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -2017 96 3018 minecraft:iron_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2017 97 3017 minecraft:iron_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -2017 97 3017 minecraft:iron_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2017 97 3018 minecraft:iron_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -2017 97 3018 minecraft:iron_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2021 98 3014 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -2021 98 3014 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -2020 98 3014 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -2020 98 3014 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -2019 98 3014 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -2019 98 3014 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -2022 99 3017 minecraft:light[level=4,waterlogged=false] run setblock -2022 99 3017 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2021 99 3017 minecraft:light[level=4,waterlogged=false] run setblock -2021 99 3017 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2019 100 3018 minecraft:light[level=4,waterlogged=false] run setblock -2019 100 3018 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2023 103 3014 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] run setblock -2023 103 3014 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] replace
execute unless block -2022 103 3014 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] run setblock -2022 103 3014 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] replace
execute unless block -2021 103 3014 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] run setblock -2021 103 3014 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] replace
execute unless block -2020 103 3014 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] run setblock -2020 103 3014 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] replace
execute unless block -2019 103 3014 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] run setblock -2019 103 3014 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] replace
execute unless block -2018 103 3014 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] run setblock -2018 103 3014 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] replace
execute unless block -2020 104 3018 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock -2020 104 3018 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block -2020 104 3019 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock -2020 104 3019 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block -2020 104 3020 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock -2020 104 3020 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block -2029 105 3008 minecraft:dark_oak_fence_gate[powered=false,open=false,facing=east,in_wall=true] run setblock -2029 105 3008 minecraft:dark_oak_fence_gate[powered=false,open=false,facing=east,in_wall=true] replace
execute unless block -2024 105 3017 minecraft:light[level=4,waterlogged=false] run setblock -2024 105 3017 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2024 105 3021 minecraft:light[level=4,waterlogged=false] run setblock -2024 105 3021 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2024 107 3017 minecraft:mangrove_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] run setblock -2024 107 3017 minecraft:mangrove_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] replace
execute unless block -2024 107 3021 minecraft:mangrove_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] run setblock -2024 107 3021 minecraft:mangrove_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] replace
execute unless block -2025 108 3008 minecraft:dark_oak_fence_gate[powered=false,open=false,facing=south,in_wall=false] run setblock -2025 108 3008 minecraft:dark_oak_fence_gate[powered=false,open=false,facing=south,in_wall=false] replace
execute unless block -2024 108 3017 minecraft:mangrove_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock -2024 108 3017 minecraft:mangrove_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block -2024 108 3021 minecraft:mangrove_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock -2024 108 3021 minecraft:mangrove_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block -2024 109 3017 minecraft:mangrove_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] run setblock -2024 109 3017 minecraft:mangrove_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] replace
execute unless block -2024 109 3021 minecraft:mangrove_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] run setblock -2024 109 3021 minecraft:mangrove_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] replace
execute unless block -2028 110 3011 minecraft:dark_oak_fence_gate[powered=false,open=false,facing=west,in_wall=false] run setblock -2028 110 3011 minecraft:dark_oak_fence_gate[powered=false,open=false,facing=west,in_wall=false] replace
execute unless block -2024 110 3017 minecraft:mangrove_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock -2024 110 3017 minecraft:mangrove_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block -2024 110 3021 minecraft:mangrove_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock -2024 110 3021 minecraft:mangrove_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block -2029 111 3009 minecraft:dark_oak_fence_gate[powered=false,open=false,facing=west,in_wall=true] run setblock -2029 111 3009 minecraft:dark_oak_fence_gate[powered=false,open=false,facing=west,in_wall=true] replace
execute unless block -2023 111 3018 minecraft:light[level=4,waterlogged=false] run setblock -2023 111 3018 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2022 111 3018 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] run setblock -2022 111 3018 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] replace
execute unless block -2019 111 3018 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] run setblock -2019 111 3018 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] replace
execute unless block -2018 111 3018 minecraft:light[level=4,waterlogged=false] run setblock -2018 111 3018 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2022 111 3019 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] run setblock -2022 111 3019 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] replace
execute unless block -2019 111 3019 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] run setblock -2019 111 3019 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] replace
execute unless block -2023 111 3020 minecraft:light[level=4,waterlogged=false] run setblock -2023 111 3020 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2022 111 3020 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] run setblock -2022 111 3020 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] replace
execute unless block -2019 111 3020 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] run setblock -2019 111 3020 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] replace
execute unless block -2018 111 3020 minecraft:light[level=4,waterlogged=false] run setblock -2018 111 3020 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2024 112 3013 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] run setblock -2024 112 3013 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] replace
execute unless block -2023 112 3013 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] run setblock -2023 112 3013 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] replace
execute unless block -2022 112 3013 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] run setblock -2022 112 3013 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] replace
execute unless block -2021 112 3013 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] run setblock -2021 112 3013 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] replace
execute unless block -2020 112 3013 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] run setblock -2020 112 3013 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] replace
execute unless block -2019 112 3013 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] run setblock -2019 112 3013 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] replace
execute unless block -2018 112 3013 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] run setblock -2018 112 3013 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] replace
execute unless block -2017 112 3013 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] run setblock -2017 112 3013 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] replace
execute unless block -2024 112 3014 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] run setblock -2024 112 3014 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] replace
execute unless block -2023 112 3014 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] run setblock -2023 112 3014 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] replace
execute unless block -2022 112 3014 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] run setblock -2022 112 3014 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] replace
execute unless block -2021 112 3014 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] run setblock -2021 112 3014 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] replace
execute unless block -2020 112 3014 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] run setblock -2020 112 3014 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] replace
execute unless block -2019 112 3014 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] run setblock -2019 112 3014 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] replace
execute unless block -2018 112 3014 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] run setblock -2018 112 3014 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] replace
execute unless block -2017 112 3014 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] run setblock -2017 112 3014 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] replace
execute unless block -2024 113 3012 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] run setblock -2024 113 3012 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] replace
execute unless block -2023 113 3012 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock -2023 113 3012 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block -2022 113 3012 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] run setblock -2022 113 3012 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] replace
execute unless block -2021 113 3012 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock -2021 113 3012 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block -2020 113 3012 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] run setblock -2020 113 3012 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] replace
execute unless block -2019 113 3012 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock -2019 113 3012 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block -2018 113 3012 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] run setblock -2018 113 3012 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] replace
execute unless block -2017 113 3012 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock -2017 113 3012 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block -2025 113 3013 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -2025 113 3013 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2025 113 3014 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -2025 113 3014 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2024 114 3012 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] run setblock -2024 114 3012 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] replace
execute unless block -2023 114 3012 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] run setblock -2023 114 3012 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] replace
execute unless block -2022 114 3012 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] run setblock -2022 114 3012 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] replace
execute unless block -2021 114 3012 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] run setblock -2021 114 3012 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] replace
execute unless block -2020 114 3012 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] run setblock -2020 114 3012 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] replace
execute unless block -2019 114 3012 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] run setblock -2019 114 3012 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] replace
execute unless block -2018 114 3012 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] run setblock -2018 114 3012 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] replace
execute unless block -2017 114 3012 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] run setblock -2017 114 3012 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] replace
execute unless block -2025 114 3013 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] run setblock -2025 114 3013 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] replace
execute unless block -2025 114 3014 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=top] run setblock -2025 114 3014 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=top] replace
execute unless block -2024 114 3017 minecraft:light[level=4,waterlogged=false] run setblock -2024 114 3017 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2020 114 3017 minecraft:light[level=4,waterlogged=false] run setblock -2020 114 3017 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2018 114 3019 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -2018 114 3019 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2017 114 3019 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -2017 114 3019 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2024 114 3021 minecraft:light[level=4,waterlogged=false] run setblock -2024 114 3021 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2024 115 3012 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] run setblock -2024 115 3012 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] replace
execute unless block -2023 115 3012 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] run setblock -2023 115 3012 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] replace
execute unless block -2022 115 3012 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] run setblock -2022 115 3012 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] replace
execute unless block -2021 115 3012 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] run setblock -2021 115 3012 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] replace
execute unless block -2020 115 3012 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] run setblock -2020 115 3012 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] replace
execute unless block -2019 115 3012 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] run setblock -2019 115 3012 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] replace
execute unless block -2018 115 3012 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] run setblock -2018 115 3012 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] replace
execute unless block -2017 115 3012 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] run setblock -2017 115 3012 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] replace
execute unless block -2025 115 3013 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] run setblock -2025 115 3013 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2025 115 3014 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] run setblock -2025 115 3014 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2019 115 3020 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] run setblock -2019 115 3020 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2019 115 3023 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] run setblock -2019 115 3023 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2026 119 3009 minecraft:dark_oak_fence_gate[powered=false,open=true,facing=west,in_wall=false] run setblock -2026 119 3009 minecraft:dark_oak_fence_gate[powered=false,open=true,facing=west,in_wall=false] replace
execute unless block -2024 119 3017 minecraft:potted_lily_of_the_valley run setblock -2024 119 3017 minecraft:potted_lily_of_the_valley replace
execute unless block -2027 121 3010 minecraft:dark_oak_fence_gate[powered=false,open=false,facing=east,in_wall=true] run setblock -2027 121 3010 minecraft:dark_oak_fence_gate[powered=false,open=false,facing=east,in_wall=true] replace
execute unless block -2018 121 3021 minecraft:iron_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] run setblock -2018 121 3021 minecraft:iron_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] replace
execute unless block -2017 121 3021 minecraft:iron_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] run setblock -2017 121 3021 minecraft:iron_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] replace
execute unless block -2026 122 3009 minecraft:dark_oak_fence_gate[powered=false,open=false,facing=south,in_wall=true] run setblock -2026 122 3009 minecraft:dark_oak_fence_gate[powered=false,open=false,facing=south,in_wall=true] replace
execute unless block -2025 122 3009 minecraft:dark_oak_fence_gate[powered=false,open=false,facing=north,in_wall=false] run setblock -2025 122 3009 minecraft:dark_oak_fence_gate[powered=false,open=false,facing=north,in_wall=false] replace
execute unless block -2023 125 3022 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] run setblock -2023 125 3022 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] replace
execute unless block -2022 125 3022 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] run setblock -2022 125 3022 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] replace
execute unless block -2019 125 3022 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] run setblock -2019 125 3022 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] replace
execute unless block -2018 125 3022 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] run setblock -2018 125 3022 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] replace
execute unless block -2023 125 3023 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] run setblock -2023 125 3023 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] replace
execute unless block -2022 125 3023 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] run setblock -2022 125 3023 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] replace
execute unless block -2019 125 3023 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] run setblock -2019 125 3023 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] replace
execute unless block -2018 125 3023 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] run setblock -2018 125 3023 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] replace
execute unless block -2024 126 3023 minecraft:light[level=4,waterlogged=false] run setblock -2024 126 3023 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2019 126 3023 minecraft:light[level=4,waterlogged=false] run setblock -2019 126 3023 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2013 94 3024 minecraft:light_gray_bed[facing=south,part=foot,occupied=false] run setblock -2013 94 3024 minecraft:light_gray_bed[facing=south,part=foot,occupied=false] replace
execute unless block -2005 94 3024 minecraft:light[level=4,waterlogged=false] run setblock -2005 94 3024 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2002 94 3024 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] run setblock -2002 94 3024 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] replace
execute unless block -2013 94 3025 minecraft:light_gray_bed[facing=south,part=head,occupied=false] run setblock -2013 94 3025 minecraft:light_gray_bed[facing=south,part=head,occupied=false] replace
execute unless block -2012 94 3025 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] run setblock -2012 94 3025 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] replace
execute unless block -2002 94 3025 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] run setblock -2002 94 3025 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] replace
execute unless block -2002 94 3026 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] run setblock -2002 94 3026 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] replace
execute unless block -2016 94 3027 minecraft:light[level=4,waterlogged=false] run setblock -2016 94 3027 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2005 94 3027 minecraft:light[level=4,waterlogged=false] run setblock -2005 94 3027 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2002 94 3027 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -2002 94 3027 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -2013 94 3029 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -2013 94 3029 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2013 94 3030 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] run setblock -2013 94 3030 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] replace
execute unless block -2005 94 3030 minecraft:light[level=4,waterlogged=false] run setblock -2005 94 3030 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2013 94 3031 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] run setblock -2013 94 3031 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] replace
execute unless block -2013 94 3032 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -2013 94 3032 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2012 94 3033 minecraft:iron_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -2012 94 3033 minecraft:iron_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2012 94 3035 minecraft:iron_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -2012 94 3035 minecraft:iron_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2014 94 3037 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -2014 94 3037 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -2013 94 3037 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -2013 94 3037 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -2016 94 3038 minecraft:light[level=4,waterlogged=false] run setblock -2016 94 3038 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2014 94 3038 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -2014 94 3038 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -2013 94 3038 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -2013 94 3038 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -2007 95 3024 minecraft:mangrove_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=bottom] run setblock -2007 95 3024 minecraft:mangrove_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=bottom] replace
execute unless block -2012 95 3025 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] run setblock -2012 95 3025 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] replace
execute unless block -2007 95 3025 minecraft:mangrove_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=bottom] run setblock -2007 95 3025 minecraft:mangrove_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=bottom] replace
execute unless block -2007 95 3026 minecraft:mangrove_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=bottom] run setblock -2007 95 3026 minecraft:mangrove_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=bottom] replace
execute unless block -2007 95 3027 minecraft:mangrove_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=bottom] run setblock -2007 95 3027 minecraft:mangrove_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=bottom] replace
execute unless block -2007 95 3028 minecraft:mangrove_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=bottom] run setblock -2007 95 3028 minecraft:mangrove_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=bottom] replace
execute unless block -2013 95 3029 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -2013 95 3029 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2007 95 3029 minecraft:mangrove_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=bottom] run setblock -2007 95 3029 minecraft:mangrove_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=bottom] replace
execute unless block -2013 95 3030 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] run setblock -2013 95 3030 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] replace
execute unless block -2007 95 3030 minecraft:mangrove_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=bottom] run setblock -2007 95 3030 minecraft:mangrove_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=bottom] replace
execute unless block -2013 95 3031 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] run setblock -2013 95 3031 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] replace
execute unless block -1986 93 3017 minecraft:light[level=4,waterlogged=false] run setblock -1986 93 3017 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1987 93 3019 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1987 93 3019 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1987 93 3021 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1987 93 3021 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1985 93 3021 minecraft:oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] run setblock -1985 93 3021 minecraft:oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] replace
execute unless block -1998 94 3015 minecraft:mangrove_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1998 94 3015 minecraft:mangrove_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1992 94 3017 minecraft:iron_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1992 94 3017 minecraft:iron_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1999 94 3018 minecraft:light[level=4,waterlogged=false] run setblock -1999 94 3018 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1997 94 3018 minecraft:light[level=4,waterlogged=false] run setblock -1997 94 3018 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1992 94 3019 minecraft:iron_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1992 94 3019 minecraft:iron_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1988 94 3019 minecraft:iron_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1988 94 3019 minecraft:iron_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1987 94 3019 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=top] run setblock -1987 94 3019 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=top] replace
execute unless block -1999 94 3020 minecraft:light[level=4,waterlogged=false] run setblock -1999 94 3020 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1988 94 3021 minecraft:iron_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] run setblock -1988 94 3021 minecraft:iron_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] replace
execute unless block -1987 94 3021 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1987 94 3021 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1998 95 3016 minecraft:mangrove_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] run setblock -1998 95 3016 minecraft:mangrove_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1992 95 3017 minecraft:iron_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1992 95 3017 minecraft:iron_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1992 95 3019 minecraft:iron_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1992 95 3019 minecraft:iron_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1988 95 3019 minecraft:birch_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=top] run setblock -1988 95 3019 minecraft:birch_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=top] replace
execute unless block -1987 95 3019 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1987 95 3019 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1988 95 3021 minecraft:birch_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] run setblock -1988 95 3021 minecraft:birch_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] replace
execute unless block -1987 95 3021 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1987 95 3021 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1992 96 3017 minecraft:iron_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1992 96 3017 minecraft:iron_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1992 96 3019 minecraft:iron_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1992 96 3019 minecraft:iron_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1988 96 3019 minecraft:birch_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1988 96 3019 minecraft:birch_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1987 96 3019 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1987 96 3019 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1988 96 3021 minecraft:birch_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1988 96 3021 minecraft:birch_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2007 95 3031 minecraft:mangrove_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=bottom] run setblock -2007 95 3031 minecraft:mangrove_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=bottom] replace
execute unless block -1987 96 3021 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1987 96 3021 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2001 95 3031 minecraft:mangrove_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] run setblock -2001 95 3031 minecraft:mangrove_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1992 97 3017 minecraft:iron_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1992 97 3017 minecraft:iron_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2013 95 3032 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -2013 95 3032 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1992 97 3019 minecraft:iron_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1992 97 3019 minecraft:iron_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2007 95 3032 minecraft:mangrove_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] run setblock -2007 95 3032 minecraft:mangrove_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1988 97 3019 minecraft:birch_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=top] run setblock -1988 97 3019 minecraft:birch_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=top] replace
execute unless block -2006 95 3032 minecraft:mangrove_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] run setblock -2006 95 3032 minecraft:mangrove_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1987 97 3019 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1987 97 3019 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2005 95 3032 minecraft:mangrove_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] run setblock -2005 95 3032 minecraft:mangrove_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1988 97 3021 minecraft:birch_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] run setblock -1988 97 3021 minecraft:birch_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] replace
execute unless block -2012 95 3033 minecraft:iron_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -2012 95 3033 minecraft:iron_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1987 97 3021 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1987 97 3021 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2012 95 3035 minecraft:iron_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -2012 95 3035 minecraft:iron_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1986 97 3023 minecraft:dark_oak_fence_gate[powered=false,open=false,facing=west,in_wall=false] run setblock -1986 97 3023 minecraft:dark_oak_fence_gate[powered=false,open=false,facing=west,in_wall=false] replace
execute unless block -2012 96 3025 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] run setblock -2012 96 3025 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] replace
execute unless block -1992 98 3017 minecraft:iron_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1992 98 3017 minecraft:iron_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2013 96 3029 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -2013 96 3029 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1986 98 3017 minecraft:light[level=4,waterlogged=false] run setblock -1986 98 3017 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2013 96 3032 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -2013 96 3032 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1992 98 3019 minecraft:iron_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1992 98 3019 minecraft:iron_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2012 96 3033 minecraft:iron_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -2012 96 3033 minecraft:iron_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1988 98 3019 minecraft:birch_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1988 98 3019 minecraft:birch_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2012 96 3035 minecraft:iron_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -2012 96 3035 minecraft:iron_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1987 98 3020 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] run setblock -1987 98 3020 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] replace
execute unless block -2013 97 3029 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -2013 97 3029 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1988 98 3021 minecraft:birch_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1988 98 3021 minecraft:birch_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2013 97 3030 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] run setblock -2013 97 3030 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] replace
execute unless block -1991 100 3016 minecraft:light[level=4,waterlogged=false] run setblock -1991 100 3016 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2013 97 3031 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] run setblock -2013 97 3031 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] replace
execute unless block -1990 100 3016 minecraft:light[level=4,waterlogged=false] run setblock -1990 100 3016 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2013 97 3032 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -2013 97 3032 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1989 100 3016 minecraft:light[level=4,waterlogged=false] run setblock -1989 100 3016 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2012 97 3033 minecraft:iron_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -2012 97 3033 minecraft:iron_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1998 104 3010 minecraft:light[level=4,waterlogged=false] run setblock -1998 104 3010 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2012 97 3035 minecraft:iron_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -2012 97 3035 minecraft:iron_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1995 104 3010 minecraft:light[level=4,waterlogged=false] run setblock -1995 104 3010 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2013 98 3031 minecraft:lever[powered=false,facing=south,face=wall] run setblock -2013 98 3031 minecraft:lever[powered=false,facing=south,face=wall] replace
execute unless block -1987 104 3011 minecraft:oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1987 104 3011 minecraft:oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2012 98 3033 minecraft:iron_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -2012 98 3033 minecraft:iron_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1986 104 3011 minecraft:oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1986 104 3011 minecraft:oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2012 98 3035 minecraft:iron_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -2012 98 3035 minecraft:iron_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1985 104 3011 minecraft:oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1985 104 3011 minecraft:oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2013 99 3031 minecraft:lever[powered=true,facing=west,face=wall] run setblock -2013 99 3031 minecraft:lever[powered=true,facing=west,face=wall] replace
execute unless block -1995 104 3014 minecraft:light[level=4,waterlogged=false] run setblock -1995 104 3014 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2014 99 3037 minecraft:iron_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=bottom] run setblock -2014 99 3037 minecraft:iron_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=bottom] replace
execute unless block -1993 104 3016 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1993 104 3016 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2013 99 3037 minecraft:iron_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] run setblock -2013 99 3037 minecraft:iron_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] replace
execute unless block -1992 104 3017 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1992 104 3017 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2014 99 3038 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] run setblock -2014 99 3038 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1991 104 3017 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1991 104 3017 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2013 99 3038 minecraft:iron_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] run setblock -2013 99 3038 minecraft:iron_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] replace
execute unless block -1992 104 3019 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1992 104 3019 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2006 99 3038 minecraft:iron_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] run setblock -2006 99 3038 minecraft:iron_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] replace
execute unless block -1991 104 3019 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1991 104 3019 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2005 99 3038 minecraft:iron_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] run setblock -2005 99 3038 minecraft:iron_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] replace
execute unless block -1994 104 3020 minecraft:light[level=4,waterlogged=false] run setblock -1994 104 3020 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2004 99 3038 minecraft:iron_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] run setblock -2004 99 3038 minecraft:iron_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] replace
execute unless block -1997 104 3023 minecraft:light[level=4,waterlogged=false] run setblock -1997 104 3023 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2003 99 3038 minecraft:iron_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] run setblock -2003 99 3038 minecraft:iron_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] replace
execute unless block -1996 105 3011 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] run setblock -1996 105 3011 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2002 99 3038 minecraft:iron_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] run setblock -2002 99 3038 minecraft:iron_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] replace
execute unless block -1996 105 3013 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] run setblock -1996 105 3013 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2001 99 3038 minecraft:iron_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] run setblock -2001 99 3038 minecraft:iron_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] replace
execute unless block -1993 105 3016 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=top] run setblock -1993 105 3016 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=top] replace
execute unless block -2010 100 3027 minecraft:light[level=4,waterlogged=false] run setblock -2010 100 3027 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1988 105 3016 minecraft:oak_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] run setblock -1988 105 3016 minecraft:oak_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2011 100 3033 minecraft:light[level=4,waterlogged=false] run setblock -2011 100 3033 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1987 105 3016 minecraft:oak_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] run setblock -1987 105 3016 minecraft:oak_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2011 100 3035 minecraft:light[level=4,waterlogged=false] run setblock -2011 100 3035 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2000 105 3017 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=bottom] run setblock -2000 105 3017 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=bottom] replace
execute unless block -2014 101 3032 minecraft:light[level=4,waterlogged=false] run setblock -2014 101 3032 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1998 105 3017 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=bottom] run setblock -1998 105 3017 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=bottom] replace
execute unless block -2014 101 3034 minecraft:light[level=4,waterlogged=false] run setblock -2014 101 3034 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1992 105 3017 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=top] run setblock -1992 105 3017 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=top] replace
execute unless block -2016 104 3025 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -2016 104 3025 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -1991 105 3017 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=top] run setblock -1991 105 3017 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=top] replace
execute unless block -2002 104 3026 minecraft:light[level=4,waterlogged=false] run setblock -2002 104 3026 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1992 105 3019 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] run setblock -1992 105 3019 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] replace
execute unless block -2016 104 3028 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock -2016 104 3028 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block -1991 105 3019 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] run setblock -1991 105 3019 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] replace
execute unless block -2012 104 3031 minecraft:iron_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -2012 104 3031 minecraft:iron_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1998 105 3023 minecraft:potted_dead_bush run setblock -1998 105 3023 minecraft:potted_dead_bush replace
execute unless block -2011 104 3031 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -2011 104 3031 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1993 106 3016 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1993 106 3016 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2015 104 3033 minecraft:iron_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -2015 104 3033 minecraft:iron_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1992 106 3017 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1992 106 3017 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2006 104 3033 minecraft:light[level=4,waterlogged=false] run setblock -2006 104 3033 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1991 106 3017 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1991 106 3017 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2002 104 3033 minecraft:light[level=4,waterlogged=false] run setblock -2002 104 3033 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1992 106 3019 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1992 106 3019 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2015 104 3034 minecraft:iron_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -2015 104 3034 minecraft:iron_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1991 106 3019 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1991 106 3019 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2012 104 3034 minecraft:iron_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -2012 104 3034 minecraft:iron_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1994 106 3020 minecraft:light[level=4,waterlogged=false] run setblock -1994 106 3020 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2011 104 3034 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -2011 104 3034 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1993 107 3016 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1993 107 3016 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2006 104 3034 minecraft:light[level=4,waterlogged=false] run setblock -2006 104 3034 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2002 104 3034 minecraft:light[level=4,waterlogged=false] run setblock -2002 104 3034 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2006 104 3035 minecraft:light[level=4,waterlogged=false] run setblock -2006 104 3035 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2002 104 3035 minecraft:light[level=4,waterlogged=false] run setblock -2002 104 3035 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1992 107 3017 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1992 107 3017 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2016 105 3025 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -2016 105 3025 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -2007 105 3026 minecraft:light[level=4,waterlogged=false] run setblock -2007 105 3026 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2007 105 3027 minecraft:light[level=4,waterlogged=false] run setblock -2007 105 3027 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2016 105 3028 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock -2016 105 3028 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block -2007 105 3028 minecraft:light[level=4,waterlogged=false] run setblock -2007 105 3028 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2012 105 3031 minecraft:iron_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -2012 105 3031 minecraft:iron_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2011 105 3031 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -2011 105 3031 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2015 105 3033 minecraft:iron_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -2015 105 3033 minecraft:iron_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2015 105 3034 minecraft:iron_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -2015 105 3034 minecraft:iron_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2012 105 3034 minecraft:iron_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -2012 105 3034 minecraft:iron_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2011 105 3034 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -2011 105 3034 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2010 105 3036 minecraft:jungle_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] run setblock -2010 105 3036 minecraft:jungle_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] replace
execute unless block -2010 105 3037 minecraft:jungle_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] run setblock -2010 105 3037 minecraft:jungle_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] replace
execute unless block -2016 106 3025 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -2016 106 3025 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -2016 106 3028 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock -2016 106 3028 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block -2012 106 3031 minecraft:iron_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -2012 106 3031 minecraft:iron_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2011 106 3031 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -2011 106 3031 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2015 106 3033 minecraft:iron_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -2015 106 3033 minecraft:iron_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2015 106 3034 minecraft:iron_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -2015 106 3034 minecraft:iron_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2012 106 3034 minecraft:iron_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -2012 106 3034 minecraft:iron_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2011 106 3034 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -2011 106 3034 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2016 107 3025 minecraft:light[level=4,waterlogged=false] run setblock -2016 107 3025 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2010 107 3027 minecraft:spruce_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] run setblock -2010 107 3027 minecraft:spruce_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] replace
execute unless block -2009 107 3027 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] run setblock -2009 107 3027 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] replace
execute unless block -2016 107 3028 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock -2016 107 3028 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block -2010 107 3029 minecraft:spruce_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] run setblock -2010 107 3029 minecraft:spruce_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] replace
execute unless block -2009 107 3029 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] run setblock -2009 107 3029 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] replace
execute unless block -2012 107 3031 minecraft:iron_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -2012 107 3031 minecraft:iron_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2011 107 3031 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -2011 107 3031 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2015 107 3033 minecraft:iron_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -2015 107 3033 minecraft:iron_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2015 107 3034 minecraft:iron_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -2015 107 3034 minecraft:iron_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2012 107 3034 minecraft:iron_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -2012 107 3034 minecraft:iron_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2011 107 3034 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -2011 107 3034 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2014 107 3035 minecraft:dark_oak_fence_gate[powered=false,open=false,facing=south,in_wall=false] run setblock -2014 107 3035 minecraft:dark_oak_fence_gate[powered=false,open=false,facing=south,in_wall=false] replace
execute unless block -2013 107 3035 minecraft:dark_oak_fence_gate[powered=false,open=false,facing=south,in_wall=false] run setblock -2013 107 3035 minecraft:dark_oak_fence_gate[powered=false,open=false,facing=south,in_wall=false] replace
execute unless block -2016 107 3036 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -2016 107 3036 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2016 107 3037 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -2016 107 3037 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2016 108 3028 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock -2016 108 3028 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block -2007 108 3028 minecraft:potted_fern run setblock -2007 108 3028 minecraft:potted_fern replace
execute unless block -2012 108 3031 minecraft:iron_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -2012 108 3031 minecraft:iron_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2011 108 3031 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -2011 108 3031 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2015 108 3033 minecraft:iron_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -2015 108 3033 minecraft:iron_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2015 108 3034 minecraft:iron_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -2015 108 3034 minecraft:iron_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2012 108 3034 minecraft:iron_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -2012 108 3034 minecraft:iron_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2011 108 3034 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -2011 108 3034 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2016 108 3036 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -2016 108 3036 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2016 108 3037 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -2016 108 3037 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2016 109 3028 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock -2016 109 3028 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block -2012 109 3031 minecraft:iron_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -2012 109 3031 minecraft:iron_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2011 109 3031 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -2011 109 3031 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2012 109 3034 minecraft:iron_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -2012 109 3034 minecraft:iron_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2011 109 3034 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -2011 109 3034 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2016 109 3036 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -2016 109 3036 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2016 109 3037 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -2016 109 3037 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2014 110 3025 minecraft:light[level=4,waterlogged=false] run setblock -2014 110 3025 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2013 110 3025 minecraft:light[level=4,waterlogged=false] run setblock -2013 110 3025 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2007 110 3025 minecraft:jungle_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock -2007 110 3025 minecraft:jungle_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block -2007 110 3026 minecraft:jungle_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock -2007 110 3026 minecraft:jungle_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block -2007 110 3027 minecraft:jungle_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock -2007 110 3027 minecraft:jungle_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block -2007 110 3028 minecraft:jungle_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock -2007 110 3028 minecraft:jungle_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block -2016 110 3036 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] run setblock -2016 110 3036 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] replace
execute unless block -2010 110 3036 minecraft:jungle_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock -2010 110 3036 minecraft:jungle_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block -2016 110 3037 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] run setblock -2016 110 3037 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] replace
execute unless block -2010 110 3037 minecraft:jungle_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock -2010 110 3037 minecraft:jungle_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block -2010 110 3038 minecraft:light[level=4,waterlogged=false] run setblock -2010 110 3038 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2014 111 3025 minecraft:spruce_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] run setblock -2014 111 3025 minecraft:spruce_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] replace
execute unless block -2013 111 3025 minecraft:spruce_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] run setblock -2013 111 3025 minecraft:spruce_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] replace
execute unless block -2014 111 3026 minecraft:spruce_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] run setblock -2014 111 3026 minecraft:spruce_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] replace
execute unless block -2013 111 3026 minecraft:spruce_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] run setblock -2013 111 3026 minecraft:spruce_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] replace
execute unless block -2014 111 3027 minecraft:spruce_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] run setblock -2014 111 3027 minecraft:spruce_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] replace
execute unless block -2013 111 3027 minecraft:spruce_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] run setblock -2013 111 3027 minecraft:spruce_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] replace
execute unless block -2014 111 3028 minecraft:spruce_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] run setblock -2014 111 3028 minecraft:spruce_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] replace
execute unless block -2013 111 3028 minecraft:spruce_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] run setblock -2013 111 3028 minecraft:spruce_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] replace
execute unless block -2016 111 3029 minecraft:light[level=4,waterlogged=false] run setblock -2016 111 3029 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2016 111 3033 minecraft:light[level=4,waterlogged=false] run setblock -2016 111 3033 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2016 111 3034 minecraft:light[level=4,waterlogged=false] run setblock -2016 111 3034 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2014 111 3035 minecraft:light[level=4,waterlogged=false] run setblock -2014 111 3035 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2013 111 3035 minecraft:light[level=4,waterlogged=false] run setblock -2013 111 3035 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2016 111 3038 minecraft:light[level=4,waterlogged=false] run setblock -2016 111 3038 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2010 111 3038 minecraft:light[level=4,waterlogged=false] run setblock -2010 111 3038 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2010 112 3026 minecraft:light[level=4,waterlogged=false] run setblock -2010 112 3026 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2009 112 3026 minecraft:light[level=4,waterlogged=false] run setblock -2009 112 3026 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2009 112 3032 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] run setblock -2009 112 3032 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] replace
execute unless block -2007 112 3032 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] run setblock -2007 112 3032 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] replace
execute unless block -2005 112 3032 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] run setblock -2005 112 3032 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] replace
execute unless block -2003 112 3032 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] run setblock -2003 112 3032 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] replace
execute unless block -2001 112 3032 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] run setblock -2001 112 3032 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] replace
execute unless block -2009 112 3033 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] run setblock -2009 112 3033 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] replace
execute unless block -2007 112 3033 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] run setblock -2007 112 3033 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] replace
execute unless block -2005 112 3033 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] run setblock -2005 112 3033 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] replace
execute unless block -2003 112 3033 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] run setblock -2003 112 3033 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] replace
execute unless block -2001 112 3033 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] run setblock -2001 112 3033 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] replace
execute unless block -2009 112 3034 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] run setblock -2009 112 3034 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] replace
execute unless block -2007 112 3034 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] run setblock -2007 112 3034 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] replace
execute unless block -2005 112 3034 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] run setblock -2005 112 3034 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] replace
execute unless block -2003 112 3034 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] run setblock -2003 112 3034 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] replace
execute unless block -2001 112 3034 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] run setblock -2001 112 3034 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] replace
execute unless block -2009 112 3035 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] run setblock -2009 112 3035 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] replace
execute unless block -2007 112 3035 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] run setblock -2007 112 3035 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] replace
execute unless block -2005 112 3035 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] run setblock -2005 112 3035 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] replace
execute unless block -2003 112 3035 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] run setblock -2003 112 3035 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] replace
execute unless block -2001 112 3035 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] run setblock -2001 112 3035 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] replace
execute unless block -2016 114 3024 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -2016 114 3024 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2015 114 3024 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -2015 114 3024 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2014 114 3024 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -2014 114 3024 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2005 114 3024 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -2005 114 3024 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2013 114 3025 minecraft:light[level=4,waterlogged=false] run setblock -2013 114 3025 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2005 114 3025 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -2005 114 3025 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2005 114 3026 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -2005 114 3026 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2005 114 3027 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -2005 114 3027 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2005 114 3028 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -2005 114 3028 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2005 114 3029 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -2005 114 3029 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1991 107 3017 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1991 107 3017 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1992 107 3019 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1992 107 3019 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1991 107 3019 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1991 107 3019 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1993 108 3016 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1993 108 3016 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1992 108 3017 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1992 108 3017 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1991 108 3017 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1991 108 3017 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1992 108 3019 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1992 108 3019 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1991 108 3019 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1991 108 3019 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1989 108 3021 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] run setblock -1989 108 3021 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] replace
execute unless block -1989 108 3022 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] run setblock -1989 108 3022 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] replace
execute unless block -2000 109 3016 minecraft:lantern[hanging=true,waterlogged=false] run setblock -2000 109 3016 minecraft:lantern[hanging=true,waterlogged=false] replace
execute unless block -1993 109 3016 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1993 109 3016 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1992 109 3017 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1992 109 3017 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1992 109 3019 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1992 109 3019 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1989 109 3022 minecraft:potted_dark_oak_sapling run setblock -1989 109 3022 minecraft:potted_dark_oak_sapling replace
execute unless block -1993 110 3016 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1993 110 3016 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1993 110 3017 minecraft:light[level=4,waterlogged=false] run setblock -1993 110 3017 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1993 110 3019 minecraft:light[level=4,waterlogged=false] run setblock -1993 110 3019 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1996 110 3022 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] run setblock -1996 110 3022 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] replace
execute unless block -1989 111 3010 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -1989 111 3010 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -1988 111 3010 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -1988 111 3010 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -1987 111 3010 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -1987 111 3010 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -1986 111 3010 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -1986 111 3010 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -1985 111 3010 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -1985 111 3010 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -1996 111 3022 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] run setblock -1996 111 3022 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] replace
execute unless block -1993 112 3014 minecraft:light[level=4,waterlogged=false] run setblock -1993 112 3014 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1993 112 3015 minecraft:light[level=4,waterlogged=false] run setblock -1993 112 3015 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1995 112 3016 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] run setblock -1995 112 3016 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] replace
execute unless block -1996 112 3022 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] run setblock -1996 112 3022 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] replace
execute unless block -1988 113 3010 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] run setblock -1988 113 3010 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1996 113 3022 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] run setblock -1996 113 3022 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] replace
execute unless block -1994 114 3016 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock -1994 114 3016 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block -1993 114 3016 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] run setblock -1993 114 3016 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] replace
execute unless block -1995 114 3017 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1995 114 3017 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1991 114 3017 minecraft:light[level=4,waterlogged=false] run setblock -1991 114 3017 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1995 114 3018 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1995 114 3018 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1992 114 3018 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1992 114 3018 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1995 114 3019 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1995 114 3019 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1992 114 3019 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1992 114 3019 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1995 114 3020 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1995 114 3020 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1991 114 3020 minecraft:light[level=4,waterlogged=false] run setblock -1991 114 3020 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2005 114 3030 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -2005 114 3030 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1988 114 3020 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1988 114 3020 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2014 114 3031 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock -2014 114 3031 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block -1988 114 3021 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1988 114 3021 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2013 114 3031 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] run setblock -2013 114 3031 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] replace
execute unless block -1996 114 3022 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] run setblock -1996 114 3022 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] replace
execute unless block -2004 114 3031 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock -2004 114 3031 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block -1986 114 3022 minecraft:light[level=4,waterlogged=false] run setblock -1986 114 3022 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2003 114 3031 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] run setblock -2003 114 3031 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] replace
execute unless block -1994 115 3016 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] run setblock -1994 115 3016 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] replace
execute unless block -2002 114 3031 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock -2002 114 3031 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block -1993 115 3016 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] run setblock -1993 115 3016 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] replace
execute unless block -2001 114 3031 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] run setblock -2001 114 3031 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] replace
execute unless block -1995 115 3017 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] run setblock -1995 115 3017 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2016 114 3032 minecraft:light[level=4,waterlogged=false] run setblock -2016 114 3032 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1995 115 3018 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] run setblock -1995 115 3018 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2005 114 3032 minecraft:light[level=4,waterlogged=false] run setblock -2005 114 3032 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1992 115 3018 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1992 115 3018 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2009 114 3033 minecraft:light[level=4,waterlogged=false] run setblock -2009 114 3033 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1986 115 3018 minecraft:potted_warped_roots run setblock -1986 115 3018 minecraft:potted_warped_roots replace
execute unless block -2012 114 3034 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -2012 114 3034 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1995 115 3019 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] run setblock -1995 115 3019 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2008 114 3034 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -2008 114 3034 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1992 115 3019 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1992 115 3019 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2012 114 3035 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -2012 114 3035 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1995 115 3020 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] run setblock -1995 115 3020 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2008 114 3035 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -2008 114 3035 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1988 115 3020 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=top] run setblock -1988 115 3020 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=top] replace
execute unless block -2005 114 3035 minecraft:light[level=4,waterlogged=false] run setblock -2005 114 3035 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1988 115 3021 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] run setblock -1988 115 3021 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] replace
execute unless block -2009 114 3036 minecraft:light[level=4,waterlogged=false] run setblock -2009 114 3036 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1996 115 3022 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] run setblock -1996 115 3022 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] replace
execute unless block -2004 114 3036 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock -2004 114 3036 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block -1992 116 3018 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1992 116 3018 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2003 114 3036 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] run setblock -2003 114 3036 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] replace
execute unless block -1992 116 3019 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1992 116 3019 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2002 114 3036 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock -2002 114 3036 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block -1988 116 3020 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1988 116 3020 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2001 114 3036 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] run setblock -2001 114 3036 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] replace
execute unless block -1988 116 3021 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1988 116 3021 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2016 114 3037 minecraft:composter[level=0] run setblock -2016 114 3037 minecraft:composter[level=0] replace
execute unless block -1996 116 3022 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] run setblock -1996 116 3022 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] replace
execute unless block -2014 114 3037 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -2014 114 3037 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -1996 116 3023 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] run setblock -1996 116 3023 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] replace
execute unless block -2013 114 3037 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -2013 114 3037 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -1993 117 3014 minecraft:light[level=4,waterlogged=false] run setblock -1993 117 3014 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2016 114 3038 minecraft:composter[level=0] run setblock -2016 114 3038 minecraft:composter[level=0] replace
execute unless block -1993 117 3015 minecraft:light[level=4,waterlogged=false] run setblock -1993 117 3015 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2014 114 3038 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -2014 114 3038 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -1992 117 3018 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1992 117 3018 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2013 114 3038 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -2013 114 3038 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -1992 117 3019 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1992 117 3019 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2005 115 3024 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] run setblock -2005 115 3024 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1988 117 3020 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1988 117 3020 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2016 115 3025 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] run setblock -2016 115 3025 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] replace
execute unless block -1988 117 3021 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1988 117 3021 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2013 115 3025 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] run setblock -2013 115 3025 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] replace
execute unless block -1991 117 3022 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] run setblock -1991 117 3022 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] replace
execute unless block -2005 115 3025 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] run setblock -2005 115 3025 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1991 117 3023 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] run setblock -1991 117 3023 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] replace
execute unless block -2015 115 3026 minecraft:composter[level=0] run setblock -2015 115 3026 minecraft:composter[level=0] replace
execute unless block -1991 118 3023 minecraft:potted_dark_oak_sapling run setblock -1991 118 3023 minecraft:potted_dark_oak_sapling replace
execute unless block -2014 115 3026 minecraft:composter[level=0] run setblock -2014 115 3026 minecraft:composter[level=0] replace
execute unless block -1991 119 3017 minecraft:light[level=4,waterlogged=false] run setblock -1991 119 3017 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2005 115 3026 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] run setblock -2005 115 3026 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1989 119 3019 minecraft:oak_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] run setblock -1989 119 3019 minecraft:oak_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] replace
execute unless block -2005 115 3027 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] run setblock -2005 115 3027 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1999 120 3016 minecraft:spruce_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] run setblock -1999 120 3016 minecraft:spruce_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] replace
execute unless block -2005 115 3028 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] run setblock -2005 115 3028 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1996 120 3016 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] run setblock -1996 120 3016 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] replace
execute unless block -2005 115 3029 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] run setblock -2005 115 3029 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1986 120 3018 minecraft:potted_flowering_azalea_bush run setblock -1986 120 3018 minecraft:potted_flowering_azalea_bush replace
execute unless block -2005 115 3030 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] run setblock -2005 115 3030 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1996 120 3020 minecraft:light[level=4,waterlogged=false] run setblock -1996 120 3020 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2014 115 3031 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock -2014 115 3031 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block -1986 120 3022 minecraft:light[level=4,waterlogged=false] run setblock -1986 120 3022 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2013 115 3031 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] run setblock -2013 115 3031 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] replace
execute unless block -1996 120 3023 minecraft:light[level=4,waterlogged=false] run setblock -1996 120 3023 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2004 115 3031 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] run setblock -2004 115 3031 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] replace
execute unless block -1986 121 3016 minecraft:light[level=4,waterlogged=false] run setblock -1986 121 3016 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2003 115 3031 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] run setblock -2003 115 3031 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] replace
execute unless block -2000 124 3010 minecraft:light[level=4,waterlogged=false] run setblock -2000 124 3010 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2002 115 3031 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] run setblock -2002 115 3031 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] replace
execute unless block -2000 124 3015 minecraft:light[level=4,waterlogged=false] run setblock -2000 124 3015 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2001 115 3031 minecraft:spruce_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=bottom] run setblock -2001 115 3031 minecraft:spruce_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=bottom] replace
execute unless block -2000 124 3017 minecraft:light[level=4,waterlogged=false] run setblock -2000 124 3017 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2009 115 3033 minecraft:light[level=4,waterlogged=false] run setblock -2009 115 3033 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2012 115 3034 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=top] run setblock -2012 115 3034 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=top] replace
execute unless block -2008 115 3034 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -2008 115 3034 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2012 115 3035 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] run setblock -2012 115 3035 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] replace
execute unless block -2008 115 3035 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -2008 115 3035 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2009 115 3036 minecraft:light[level=4,waterlogged=false] run setblock -2009 115 3036 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1994 134 3021 minecraft:iron_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] run setblock -1994 134 3021 minecraft:iron_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] replace
execute unless block -2004 115 3036 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] run setblock -2004 115 3036 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] replace
execute unless block -1995 134 3023 minecraft:iron_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] run setblock -1995 134 3023 minecraft:iron_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] replace
execute unless block -2003 115 3036 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] run setblock -2003 115 3036 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] replace
execute unless block -1993 134 3023 minecraft:iron_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] run setblock -1993 134 3023 minecraft:iron_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] replace
execute unless block -2002 115 3036 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] run setblock -2002 115 3036 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] replace
execute unless block -2001 115 3036 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] run setblock -2001 115 3036 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] replace
execute unless block -2016 115 3038 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] run setblock -2016 115 3038 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2009 115 3038 minecraft:lantern[hanging=false,waterlogged=false] run setblock -2009 115 3038 minecraft:lantern[hanging=false,waterlogged=false] replace
execute unless block -2016 116 3025 minecraft:spruce_fence_gate[powered=false,open=true,facing=south,in_wall=false] run setblock -2016 116 3025 minecraft:spruce_fence_gate[powered=false,open=true,facing=south,in_wall=false] replace
execute unless block -2014 116 3031 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] run setblock -2014 116 3031 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] replace
execute unless block -2013 116 3031 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] run setblock -2013 116 3031 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] replace
execute unless block -2012 116 3034 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -2012 116 3034 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2008 116 3034 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -2008 116 3034 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2012 116 3035 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -2012 116 3035 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2008 116 3035 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -2008 116 3035 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2013 117 3024 minecraft:spruce_fence_gate[powered=false,open=true,facing=east,in_wall=false] run setblock -2013 117 3024 minecraft:spruce_fence_gate[powered=false,open=true,facing=east,in_wall=false] replace
execute unless block -2016 117 3025 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] run setblock -2016 117 3025 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2013 117 3025 minecraft:spruce_fence_gate[powered=false,open=true,facing=east,in_wall=false] run setblock -2013 117 3025 minecraft:spruce_fence_gate[powered=false,open=true,facing=east,in_wall=false] replace
execute unless block -2007 117 3027 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] run setblock -2007 117 3027 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] replace
execute unless block -2014 117 3031 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock -2014 117 3031 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block -2013 117 3031 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] run setblock -2013 117 3031 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] replace
execute unless block -2012 117 3034 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -2012 117 3034 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2008 117 3034 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -2008 117 3034 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2012 117 3035 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -2012 117 3035 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2008 117 3035 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -2008 117 3035 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2013 118 3024 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] run setblock -2013 118 3024 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] replace
execute unless block -2016 118 3025 minecraft:light[level=4,waterlogged=false] run setblock -2016 118 3025 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2013 118 3025 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] run setblock -2013 118 3025 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] replace
execute unless block -2007 118 3025 minecraft:light[level=4,waterlogged=false] run setblock -2007 118 3025 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2006 118 3028 minecraft:spruce_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] run setblock -2006 118 3028 minecraft:spruce_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] replace
execute unless block -2006 118 3029 minecraft:spruce_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] run setblock -2006 118 3029 minecraft:spruce_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] replace
execute unless block -2014 119 3029 minecraft:light[level=4,waterlogged=false] run setblock -2014 119 3029 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2014 119 3037 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] run setblock -2014 119 3037 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2013 119 3037 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] run setblock -2013 119 3037 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2014 119 3038 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] run setblock -2014 119 3038 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2013 119 3038 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] run setblock -2013 119 3038 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2004 120 3031 minecraft:spruce_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] run setblock -2004 120 3031 minecraft:spruce_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] replace
execute unless block -2001 120 3031 minecraft:spruce_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] run setblock -2001 120 3031 minecraft:spruce_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] replace
execute unless block -2004 120 3036 minecraft:spruce_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] run setblock -2004 120 3036 minecraft:spruce_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] replace
execute unless block -2001 120 3036 minecraft:spruce_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] run setblock -2001 120 3036 minecraft:spruce_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] replace
execute unless block -2016 121 3025 minecraft:light[level=4,waterlogged=false] run setblock -2016 121 3025 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2015 121 3026 minecraft:composter[level=0] run setblock -2015 121 3026 minecraft:composter[level=0] replace
execute unless block -2014 121 3026 minecraft:composter[level=0] run setblock -2014 121 3026 minecraft:composter[level=0] replace
execute unless block -2013 121 3032 minecraft:light[level=4,waterlogged=false] run setblock -2013 121 3032 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2015 121 3034 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -2015 121 3034 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -2014 121 3034 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -2014 121 3034 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -2011 121 3034 minecraft:light[level=4,waterlogged=false] run setblock -2011 121 3034 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2009 121 3034 minecraft:light[level=4,waterlogged=false] run setblock -2009 121 3034 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2015 121 3035 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -2015 121 3035 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -2014 121 3035 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -2014 121 3035 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -2003 121 3037 minecraft:light[level=4,waterlogged=false] run setblock -2003 121 3037 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2014 122 3027 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -2014 122 3027 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2013 122 3027 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -2013 122 3027 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2012 122 3029 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] run setblock -2012 122 3029 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] replace
execute unless block -2014 123 3027 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -2014 123 3027 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2013 123 3027 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -2013 123 3027 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2010 123 3027 minecraft:light[level=4,waterlogged=false] run setblock -2010 123 3027 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2009 123 3027 minecraft:light[level=4,waterlogged=false] run setblock -2009 123 3027 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2015 123 3028 minecraft:light[level=4,waterlogged=false] run setblock -2015 123 3028 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2012 123 3028 minecraft:light[level=4,waterlogged=false] run setblock -2012 123 3028 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2013 123 3032 minecraft:light[level=4,waterlogged=false] run setblock -2013 123 3032 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2005 123 3033 minecraft:light[level=4,waterlogged=false] run setblock -2005 123 3033 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2005 123 3034 minecraft:light[level=4,waterlogged=false] run setblock -2005 123 3034 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2014 124 3027 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -2014 124 3027 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2013 124 3027 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -2013 124 3027 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2015 125 3024 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] run setblock -2015 125 3024 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] replace
execute unless block -2014 125 3024 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] run setblock -2014 125 3024 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] replace
execute unless block -2015 126 3034 minecraft:iron_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=bottom] run setblock -2015 126 3034 minecraft:iron_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=bottom] replace
execute unless block -2014 126 3034 minecraft:iron_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] run setblock -2014 126 3034 minecraft:iron_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] replace
execute unless block -2015 126 3035 minecraft:iron_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=bottom] run setblock -2015 126 3035 minecraft:iron_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=bottom] replace
execute unless block -2014 126 3035 minecraft:iron_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] run setblock -2014 126 3035 minecraft:iron_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] replace
execute unless block -1950 104 3024 minecraft:light[level=4,waterlogged=false] run setblock -1950 104 3024 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1944 104 3024 minecraft:light[level=4,waterlogged=false] run setblock -1944 104 3024 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1951 104 3025 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -1951 104 3025 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -1950 104 3025 minecraft:iron_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] run setblock -1950 104 3025 minecraft:iron_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] replace
execute unless block -1949 104 3025 minecraft:iron_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] run setblock -1949 104 3025 minecraft:iron_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] replace
execute unless block -1948 104 3025 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -1948 104 3025 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -1950 104 3026 minecraft:iron_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] run setblock -1950 104 3026 minecraft:iron_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] replace
execute unless block -1949 104 3026 minecraft:iron_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] run setblock -1949 104 3026 minecraft:iron_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] replace
execute unless block -1950 104 3027 minecraft:birch_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] run setblock -1950 104 3027 minecraft:birch_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] replace
execute unless block -1949 104 3027 minecraft:birch_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] run setblock -1949 104 3027 minecraft:birch_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] replace
execute unless block -1950 104 3028 minecraft:birch_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] run setblock -1950 104 3028 minecraft:birch_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] replace
execute unless block -1949 104 3028 minecraft:birch_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] run setblock -1949 104 3028 minecraft:birch_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] replace
execute unless block -1950 104 3029 minecraft:birch_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] run setblock -1950 104 3029 minecraft:birch_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] replace
execute unless block -1949 104 3029 minecraft:birch_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] run setblock -1949 104 3029 minecraft:birch_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] replace
execute unless block -1950 104 3030 minecraft:birch_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] run setblock -1950 104 3030 minecraft:birch_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] replace
execute unless block -1949 104 3030 minecraft:birch_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] run setblock -1949 104 3030 minecraft:birch_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] replace
execute unless block -1947 104 3034 minecraft:light[level=4,waterlogged=false] run setblock -1947 104 3034 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1950 105 3028 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1950 105 3028 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1949 105 3028 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1949 105 3028 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1951 105 3034 minecraft:iron_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1951 105 3034 minecraft:iron_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1948 105 3034 minecraft:iron_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1948 105 3034 minecraft:iron_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1950 106 3033 minecraft:lever[powered=false,facing=east,face=floor] run setblock -1950 106 3033 minecraft:lever[powered=false,facing=east,face=floor] replace
execute unless block -1949 106 3033 minecraft:lever[powered=false,facing=west,face=floor] run setblock -1949 106 3033 minecraft:lever[powered=false,facing=west,face=floor] replace
execute unless block -1950 107 3026 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] run setblock -1950 107 3026 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1949 107 3026 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] run setblock -1949 107 3026 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1951 107 3027 minecraft:lever[powered=false,facing=east,face=floor] run setblock -1951 107 3027 minecraft:lever[powered=false,facing=east,face=floor] replace
execute unless block -1948 107 3027 minecraft:lever[powered=false,facing=west,face=floor] run setblock -1948 107 3027 minecraft:lever[powered=false,facing=west,face=floor] replace
execute unless block -1951 107 3029 minecraft:lever[powered=false,facing=east,face=floor] run setblock -1951 107 3029 minecraft:lever[powered=false,facing=east,face=floor] replace
execute unless block -1948 107 3029 minecraft:lever[powered=false,facing=west,face=floor] run setblock -1948 107 3029 minecraft:lever[powered=false,facing=west,face=floor] replace
execute unless block -1950 107 3030 minecraft:iron_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] run setblock -1950 107 3030 minecraft:iron_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1949 107 3030 minecraft:iron_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] run setblock -1949 107 3030 minecraft:iron_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1947 108 3034 minecraft:light[level=4,waterlogged=false] run setblock -1947 108 3034 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1947 109 3029 minecraft:spruce_fence_gate[powered=false,open=false,facing=west,in_wall=false] run setblock -1947 109 3029 minecraft:spruce_fence_gate[powered=false,open=false,facing=west,in_wall=false] replace
execute unless block -1947 109 3030 minecraft:spruce_fence_gate[powered=false,open=false,facing=west,in_wall=false] run setblock -1947 109 3030 minecraft:spruce_fence_gate[powered=false,open=false,facing=west,in_wall=false] replace
execute unless block -1947 109 3031 minecraft:spruce_fence_gate[powered=false,open=false,facing=west,in_wall=false] run setblock -1947 109 3031 minecraft:spruce_fence_gate[powered=false,open=false,facing=west,in_wall=false] replace
execute unless block -1947 109 3032 minecraft:spruce_fence_gate[powered=false,open=false,facing=west,in_wall=false] run setblock -1947 109 3032 minecraft:spruce_fence_gate[powered=false,open=false,facing=west,in_wall=false] replace
execute unless block -1947 109 3033 minecraft:spruce_fence_gate[powered=false,open=false,facing=west,in_wall=false] run setblock -1947 109 3033 minecraft:spruce_fence_gate[powered=false,open=false,facing=west,in_wall=false] replace
execute unless block -1952 109 3035 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] run setblock -1952 109 3035 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1951 109 3035 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] run setblock -1951 109 3035 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1950 109 3035 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] run setblock -1950 109 3035 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1949 109 3035 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=bottom] run setblock -1949 109 3035 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=bottom] replace
execute unless block -1948 109 3035 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] run setblock -1948 109 3035 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1947 109 3035 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] run setblock -1947 109 3035 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1950 112 3024 minecraft:light[level=4,waterlogged=false] run setblock -1950 112 3024 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1944 112 3024 minecraft:light[level=4,waterlogged=false] run setblock -1944 112 3024 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1947 112 3034 minecraft:light[level=4,waterlogged=false] run setblock -1947 112 3034 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1945 112 3034 minecraft:light[level=4,waterlogged=false] run setblock -1945 112 3034 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1948 116 3024 minecraft:light[level=4,waterlogged=false] run setblock -1948 116 3024 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1984 93 3017 minecraft:oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] run setblock -1984 93 3017 minecraft:oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] replace
execute unless block -1983 93 3017 minecraft:oak_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] run setblock -1983 93 3017 minecraft:oak_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] replace
execute unless block -1982 93 3017 minecraft:oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock -1982 93 3017 minecraft:oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block -1977 93 3017 minecraft:light[level=4,waterlogged=false] run setblock -1977 93 3017 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1982 93 3021 minecraft:oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock -1982 93 3021 minecraft:oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block -1976 94 3018 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=top] run setblock -1976 94 3018 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=top] replace
execute unless block -1976 94 3019 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] run setblock -1976 94 3019 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] replace
execute unless block -1976 95 3018 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=top] run setblock -1976 95 3018 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=top] replace
execute unless block -1976 95 3019 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] run setblock -1976 95 3019 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] replace
execute unless block -1976 96 3018 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=top] run setblock -1976 96 3018 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=top] replace
execute unless block -1976 96 3019 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] run setblock -1976 96 3019 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] replace
execute unless block -1977 98 3017 minecraft:light[level=4,waterlogged=false] run setblock -1977 98 3017 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1984 104 3011 minecraft:oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1984 104 3011 minecraft:oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1983 104 3011 minecraft:oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1983 104 3011 minecraft:oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1982 104 3011 minecraft:oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1982 104 3011 minecraft:oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1981 104 3011 minecraft:oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1981 104 3011 minecraft:oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1980 104 3011 minecraft:oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1980 104 3011 minecraft:oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1979 104 3011 minecraft:oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1979 104 3011 minecraft:oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1978 104 3011 minecraft:oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1978 104 3011 minecraft:oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1977 104 3011 minecraft:oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1977 104 3011 minecraft:oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1976 104 3011 minecraft:oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1976 104 3011 minecraft:oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1974 104 3013 minecraft:oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock -1974 104 3013 minecraft:oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block -1974 104 3014 minecraft:oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock -1974 104 3014 minecraft:oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block -1977 104 3016 minecraft:oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock -1977 104 3016 minecraft:oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block -1976 104 3016 minecraft:oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] run setblock -1976 104 3016 minecraft:oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] replace
execute unless block -1977 105 3016 minecraft:oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock -1977 105 3016 minecraft:oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block -1976 105 3016 minecraft:oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] run setblock -1976 105 3016 minecraft:oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] replace
execute unless block -1974 105 3019 minecraft:jungle_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1974 105 3019 minecraft:jungle_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1974 105 3021 minecraft:jungle_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1974 105 3021 minecraft:jungle_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1977 106 3015 minecraft:birch_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1977 106 3015 minecraft:birch_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1976 106 3015 minecraft:birch_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1976 106 3015 minecraft:birch_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1977 107 3015 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] run setblock -1977 107 3015 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] replace
execute unless block -1976 107 3015 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] run setblock -1976 107 3015 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] replace
execute unless block -1973 107 3019 minecraft:iron_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] run setblock -1973 107 3019 minecraft:iron_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] replace
execute unless block -1973 107 3020 minecraft:iron_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] run setblock -1973 107 3020 minecraft:iron_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] replace
execute unless block -1973 107 3021 minecraft:iron_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] run setblock -1973 107 3021 minecraft:iron_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] replace
execute unless block -1982 107 3023 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] run setblock -1982 107 3023 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] replace
execute unless block -1976 107 3023 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] run setblock -1976 107 3023 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] replace
execute unless block -1977 108 3015 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock -1977 108 3015 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block -1976 108 3015 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] run setblock -1976 108 3015 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] replace
execute unless block -1982 108 3023 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1982 108 3023 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1976 108 3023 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] run setblock -1976 108 3023 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] replace
execute unless block -1977 109 3015 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] run setblock -1977 109 3015 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] replace
execute unless block -1976 109 3015 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] run setblock -1976 109 3015 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] replace
execute unless block -1982 109 3023 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1982 109 3023 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1976 109 3023 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] run setblock -1976 109 3023 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] replace
execute unless block -1977 110 3015 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] run setblock -1977 110 3015 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] replace
execute unless block -1976 110 3015 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] run setblock -1976 110 3015 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] replace
execute unless block -1982 110 3023 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1982 110 3023 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1976 110 3023 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1976 110 3023 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1984 111 3010 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -1984 111 3010 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -1983 111 3010 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -1983 111 3010 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -1982 111 3010 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -1982 111 3010 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -1981 111 3010 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -1981 111 3010 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -1980 111 3010 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -1980 111 3010 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -1979 111 3010 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -1979 111 3010 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -1978 111 3010 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -1978 111 3010 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -1977 111 3010 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -1977 111 3010 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -1976 111 3010 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -1976 111 3010 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -1975 111 3010 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -1975 111 3010 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -1974 111 3017 minecraft:light[level=4,waterlogged=false] run setblock -1974 111 3017 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1974 111 3023 minecraft:light[level=4,waterlogged=false] run setblock -1974 111 3023 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1976 113 3010 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] run setblock -1976 113 3010 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1976 113 3011 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] run setblock -1976 113 3011 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1981 114 3016 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1981 114 3016 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1980 114 3016 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1980 114 3016 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1979 114 3016 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1979 114 3016 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1978 114 3016 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1978 114 3016 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1981 114 3020 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1981 114 3020 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1980 114 3020 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1980 114 3020 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1979 114 3020 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1979 114 3020 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1978 114 3020 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1978 114 3020 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1980 114 3022 minecraft:light[level=4,waterlogged=false] run setblock -1980 114 3022 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1984 114 3023 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock -1984 114 3023 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block -1983 114 3023 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] run setblock -1983 114 3023 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] replace
execute unless block -1978 114 3023 minecraft:light[level=4,waterlogged=false] run setblock -1978 114 3023 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1977 114 3023 minecraft:light[level=4,waterlogged=false] run setblock -1977 114 3023 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1977 115 3020 minecraft:potted_mangrove_propagule run setblock -1977 115 3020 minecraft:potted_mangrove_propagule replace
execute unless block -1984 115 3023 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock -1984 115 3023 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block -1983 115 3023 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] run setblock -1983 115 3023 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] replace
execute unless block -1984 116 3023 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock -1984 116 3023 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block -1983 116 3023 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] run setblock -1983 116 3023 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] replace
execute unless block -1984 117 3023 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock -1984 117 3023 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block -1983 117 3023 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] run setblock -1983 117 3023 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] replace
execute unless block -1978 117 3023 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] run setblock -1978 117 3023 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1977 117 3023 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] run setblock -1977 117 3023 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1980 120 3022 minecraft:light[level=4,waterlogged=false] run setblock -1980 120 3022 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1984 121 3016 minecraft:light[level=4,waterlogged=false] run setblock -1984 121 3016 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1980 121 3016 minecraft:light[level=4,waterlogged=false] run setblock -1980 121 3016 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1977 121 3016 minecraft:light[level=4,waterlogged=false] run setblock -1977 121 3016 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1975 131 3019 minecraft:acacia_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=top] run setblock -1975 131 3019 minecraft:acacia_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=top] replace
execute unless block -1976 131 3020 minecraft:acacia_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock -1976 131 3020 minecraft:acacia_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block -1974 131 3020 minecraft:acacia_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] run setblock -1974 131 3020 minecraft:acacia_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] replace
execute unless block -1975 131 3021 minecraft:acacia_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] run setblock -1975 131 3021 minecraft:acacia_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] replace
execute unless block -1975 132 3020 minecraft:acacia_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -1975 132 3020 minecraft:acacia_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -1943 101 3005 minecraft:dark_oak_fence_gate[powered=false,open=false,facing=west,in_wall=false] run setblock -1943 101 3005 minecraft:dark_oak_fence_gate[powered=false,open=false,facing=west,in_wall=false] replace
execute unless block -1941 103 3002 minecraft:dark_oak_fence_gate[powered=false,open=true,facing=west,in_wall=false] run setblock -1941 103 3002 minecraft:dark_oak_fence_gate[powered=false,open=true,facing=west,in_wall=false] replace
execute unless block -1940 105 3001 minecraft:dark_oak_fence_gate[powered=false,open=false,facing=north,in_wall=true] run setblock -1940 105 3001 minecraft:dark_oak_fence_gate[powered=false,open=false,facing=north,in_wall=true] replace
execute unless block -1939 111 3001 minecraft:dark_oak_fence_gate[powered=false,open=false,facing=south,in_wall=true] run setblock -1939 111 3001 minecraft:dark_oak_fence_gate[powered=false,open=false,facing=south,in_wall=true] replace
execute unless block -1942 112 3001 minecraft:dark_oak_fence_gate[powered=false,open=true,facing=east,in_wall=false] run setblock -1942 112 3001 minecraft:dark_oak_fence_gate[powered=false,open=true,facing=east,in_wall=false] replace
execute unless block -1942 116 3005 minecraft:dark_oak_fence_gate[powered=false,open=false,facing=east,in_wall=false] run setblock -1942 116 3005 minecraft:dark_oak_fence_gate[powered=false,open=false,facing=east,in_wall=false] replace
execute unless block -1941 117 3007 minecraft:dark_oak_fence_gate[powered=false,open=false,facing=east,in_wall=true] run setblock -1941 117 3007 minecraft:dark_oak_fence_gate[powered=false,open=false,facing=east,in_wall=true] replace
execute unless block -1944 125 3007 minecraft:dark_oak_fence_gate[powered=false,open=true,facing=east,in_wall=false] run setblock -1944 125 3007 minecraft:dark_oak_fence_gate[powered=false,open=true,facing=east,in_wall=false] replace
execute unless block -1943 127 3006 minecraft:dark_oak_fence_gate[powered=false,open=false,facing=west,in_wall=true] run setblock -1943 127 3006 minecraft:dark_oak_fence_gate[powered=false,open=false,facing=west,in_wall=true] replace
execute unless block -1945 128 3007 minecraft:dark_oak_fence_gate[powered=false,open=false,facing=south,in_wall=false] run setblock -1945 128 3007 minecraft:dark_oak_fence_gate[powered=false,open=false,facing=south,in_wall=false] replace
execute unless block -1944 128 3007 minecraft:dark_oak_fence_gate[powered=false,open=false,facing=north,in_wall=true] run setblock -1944 128 3007 minecraft:dark_oak_fence_gate[powered=false,open=false,facing=north,in_wall=true] replace
execute unless block -1952 104 3047 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -1952 104 3047 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -1952 106 3046 minecraft:iron_door[powered=false,open=false,facing=south,half=lower,hinge=right] run setblock -1952 106 3046 minecraft:iron_door[powered=false,open=false,facing=south,half=lower,hinge=right] replace
execute unless block -1952 107 3046 minecraft:iron_door[powered=false,open=false,facing=south,half=upper,hinge=right] run setblock -1952 107 3046 minecraft:iron_door[powered=false,open=false,facing=south,half=upper,hinge=right] replace
execute unless block -2003 94 3015 minecraft:mangrove_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -2003 94 3015 minecraft:mangrove_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2004 94 3018 minecraft:light[level=4,waterlogged=false] run setblock -2004 94 3018 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2002 94 3018 minecraft:light[level=4,waterlogged=false] run setblock -2002 94 3018 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2003 94 3020 minecraft:light[level=4,waterlogged=false] run setblock -2003 94 3020 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2005 94 3021 minecraft:light[level=4,waterlogged=false] run setblock -2005 94 3021 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2003 95 3016 minecraft:mangrove_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] run setblock -2003 95 3016 minecraft:mangrove_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2007 95 3022 minecraft:mangrove_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=bottom] run setblock -2007 95 3022 minecraft:mangrove_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=bottom] replace
execute unless block -2007 95 3023 minecraft:mangrove_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=bottom] run setblock -2007 95 3023 minecraft:mangrove_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=bottom] replace
execute unless block -2001 95 3023 minecraft:potted_red_mushroom run setblock -2001 95 3023 minecraft:potted_red_mushroom replace
execute unless block -2006 98 3013 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -2006 98 3013 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2006 98 3015 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -2006 98 3015 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2014 100 3019 minecraft:light[level=4,waterlogged=false] run setblock -2014 100 3019 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2013 100 3019 minecraft:light[level=4,waterlogged=false] run setblock -2013 100 3019 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2010 100 3019 minecraft:light[level=4,waterlogged=false] run setblock -2010 100 3019 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2010 100 3023 minecraft:light[level=4,waterlogged=false] run setblock -2010 100 3023 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2014 101 3019 minecraft:iron_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] run setblock -2014 101 3019 minecraft:iron_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] replace
execute unless block -2013 101 3019 minecraft:iron_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] run setblock -2013 101 3019 minecraft:iron_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] replace
execute unless block -2014 102 3021 minecraft:mangrove_fence_gate[powered=false,open=true,facing=west,in_wall=false] run setblock -2014 102 3021 minecraft:mangrove_fence_gate[powered=false,open=true,facing=west,in_wall=false] replace
execute unless block -2013 102 3021 minecraft:mangrove_fence_gate[powered=false,open=true,facing=east,in_wall=false] run setblock -2013 102 3021 minecraft:mangrove_fence_gate[powered=false,open=true,facing=east,in_wall=false] replace
execute unless block -2005 104 3010 minecraft:light[level=4,waterlogged=false] run setblock -2005 104 3010 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2002 104 3010 minecraft:light[level=4,waterlogged=false] run setblock -2002 104 3010 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2005 104 3014 minecraft:light[level=4,waterlogged=false] run setblock -2005 104 3014 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2007 104 3016 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -2007 104 3016 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2016 104 3017 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -2016 104 3017 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2012 104 3017 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -2012 104 3017 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2011 104 3017 minecraft:light[level=4,waterlogged=false] run setblock -2011 104 3017 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2008 104 3017 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -2008 104 3017 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2016 104 3018 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -2016 104 3018 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2012 104 3018 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] run setblock -2012 104 3018 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] replace
execute unless block -2011 104 3018 minecraft:light[level=4,waterlogged=false] run setblock -2011 104 3018 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2008 104 3018 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -2008 104 3018 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2002 104 3023 minecraft:light[level=4,waterlogged=false] run setblock -2002 104 3023 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2004 105 3011 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] run setblock -2004 105 3011 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2004 105 3013 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] run setblock -2004 105 3013 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2015 105 3015 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] run setblock -2015 105 3015 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] replace
execute unless block -2014 105 3015 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock -2014 105 3015 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block -2011 105 3015 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] run setblock -2011 105 3015 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] replace
execute unless block -2010 105 3015 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock -2010 105 3015 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block -2007 105 3016 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -2007 105 3016 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2016 105 3017 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -2016 105 3017 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2012 105 3017 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -2012 105 3017 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2008 105 3017 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -2008 105 3017 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2016 105 3018 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -2016 105 3018 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2012 105 3018 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] run setblock -2012 105 3018 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] replace
execute unless block -2008 105 3018 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -2008 105 3018 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2004 105 3020 minecraft:light[level=4,waterlogged=false] run setblock -2004 105 3020 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2010 105 3021 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] run setblock -2010 105 3021 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] replace
execute unless block -2009 105 3021 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] run setblock -2009 105 3021 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] replace
execute unless block -2007 105 3022 minecraft:light[level=4,waterlogged=false] run setblock -2007 105 3022 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2004 105 3022 minecraft:light[level=4,waterlogged=false] run setblock -2004 105 3022 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2015 106 3015 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] run setblock -2015 106 3015 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] replace
execute unless block -2014 106 3015 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] run setblock -2014 106 3015 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] replace
execute unless block -2011 106 3015 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] run setblock -2011 106 3015 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] replace
execute unless block -2010 106 3015 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock -2010 106 3015 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block -2007 106 3016 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -2007 106 3016 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2016 106 3017 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -2016 106 3017 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2012 106 3017 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -2012 106 3017 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2008 106 3017 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -2008 106 3017 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2016 106 3018 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -2016 106 3018 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2012 106 3018 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -2012 106 3018 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2008 106 3018 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -2008 106 3018 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2014 106 3021 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock -2014 106 3021 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block -2013 106 3021 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] run setblock -2013 106 3021 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] replace
execute unless block -2015 107 3015 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] run setblock -2015 107 3015 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] replace
execute unless block -2014 107 3015 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] run setblock -2014 107 3015 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] replace
execute unless block -2011 107 3015 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] run setblock -2011 107 3015 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] replace
execute unless block -2010 107 3015 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] run setblock -2010 107 3015 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] replace
execute unless block -2007 107 3016 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -2007 107 3016 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2016 107 3017 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -2016 107 3017 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2012 107 3017 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -2012 107 3017 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2008 107 3017 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -2008 107 3017 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2016 107 3018 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -2016 107 3018 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2012 107 3018 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -2012 107 3018 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2008 107 3018 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -2008 107 3018 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2015 108 3015 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] run setblock -2015 108 3015 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] replace
execute unless block -2014 108 3015 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] run setblock -2014 108 3015 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] replace
execute unless block -2011 108 3015 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] run setblock -2011 108 3015 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] replace
execute unless block -2010 108 3015 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] run setblock -2010 108 3015 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] replace
execute unless block -2007 108 3016 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -2007 108 3016 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2008 108 3017 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -2008 108 3017 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2008 108 3018 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -2008 108 3018 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2013 108 3020 minecraft:mangrove_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -2013 108 3020 minecraft:mangrove_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2015 109 3015 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] run setblock -2015 109 3015 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] replace
execute unless block -2014 109 3015 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] run setblock -2014 109 3015 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] replace
execute unless block -2011 109 3015 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] run setblock -2011 109 3015 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] replace
execute unless block -2010 109 3015 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] run setblock -2010 109 3015 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] replace
execute unless block -2007 109 3015 minecraft:potted_wither_rose run setblock -2007 109 3015 minecraft:potted_wither_rose replace
execute unless block -2007 109 3016 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -2007 109 3016 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2015 109 3017 minecraft:light[level=4,waterlogged=false] run setblock -2015 109 3017 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2014 109 3017 minecraft:light[level=4,waterlogged=false] run setblock -2014 109 3017 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2008 109 3017 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -2008 109 3017 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2015 109 3018 minecraft:light[level=4,waterlogged=false] run setblock -2015 109 3018 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2014 109 3018 minecraft:light[level=4,waterlogged=false] run setblock -2014 109 3018 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2008 109 3018 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -2008 109 3018 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2007 109 3023 minecraft:potted_acacia_sapling run setblock -2007 109 3023 minecraft:potted_acacia_sapling replace
execute unless block -2015 110 3015 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] run setblock -2015 110 3015 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] replace
execute unless block -2014 110 3015 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] run setblock -2014 110 3015 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] replace
execute unless block -2011 110 3015 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] run setblock -2011 110 3015 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] replace
execute unless block -2010 110 3015 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] run setblock -2010 110 3015 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] replace
execute unless block -2007 110 3016 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -2007 110 3016 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2007 111 3020 minecraft:jungle_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock -2007 111 3020 minecraft:jungle_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block -2007 111 3021 minecraft:jungle_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock -2007 111 3021 minecraft:jungle_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block -2007 111 3022 minecraft:jungle_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock -2007 111 3022 minecraft:jungle_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block -2007 111 3023 minecraft:jungle_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock -2007 111 3023 minecraft:jungle_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block -2016 112 3013 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] run setblock -2016 112 3013 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] replace
execute unless block -2015 112 3013 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] run setblock -2015 112 3013 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] replace
execute unless block -2014 112 3013 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] run setblock -2014 112 3013 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] replace
execute unless block -2013 112 3013 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] run setblock -2013 112 3013 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] replace
execute unless block -2012 112 3013 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] run setblock -2012 112 3013 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] replace
execute unless block -2011 112 3013 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] run setblock -2011 112 3013 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] replace
execute unless block -2016 112 3014 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] run setblock -2016 112 3014 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] replace
execute unless block -2015 112 3014 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] run setblock -2015 112 3014 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] replace
execute unless block -2014 112 3014 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] run setblock -2014 112 3014 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] replace
execute unless block -2013 112 3014 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] run setblock -2013 112 3014 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] replace
execute unless block -2012 112 3014 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] run setblock -2012 112 3014 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] replace
execute unless block -2011 112 3014 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] run setblock -2011 112 3014 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] replace
execute unless block -2007 112 3014 minecraft:light[level=4,waterlogged=false] run setblock -2007 112 3014 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2007 112 3015 minecraft:light[level=4,waterlogged=false] run setblock -2007 112 3015 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2005 112 3016 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -2005 112 3016 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -2011 112 3017 minecraft:light[level=4,waterlogged=false] run setblock -2011 112 3017 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2009 112 3017 minecraft:light[level=4,waterlogged=false] run setblock -2009 112 3017 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2016 113 3012 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] run setblock -2016 113 3012 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] replace
execute unless block -2015 113 3012 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock -2015 113 3012 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block -2014 113 3012 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] run setblock -2014 113 3012 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] replace
execute unless block -2013 113 3012 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock -2013 113 3012 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block -2012 113 3012 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] run setblock -2012 113 3012 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] replace
execute unless block -2011 113 3012 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock -2011 113 3012 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block -2010 113 3013 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -2010 113 3013 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2016 114 3012 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] run setblock -2016 114 3012 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] replace
execute unless block -2015 114 3012 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] run setblock -2015 114 3012 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] replace
execute unless block -2014 114 3012 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] run setblock -2014 114 3012 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] replace
execute unless block -2013 114 3012 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] run setblock -2013 114 3012 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] replace
execute unless block -2012 114 3012 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] run setblock -2012 114 3012 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] replace
execute unless block -2011 114 3012 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] run setblock -2011 114 3012 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] replace
execute unless block -2010 114 3013 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] run setblock -2010 114 3013 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] replace
execute unless block -2012 114 3014 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] run setblock -2012 114 3014 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] replace
execute unless block -2010 114 3014 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -2010 114 3014 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2014 114 3015 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] run setblock -2014 114 3015 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] replace
execute unless block -2013 114 3015 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] run setblock -2013 114 3015 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] replace
execute unless block -2012 114 3015 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] run setblock -2012 114 3015 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] replace
execute unless block -2007 114 3016 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] run setblock -2007 114 3016 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] replace
execute unless block -2006 114 3016 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock -2006 114 3016 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block -2016 114 3017 minecraft:light[level=4,waterlogged=false] run setblock -2016 114 3017 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2008 114 3017 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -2008 114 3017 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2005 114 3017 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -2005 114 3017 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2008 114 3018 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -2008 114 3018 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2005 114 3018 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -2005 114 3018 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2016 114 3019 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -2016 114 3019 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2015 114 3019 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -2015 114 3019 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2014 114 3019 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -2014 114 3019 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2005 114 3019 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -2005 114 3019 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2005 114 3020 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -2005 114 3020 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2010 114 3021 minecraft:light[level=4,waterlogged=false] run setblock -2010 114 3021 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2009 114 3021 minecraft:light[level=4,waterlogged=false] run setblock -2009 114 3021 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2005 114 3021 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -2005 114 3021 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2005 114 3022 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -2005 114 3022 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2005 114 3023 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -2005 114 3023 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2016 115 3012 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] run setblock -2016 115 3012 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] replace
execute unless block -2015 115 3012 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] run setblock -2015 115 3012 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] replace
execute unless block -2014 115 3012 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] run setblock -2014 115 3012 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] replace
execute unless block -2013 115 3012 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] run setblock -2013 115 3012 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] replace
execute unless block -2012 115 3012 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] run setblock -2012 115 3012 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] replace
execute unless block -2011 115 3012 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] run setblock -2011 115 3012 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] replace
execute unless block -2010 115 3013 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] run setblock -2010 115 3013 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2012 115 3014 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] run setblock -2012 115 3014 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] replace
execute unless block -2010 115 3014 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] run setblock -2010 115 3014 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2014 115 3015 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] run setblock -2014 115 3015 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] replace
execute unless block -2013 115 3015 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] run setblock -2013 115 3015 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] replace
execute unless block -2012 115 3015 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] run setblock -2012 115 3015 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] replace
execute unless block -2007 115 3016 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] run setblock -2007 115 3016 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] replace
execute unless block -2006 115 3016 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] run setblock -2006 115 3016 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] replace
execute unless block -2008 115 3017 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -2008 115 3017 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2005 115 3017 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] run setblock -2005 115 3017 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2008 115 3018 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -2008 115 3018 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2005 115 3018 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] run setblock -2005 115 3018 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2005 115 3019 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] run setblock -2005 115 3019 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2005 115 3020 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] run setblock -2005 115 3020 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2005 115 3021 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] run setblock -2005 115 3021 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2005 115 3022 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] run setblock -2005 115 3022 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2005 115 3023 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] run setblock -2005 115 3023 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2012 116 3014 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] run setblock -2012 116 3014 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] replace
execute unless block -2014 116 3015 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] run setblock -2014 116 3015 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] replace
execute unless block -2013 116 3015 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] run setblock -2013 116 3015 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] replace
execute unless block -2012 116 3015 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] run setblock -2012 116 3015 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] replace
execute unless block -2014 116 3016 minecraft:dark_oak_fence_gate[powered=false,open=true,facing=west,in_wall=false] run setblock -2014 116 3016 minecraft:dark_oak_fence_gate[powered=false,open=true,facing=west,in_wall=false] replace
execute unless block -2012 116 3016 minecraft:dark_oak_fence_gate[powered=false,open=true,facing=east,in_wall=false] run setblock -2012 116 3016 minecraft:dark_oak_fence_gate[powered=false,open=true,facing=east,in_wall=false] replace
execute unless block -2008 116 3017 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -2008 116 3017 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2008 116 3018 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -2008 116 3018 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2010 116 3023 minecraft:light[level=4,waterlogged=false] run setblock -2010 116 3023 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2009 116 3023 minecraft:light[level=4,waterlogged=false] run setblock -2009 116 3023 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2012 117 3014 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] run setblock -2012 117 3014 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] replace
execute unless block -2007 117 3014 minecraft:light[level=4,waterlogged=false] run setblock -2007 117 3014 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2014 117 3015 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] run setblock -2014 117 3015 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] replace
execute unless block -2013 117 3015 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] run setblock -2013 117 3015 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] replace
execute unless block -2012 117 3015 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] run setblock -2012 117 3015 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] replace
execute unless block -2007 117 3015 minecraft:light[level=4,waterlogged=false] run setblock -2007 117 3015 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2008 117 3017 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -2008 117 3017 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2008 117 3018 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -2008 117 3018 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2007 117 3023 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -2007 117 3023 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -2006 118 3021 minecraft:spruce_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] run setblock -2006 118 3021 minecraft:spruce_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] replace
execute unless block -2006 118 3022 minecraft:spruce_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] run setblock -2006 118 3022 minecraft:spruce_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] replace
execute unless block -2004 120 3016 minecraft:spruce_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] run setblock -2004 120 3016 minecraft:spruce_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] replace
execute unless block -2001 120 3016 minecraft:spruce_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] run setblock -2001 120 3016 minecraft:spruce_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] replace
execute unless block -2016 120 3021 minecraft:iron_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] run setblock -2016 120 3021 minecraft:iron_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] replace
execute unless block -2011 120 3021 minecraft:light[level=4,waterlogged=false] run setblock -2011 120 3021 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2011 120 3022 minecraft:light[level=4,waterlogged=false] run setblock -2011 120 3022 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2012 121 3018 minecraft:light[level=4,waterlogged=false] run setblock -2012 121 3018 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2009 121 3018 minecraft:light[level=4,waterlogged=false] run setblock -2009 121 3018 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2016 121 3019 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] run setblock -2016 121 3019 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2016 121 3020 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] run setblock -2016 121 3020 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2015 121 3021 minecraft:iron_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=bottom] run setblock -2015 121 3021 minecraft:iron_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=bottom] replace
execute unless block -2014 121 3021 minecraft:iron_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=bottom] run setblock -2014 121 3021 minecraft:iron_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=bottom] replace
execute unless block -2016 121 3022 minecraft:iron_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] run setblock -2016 121 3022 minecraft:iron_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2016 121 3023 minecraft:iron_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] run setblock -2016 121 3023 minecraft:iron_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2015 125 3022 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] run setblock -2015 125 3022 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] replace
execute unless block -2014 125 3022 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] run setblock -2014 125 3022 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] replace
execute unless block -2015 125 3023 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] run setblock -2015 125 3023 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] replace
execute unless block -2014 125 3023 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] run setblock -2014 125 3023 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] replace
execute unless block -2015 126 3023 minecraft:light[level=4,waterlogged=false] run setblock -2015 126 3023 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2013 126 3023 minecraft:light[level=4,waterlogged=false] run setblock -2013 126 3023 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2020 94 3027 minecraft:light[level=4,waterlogged=false] run setblock -2020 94 3027 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2022 94 3038 minecraft:light[level=4,waterlogged=false] run setblock -2022 94 3038 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2018 95 3028 minecraft:iron_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] run setblock -2018 95 3028 minecraft:iron_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2017 95 3028 minecraft:iron_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] run setblock -2017 95 3028 minecraft:iron_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2020 95 3032 minecraft:iron_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=bottom] run setblock -2020 95 3032 minecraft:iron_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=bottom] replace
execute unless block -2020 95 3033 minecraft:iron_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=bottom] run setblock -2020 95 3033 minecraft:iron_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=bottom] replace
execute unless block -2022 98 3033 minecraft:lever[powered=true,facing=east,face=wall] run setblock -2022 98 3033 minecraft:lever[powered=true,facing=east,face=wall] replace
execute unless block -1982 93 3024 minecraft:oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock -1982 93 3024 minecraft:oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block -2022 99 3024 minecraft:light[level=4,waterlogged=false] run setblock -2022 99 3024 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2018 99 3024 minecraft:light[level=4,waterlogged=false] run setblock -2018 99 3024 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1977 93 3026 minecraft:light[level=4,waterlogged=false] run setblock -1977 93 3026 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2020 100 3028 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -2020 100 3028 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -1976 94 3024 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=top] run setblock -1976 94 3024 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=top] replace
execute unless block -2020 100 3029 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -2020 100 3029 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -1976 94 3025 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] run setblock -1976 94 3025 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] replace
execute unless block -2018 100 3029 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -2018 100 3029 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -1973 94 3033 minecraft:light[level=4,waterlogged=false] run setblock -1973 94 3033 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2020 100 3030 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -2020 100 3030 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -1972 94 3035 minecraft:light[level=4,waterlogged=false] run setblock -1972 94 3035 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2018 100 3030 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -2018 100 3030 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -1971 94 3036 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -1971 94 3036 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -2020 100 3031 minecraft:iron_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] run setblock -2020 100 3031 minecraft:iron_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] replace
execute unless block -1970 94 3036 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -1970 94 3036 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -2018 100 3031 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -2018 100 3031 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -1984 94 3037 minecraft:light[level=4,waterlogged=false] run setblock -1984 94 3037 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2020 100 3032 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -2020 100 3032 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -1979 94 3037 minecraft:light[level=4,waterlogged=false] run setblock -1979 94 3037 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2018 100 3032 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -2018 100 3032 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -1974 94 3037 minecraft:light[level=4,waterlogged=false] run setblock -1974 94 3037 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2020 100 3033 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -2020 100 3033 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -1984 94 3038 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -1984 94 3038 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -2018 100 3033 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -2018 100 3033 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -1979 94 3038 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -1979 94 3038 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -2020 100 3034 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -2020 100 3034 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -1977 94 3038 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -1977 94 3038 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -2018 100 3034 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -2018 100 3034 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -1976 94 3038 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -1976 94 3038 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -2020 100 3035 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -2020 100 3035 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -1975 94 3038 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -1975 94 3038 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -2018 100 3035 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -2018 100 3035 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -1974 94 3038 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -1974 94 3038 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -2020 100 3036 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -2020 100 3036 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -1976 95 3024 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1976 95 3024 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2018 100 3036 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -2018 100 3036 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -1976 95 3025 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1976 95 3025 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2021 100 3038 minecraft:acacia_fence_gate[powered=false,open=true,facing=south,in_wall=false] run setblock -2021 100 3038 minecraft:acacia_fence_gate[powered=false,open=true,facing=south,in_wall=false] replace
execute unless block -1975 95 3025 minecraft:lantern[hanging=false,waterlogged=false] run setblock -1975 95 3025 minecraft:lantern[hanging=false,waterlogged=false] replace
execute unless block -2021 101 3027 minecraft:light[level=4,waterlogged=false] run setblock -2021 101 3027 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2018 101 3028 minecraft:light[level=4,waterlogged=false] run setblock -2018 101 3028 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1973 95 3032 minecraft:lever[powered=false,facing=west,face=wall] run setblock -1973 95 3032 minecraft:lever[powered=false,facing=west,face=wall] replace
execute unless block -2021 101 3037 minecraft:light[level=4,waterlogged=false] run setblock -2021 101 3037 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1973 95 3033 minecraft:light[level=4,waterlogged=false] run setblock -1973 95 3033 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2017 101 3037 minecraft:light[level=4,waterlogged=false] run setblock -2017 101 3037 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1982 95 3036 minecraft:iron_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] run setblock -1982 95 3036 minecraft:iron_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] replace
execute unless block -2020 104 3028 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] run setblock -2020 104 3028 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] replace
execute unless block -1981 95 3036 minecraft:iron_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] run setblock -1981 95 3036 minecraft:iron_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] replace
execute unless block -2023 104 3032 minecraft:ladder[facing=east,waterlogged=false] run setblock -2023 104 3032 minecraft:ladder[facing=east,waterlogged=false] replace
execute unless block -1971 95 3036 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -1971 95 3036 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -2020 104 3032 minecraft:light[level=4,waterlogged=false] run setblock -2020 104 3032 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1970 95 3036 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -1970 95 3036 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -2021 104 3038 minecraft:oak_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -2021 104 3038 minecraft:oak_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -1984 95 3038 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -1984 95 3038 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -2020 104 3038 minecraft:oak_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -2020 104 3038 minecraft:oak_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -1976 96 3024 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=top] run setblock -1976 96 3024 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=top] replace
execute unless block -2019 104 3038 minecraft:oak_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -2019 104 3038 minecraft:oak_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -1976 96 3025 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] run setblock -1976 96 3025 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] replace
execute unless block -2020 105 3028 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] run setblock -2020 105 3028 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] replace
execute unless block -1982 96 3030 minecraft:light[level=4,waterlogged=false] run setblock -1982 96 3030 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2018 105 3029 minecraft:light[level=4,waterlogged=false] run setblock -2018 105 3029 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1979 96 3030 minecraft:light[level=4,waterlogged=false] run setblock -1979 96 3030 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2019 105 3031 minecraft:flower_pot run setblock -2019 105 3031 minecraft:flower_pot replace
execute unless block -1973 96 3032 minecraft:lever[powered=false,facing=west,face=wall] run setblock -1973 96 3032 minecraft:lever[powered=false,facing=west,face=wall] replace
execute unless block -2023 105 3032 minecraft:ladder[facing=east,waterlogged=false] run setblock -2023 105 3032 minecraft:ladder[facing=east,waterlogged=false] replace
execute unless block -1973 96 3033 minecraft:light[level=4,waterlogged=false] run setblock -1973 96 3033 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2022 105 3038 minecraft:light[level=4,waterlogged=false] run setblock -2022 105 3038 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1971 96 3036 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -1971 96 3036 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -2021 105 3038 minecraft:light[level=4,waterlogged=false] run setblock -2021 105 3038 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1970 96 3036 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -1970 96 3036 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -2020 105 3038 minecraft:light[level=4,waterlogged=false] run setblock -2020 105 3038 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1984 96 3038 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -1984 96 3038 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -2019 105 3038 minecraft:light[level=4,waterlogged=false] run setblock -2019 105 3038 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1979 96 3038 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -1979 96 3038 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -2018 105 3038 minecraft:light[level=4,waterlogged=false] run setblock -2018 105 3038 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1978 96 3038 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -1978 96 3038 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -2020 106 3028 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] run setblock -2020 106 3028 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] replace
execute unless block -1977 96 3038 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -1977 96 3038 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -2019 106 3028 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock -2019 106 3028 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block -1976 96 3038 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -1976 96 3038 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -1975 96 3038 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -1975 96 3038 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -2018 106 3028 minecraft:oak_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] run setblock -2018 106 3028 minecraft:oak_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1974 96 3038 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -1974 96 3038 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -2017 106 3028 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] run setblock -2017 106 3028 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] replace
execute unless block -1973 97 3033 minecraft:light[level=4,waterlogged=false] run setblock -1973 97 3033 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2023 106 3032 minecraft:ladder[facing=east,waterlogged=false] run setblock -2023 106 3032 minecraft:ladder[facing=east,waterlogged=false] replace
execute unless block -1972 98 3025 minecraft:light[level=4,waterlogged=false] run setblock -1972 98 3025 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2020 107 3028 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] run setblock -2020 107 3028 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] replace
execute unless block -1977 98 3026 minecraft:light[level=4,waterlogged=false] run setblock -1977 98 3026 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2019 107 3028 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock -2019 107 3028 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block -1971 98 3031 minecraft:light[level=4,waterlogged=false] run setblock -1971 98 3031 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2017 107 3028 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] run setblock -2017 107 3028 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] replace
execute unless block -1971 98 3036 minecraft:light[level=4,waterlogged=false] run setblock -1971 98 3036 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2023 107 3032 minecraft:ladder[facing=east,waterlogged=false] run setblock -2023 107 3032 minecraft:ladder[facing=east,waterlogged=false] replace
execute unless block -1979 98 3038 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] run setblock -1979 98 3038 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] replace
execute unless block -2020 108 3028 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] run setblock -2020 108 3028 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] replace
execute unless block -1978 98 3038 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -1978 98 3038 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -2019 108 3028 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock -2019 108 3028 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block -1977 98 3038 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -1977 98 3038 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -2017 108 3028 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] run setblock -2017 108 3028 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] replace
execute unless block -1976 98 3038 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -1976 98 3038 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -2023 108 3032 minecraft:ladder[facing=east,waterlogged=false] run setblock -2023 108 3032 minecraft:ladder[facing=east,waterlogged=false] replace
execute unless block -1975 98 3038 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -1975 98 3038 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -2024 109 3028 minecraft:light[level=4,waterlogged=false] run setblock -2024 109 3028 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1974 98 3038 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -1974 98 3038 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -2023 109 3028 minecraft:light[level=4,waterlogged=false] run setblock -2023 109 3028 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1983 100 3032 minecraft:light[level=4,waterlogged=false] run setblock -1983 100 3032 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2020 109 3028 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] run setblock -2020 109 3028 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] replace
execute unless block -1979 100 3032 minecraft:light[level=4,waterlogged=false] run setblock -1979 100 3032 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2019 109 3028 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock -2019 109 3028 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block -1974 100 3032 minecraft:light[level=4,waterlogged=false] run setblock -1974 100 3032 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2017 109 3028 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] run setblock -2017 109 3028 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] replace
execute unless block -1979 100 3038 minecraft:light[level=4,waterlogged=false] run setblock -1979 100 3038 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2023 109 3032 minecraft:ladder[facing=east,waterlogged=false] run setblock -2023 109 3032 minecraft:ladder[facing=east,waterlogged=false] replace
execute unless block -1974 100 3038 minecraft:light[level=4,waterlogged=false] run setblock -1974 100 3038 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2024 110 3027 minecraft:lever[powered=false,facing=north,face=ceiling] run setblock -2024 110 3027 minecraft:lever[powered=false,facing=north,face=ceiling] replace
execute unless block -1975 102 3025 minecraft:light[level=4,waterlogged=false] run setblock -1975 102 3025 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2023 110 3027 minecraft:lever[powered=false,facing=north,face=ceiling] run setblock -2023 110 3027 minecraft:lever[powered=false,facing=north,face=ceiling] replace
execute unless block -1972 102 3025 minecraft:light[level=4,waterlogged=false] run setblock -1972 102 3025 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2024 110 3029 minecraft:lever[powered=false,facing=south,face=ceiling] run setblock -2024 110 3029 minecraft:lever[powered=false,facing=south,face=ceiling] replace
execute unless block -1972 104 3024 minecraft:iron_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] run setblock -1972 104 3024 minecraft:iron_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] replace
execute unless block -2023 110 3029 minecraft:lever[powered=false,facing=south,face=ceiling] run setblock -2023 110 3029 minecraft:lever[powered=false,facing=south,face=ceiling] replace
execute unless block -1971 104 3024 minecraft:iron_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] run setblock -1971 104 3024 minecraft:iron_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] replace
execute unless block -2020 111 3029 minecraft:light[level=4,waterlogged=false] run setblock -2020 111 3029 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1970 104 3024 minecraft:iron_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] run setblock -1970 104 3024 minecraft:iron_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] replace
execute unless block -2024 111 3032 minecraft:light[level=4,waterlogged=false] run setblock -2024 111 3032 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1981 104 3026 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1981 104 3026 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2024 111 3038 minecraft:light[level=4,waterlogged=false] run setblock -2024 111 3038 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1980 104 3026 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1980 104 3026 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2018 114 3024 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -2018 114 3024 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1981 104 3027 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1981 104 3027 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2017 114 3024 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -2017 114 3024 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1980 104 3027 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1980 104 3027 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2024 114 3025 minecraft:light[level=4,waterlogged=false] run setblock -2024 114 3025 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1973 104 3028 minecraft:oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1973 104 3028 minecraft:oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2019 114 3025 minecraft:light[level=4,waterlogged=false] run setblock -2019 114 3025 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1972 104 3028 minecraft:oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1972 104 3028 minecraft:oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2019 114 3029 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -2019 114 3029 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1971 104 3029 minecraft:oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock -1971 104 3029 minecraft:oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block -2024 114 3030 minecraft:light[level=4,waterlogged=false] run setblock -2024 114 3030 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1971 104 3030 minecraft:oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock -1971 104 3030 minecraft:oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block -2019 114 3030 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -2019 114 3030 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1976 104 3031 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1976 104 3031 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1971 104 3031 minecraft:oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock -1971 104 3031 minecraft:oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block -1971 104 3032 minecraft:oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock -1971 104 3032 minecraft:oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block -1976 104 3034 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1976 104 3034 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1973 104 3034 minecraft:light[level=4,waterlogged=false] run setblock -1973 104 3034 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1971 104 3034 minecraft:light[level=4,waterlogged=false] run setblock -1971 104 3034 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1976 104 3035 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1976 104 3035 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2023 114 3031 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock -2023 114 3031 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block -1982 104 3037 minecraft:iron_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] run setblock -1982 104 3037 minecraft:iron_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] replace
execute unless block -2022 114 3031 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] run setblock -2022 114 3031 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] replace
execute unless block -1981 104 3037 minecraft:iron_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] run setblock -1981 104 3037 minecraft:iron_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] replace
execute unless block -2021 114 3032 minecraft:light[level=4,waterlogged=false] run setblock -2021 114 3032 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1982 104 3038 minecraft:iron_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] run setblock -1982 104 3038 minecraft:iron_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] replace
execute unless block -2023 114 3036 minecraft:warped_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] run setblock -2023 114 3036 minecraft:warped_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1981 104 3038 minecraft:iron_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] run setblock -1981 104 3038 minecraft:iron_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] replace
execute unless block -2022 114 3036 minecraft:warped_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] run setblock -2022 114 3036 minecraft:warped_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1972 105 3024 minecraft:iron_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] run setblock -1972 105 3024 minecraft:iron_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] replace
execute unless block -2019 115 3025 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] run setblock -2019 115 3025 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] replace
execute unless block -1971 105 3024 minecraft:iron_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] run setblock -1971 105 3024 minecraft:iron_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] replace
execute unless block -2018 115 3026 minecraft:composter[level=0] run setblock -2018 115 3026 minecraft:composter[level=0] replace
execute unless block -1970 105 3024 minecraft:iron_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] run setblock -1970 105 3024 minecraft:iron_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] replace
execute unless block -2017 115 3026 minecraft:composter[level=0] run setblock -2017 115 3026 minecraft:composter[level=0] replace
execute unless block -1981 105 3026 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1981 105 3026 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2019 115 3029 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=top] run setblock -2019 115 3029 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=top] replace
execute unless block -1980 105 3026 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1980 105 3026 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2019 115 3030 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] run setblock -2019 115 3030 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] replace
execute unless block -1981 105 3027 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1981 105 3027 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2023 115 3031 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock -2023 115 3031 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block -1980 105 3027 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1980 105 3027 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2022 115 3031 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] run setblock -2022 115 3031 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] replace
execute unless block -1976 105 3031 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1976 105 3031 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2019 115 3032 minecraft:dark_oak_fence_gate[powered=false,open=false,facing=north,in_wall=false] run setblock -2019 115 3032 minecraft:dark_oak_fence_gate[powered=false,open=false,facing=north,in_wall=false] replace
execute unless block -1976 105 3034 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1976 105 3034 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2018 115 3032 minecraft:dark_oak_fence_gate[powered=false,open=false,facing=north,in_wall=false] run setblock -2018 115 3032 minecraft:dark_oak_fence_gate[powered=false,open=false,facing=north,in_wall=false] replace
execute unless block -1976 105 3035 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1976 105 3035 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2019 115 3034 minecraft:light[level=4,waterlogged=false] run setblock -2019 115 3034 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1983 105 3037 minecraft:iron_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] run setblock -1983 105 3037 minecraft:iron_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] replace
execute unless block -2019 115 3035 minecraft:light[level=4,waterlogged=false] run setblock -2019 115 3035 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1983 105 3038 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] run setblock -1983 105 3038 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2019 115 3036 minecraft:light[level=4,waterlogged=false] run setblock -2019 115 3036 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1972 106 3024 minecraft:iron_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] run setblock -1972 106 3024 minecraft:iron_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] replace
execute unless block -2024 115 3038 minecraft:lever[powered=false,facing=west,face=floor] run setblock -2024 115 3038 minecraft:lever[powered=false,facing=west,face=floor] replace
execute unless block -1971 106 3024 minecraft:iron_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] run setblock -1971 106 3024 minecraft:iron_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] replace
execute unless block -2021 115 3038 minecraft:light[level=4,waterlogged=false] run setblock -2021 115 3038 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1970 106 3024 minecraft:iron_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] run setblock -1970 106 3024 minecraft:iron_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] replace
execute unless block -2018 116 3028 minecraft:light[level=4,waterlogged=false] run setblock -2018 116 3028 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1981 106 3026 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1981 106 3026 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1980 106 3026 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1980 106 3026 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2017 116 3028 minecraft:light[level=4,waterlogged=false] run setblock -2017 116 3028 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1981 106 3027 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1981 106 3027 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2019 116 3029 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -2019 116 3029 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1980 106 3027 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1980 106 3027 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2019 116 3030 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -2019 116 3030 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1981 106 3028 minecraft:iron_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] run setblock -1981 106 3028 minecraft:iron_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] replace
execute unless block -2023 116 3031 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] run setblock -2023 116 3031 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] replace
execute unless block -1981 106 3029 minecraft:iron_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] run setblock -1981 106 3029 minecraft:iron_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] replace
execute unless block -2022 116 3031 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] run setblock -2022 116 3031 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] replace
execute unless block -1976 106 3031 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1976 106 3031 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2024 117 3029 minecraft:potted_spruce_sapling run setblock -2024 117 3029 minecraft:potted_spruce_sapling replace
execute unless block -1981 106 3033 minecraft:iron_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] run setblock -1981 106 3033 minecraft:iron_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] replace
execute unless block -2019 117 3029 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -2019 117 3029 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1981 106 3034 minecraft:iron_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] run setblock -1981 106 3034 minecraft:iron_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] replace
execute unless block -2019 117 3030 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -2019 117 3030 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1976 106 3034 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1976 106 3034 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2023 117 3031 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock -2023 117 3031 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block -1981 106 3035 minecraft:light[level=4,waterlogged=false] run setblock -1981 106 3035 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2022 117 3031 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] run setblock -2022 117 3031 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] replace
execute unless block -1976 106 3035 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1976 106 3035 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2019 117 3032 minecraft:light[level=4,waterlogged=false] run setblock -2019 117 3032 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1972 107 3024 minecraft:iron_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] run setblock -1972 107 3024 minecraft:iron_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] replace
execute unless block -2018 117 3032 minecraft:light[level=4,waterlogged=false] run setblock -2018 117 3032 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1971 107 3024 minecraft:iron_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] run setblock -1971 107 3024 minecraft:iron_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] replace
execute unless block -2024 118 3032 minecraft:oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock -2024 118 3032 minecraft:oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block -1970 107 3024 minecraft:iron_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] run setblock -1970 107 3024 minecraft:iron_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] replace
execute unless block -2024 118 3033 minecraft:oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock -2024 118 3033 minecraft:oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block -1981 107 3026 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1981 107 3026 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2020 119 3027 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] run setblock -2020 119 3027 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] replace
execute unless block -1980 107 3026 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1980 107 3026 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2020 119 3029 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] run setblock -2020 119 3029 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] replace
execute unless block -1974 107 3026 minecraft:lantern[hanging=true,waterlogged=false] run setblock -1974 107 3026 minecraft:lantern[hanging=true,waterlogged=false] replace
execute unless block -2017 119 3029 minecraft:light[level=4,waterlogged=false] run setblock -2017 119 3029 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1981 107 3027 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1981 107 3027 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2019 119 3032 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] run setblock -2019 119 3032 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1980 107 3027 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1980 107 3027 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2018 119 3032 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] run setblock -2018 119 3032 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1978 107 3027 minecraft:lantern[hanging=true,waterlogged=false] run setblock -1978 107 3027 minecraft:lantern[hanging=true,waterlogged=false] replace
execute unless block -2017 119 3032 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] run setblock -2017 119 3032 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1976 107 3031 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] run setblock -1976 107 3031 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] replace
execute unless block -2020 120 3029 minecraft:potted_dark_oak_sapling run setblock -2020 120 3029 minecraft:potted_dark_oak_sapling replace
execute unless block -1978 107 3033 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1978 107 3033 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2024 120 3030 minecraft:light[level=4,waterlogged=false] run setblock -2024 120 3030 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1976 107 3034 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=top] run setblock -1976 107 3034 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=top] replace
execute unless block -2018 121 3026 minecraft:composter[level=0] run setblock -2018 121 3026 minecraft:composter[level=0] replace
execute unless block -1976 107 3035 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] run setblock -1976 107 3035 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] replace
execute unless block -2017 121 3026 minecraft:composter[level=0] run setblock -2017 121 3026 minecraft:composter[level=0] replace
execute unless block -1983 107 3037 minecraft:iron_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] run setblock -1983 107 3037 minecraft:iron_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] replace
execute unless block -2018 121 3032 minecraft:light[level=4,waterlogged=false] run setblock -2018 121 3032 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1983 107 3038 minecraft:iron_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] run setblock -1983 107 3038 minecraft:iron_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] replace
execute unless block -2018 121 3038 minecraft:light[level=4,waterlogged=false] run setblock -2018 121 3038 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1972 108 3024 minecraft:iron_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] run setblock -1972 108 3024 minecraft:iron_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] replace
execute unless block -2021 123 3032 minecraft:light[level=4,waterlogged=false] run setblock -2021 123 3032 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1971 108 3024 minecraft:iron_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] run setblock -1971 108 3024 minecraft:iron_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] replace
execute unless block -2021 123 3035 minecraft:light[level=4,waterlogged=false] run setblock -2021 123 3035 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1970 108 3024 minecraft:iron_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] run setblock -1970 108 3024 minecraft:iron_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] replace
execute unless block -2021 123 3038 minecraft:light[level=4,waterlogged=false] run setblock -2021 123 3038 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1981 108 3026 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1981 108 3026 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2020 124 3026 minecraft:light[level=4,waterlogged=false] run setblock -2020 124 3026 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1981 108 3027 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1981 108 3027 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2020 124 3030 minecraft:light[level=4,waterlogged=false] run setblock -2020 124 3030 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1976 108 3031 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1976 108 3031 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2023 125 3024 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] run setblock -2023 125 3024 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] replace
execute unless block -1978 108 3033 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1978 108 3033 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2022 125 3024 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] run setblock -2022 125 3024 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] replace
execute unless block -1976 108 3034 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=top] run setblock -1976 108 3034 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=top] replace
execute unless block -1969 108 3034 minecraft:light[level=4,waterlogged=false] run setblock -1969 108 3034 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1976 108 3035 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] run setblock -1976 108 3035 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] replace
execute unless block -1969 109 3029 minecraft:spruce_fence_gate[powered=false,open=false,facing=west,in_wall=false] run setblock -1969 109 3029 minecraft:spruce_fence_gate[powered=false,open=false,facing=west,in_wall=false] replace
execute unless block -1969 109 3030 minecraft:spruce_fence_gate[powered=false,open=false,facing=west,in_wall=false] run setblock -1969 109 3030 minecraft:spruce_fence_gate[powered=false,open=false,facing=west,in_wall=false] replace
execute unless block -1969 109 3031 minecraft:spruce_fence_gate[powered=false,open=false,facing=west,in_wall=false] run setblock -1969 109 3031 minecraft:spruce_fence_gate[powered=false,open=false,facing=west,in_wall=false] replace
execute unless block -2019 125 3024 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] run setblock -2019 125 3024 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] replace
execute unless block -1969 109 3032 minecraft:spruce_fence_gate[powered=false,open=false,facing=west,in_wall=false] run setblock -1969 109 3032 minecraft:spruce_fence_gate[powered=false,open=false,facing=west,in_wall=false] replace
execute unless block -2018 125 3024 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] run setblock -2018 125 3024 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] replace
execute unless block -1969 109 3033 minecraft:spruce_fence_gate[powered=false,open=false,facing=west,in_wall=false] run setblock -1969 109 3033 minecraft:spruce_fence_gate[powered=false,open=false,facing=west,in_wall=false] replace
execute unless block -1981 109 3035 minecraft:light[level=4,waterlogged=false] run setblock -1981 109 3035 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1969 109 3035 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] run setblock -1969 109 3035 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1982 109 3037 minecraft:iron_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] run setblock -1982 109 3037 minecraft:iron_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1981 109 3037 minecraft:iron_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] run setblock -1981 109 3037 minecraft:iron_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1982 109 3038 minecraft:iron_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] run setblock -1982 109 3038 minecraft:iron_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1981 109 3038 minecraft:iron_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] run setblock -1981 109 3038 minecraft:iron_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1972 112 3024 minecraft:light[level=4,waterlogged=false] run setblock -1972 112 3024 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1973 112 3028 minecraft:light[level=4,waterlogged=false] run setblock -1973 112 3028 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1973 112 3034 minecraft:light[level=4,waterlogged=false] run setblock -1973 112 3034 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1971 112 3034 minecraft:light[level=4,waterlogged=false] run setblock -1971 112 3034 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1969 112 3034 minecraft:light[level=4,waterlogged=false] run setblock -1969 112 3034 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1980 114 3028 minecraft:light[level=4,waterlogged=false] run setblock -1980 114 3028 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1984 114 3031 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock -1984 114 3031 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block -1983 114 3031 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] run setblock -1983 114 3031 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] replace
execute unless block -1978 114 3031 minecraft:light[level=4,waterlogged=false] run setblock -1978 114 3031 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1977 114 3031 minecraft:light[level=4,waterlogged=false] run setblock -1977 114 3031 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1980 114 3034 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1980 114 3034 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1979 114 3034 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1979 114 3034 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1978 114 3034 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1978 114 3034 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1977 114 3034 minecraft:birch_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1977 114 3034 minecraft:birch_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1980 114 3038 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1980 114 3038 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1979 114 3038 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1979 114 3038 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1978 114 3038 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1978 114 3038 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1977 114 3038 minecraft:birch_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1977 114 3038 minecraft:birch_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1981 115 3029 minecraft:iron_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] run setblock -1981 115 3029 minecraft:iron_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] replace
execute unless block -1984 115 3031 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock -1984 115 3031 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block -1983 115 3031 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] run setblock -1983 115 3031 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] replace
execute unless block -1982 115 3032 minecraft:spruce_fence_gate[powered=false,open=true,facing=north,in_wall=false] run setblock -1982 115 3032 minecraft:spruce_fence_gate[powered=false,open=true,facing=north,in_wall=false] replace
execute unless block -1981 115 3032 minecraft:spruce_fence_gate[powered=false,open=true,facing=north,in_wall=false] run setblock -1981 115 3032 minecraft:spruce_fence_gate[powered=false,open=true,facing=north,in_wall=false] replace
execute unless block -1980 115 3032 minecraft:spruce_fence_gate[powered=false,open=true,facing=north,in_wall=false] run setblock -1980 115 3032 minecraft:spruce_fence_gate[powered=false,open=true,facing=north,in_wall=false] replace
execute unless block -1984 116 3031 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock -1984 116 3031 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block -1983 116 3031 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] run setblock -1983 116 3031 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] replace
execute unless block -1982 116 3032 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] run setblock -1982 116 3032 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1981 116 3032 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] run setblock -1981 116 3032 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1980 116 3032 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] run setblock -1980 116 3032 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1980 117 3025 minecraft:lever[powered=false,facing=south,face=wall] run setblock -1980 117 3025 minecraft:lever[powered=false,facing=south,face=wall] replace
execute unless block -1984 117 3031 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock -1984 117 3031 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block -1983 117 3031 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] run setblock -1983 117 3031 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] replace
execute unless block -1978 117 3031 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] run setblock -1978 117 3031 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1977 117 3031 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] run setblock -1977 117 3031 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1982 119 3025 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] run setblock -1982 119 3025 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] replace
execute unless block -1980 119 3025 minecraft:light[level=4,waterlogged=false] run setblock -1980 119 3025 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1982 119 3026 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] run setblock -1982 119 3026 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] replace
execute unless block -1982 119 3027 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] run setblock -1982 119 3027 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] replace
execute unless block -1982 119 3028 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] run setblock -1982 119 3028 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] replace
execute unless block -1982 119 3029 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] run setblock -1982 119 3029 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] replace
execute unless block -1980 119 3029 minecraft:light[level=4,waterlogged=false] run setblock -1980 119 3029 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1983 119 3033 minecraft:iron_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] run setblock -1983 119 3033 minecraft:iron_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] replace
execute unless block -1980 119 3034 minecraft:iron_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] run setblock -1980 119 3034 minecraft:iron_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] replace
execute unless block -1982 119 3035 minecraft:iron_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] run setblock -1982 119 3035 minecraft:iron_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] replace
execute unless block -1984 119 3036 minecraft:iron_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] run setblock -1984 119 3036 minecraft:iron_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] replace
execute unless block -1981 119 3037 minecraft:iron_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] run setblock -1981 119 3037 minecraft:iron_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] replace
execute unless block -1980 120 3032 minecraft:light[level=4,waterlogged=false] run setblock -1980 120 3032 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1982 120 3034 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] run setblock -1982 120 3034 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1983 120 3035 minecraft:iron_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] run setblock -1983 120 3035 minecraft:iron_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] replace
execute unless block -1981 120 3035 minecraft:iron_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=bottom] run setblock -1981 120 3035 minecraft:iron_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=bottom] replace
execute unless block -1982 120 3036 minecraft:iron_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] run setblock -1982 120 3036 minecraft:iron_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1984 121 3038 minecraft:light[level=4,waterlogged=false] run setblock -1984 121 3038 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1980 121 3038 minecraft:light[level=4,waterlogged=false] run setblock -1980 121 3038 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1977 121 3038 minecraft:light[level=4,waterlogged=false] run setblock -1977 121 3038 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1976 122 3024 minecraft:oak_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] run setblock -1976 122 3024 minecraft:oak_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] replace
execute unless block -1976 122 3025 minecraft:oak_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] run setblock -1976 122 3025 minecraft:oak_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] replace
execute unless block -1976 122 3026 minecraft:oak_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] run setblock -1976 122 3026 minecraft:oak_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] replace
execute unless block -1976 122 3027 minecraft:oak_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] run setblock -1976 122 3027 minecraft:oak_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] replace
execute unless block -1982 122 3029 minecraft:light[level=4,waterlogged=false] run setblock -1982 122 3029 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1977 122 3029 minecraft:light[level=4,waterlogged=false] run setblock -1977 122 3029 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1984 122 3030 minecraft:oak_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -1984 122 3030 minecraft:oak_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -1983 122 3030 minecraft:oak_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -1983 122 3030 minecraft:oak_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -1982 122 3030 minecraft:oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock -1982 122 3030 minecraft:oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block -1981 122 3030 minecraft:oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] run setblock -1981 122 3030 minecraft:oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] replace
execute unless block -1980 122 3030 minecraft:oak_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -1980 122 3030 minecraft:oak_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -1979 122 3030 minecraft:oak_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -1979 122 3030 minecraft:oak_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -1978 122 3030 minecraft:oak_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -1978 122 3030 minecraft:oak_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -1977 122 3030 minecraft:oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock -1977 122 3030 minecraft:oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block -1981 123 3030 minecraft:oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] run setblock -1981 123 3030 minecraft:oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] replace
execute unless block -1980 123 3030 minecraft:oak_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -1980 123 3030 minecraft:oak_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -1979 123 3030 minecraft:oak_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -1979 123 3030 minecraft:oak_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -1978 123 3030 minecraft:oak_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -1978 123 3030 minecraft:oak_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -1977 123 3030 minecraft:oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock -1977 123 3030 minecraft:oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block -1976 124 3024 minecraft:oak_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] run setblock -1976 124 3024 minecraft:oak_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] replace
execute unless block -1976 124 3025 minecraft:oak_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] run setblock -1976 124 3025 minecraft:oak_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] replace
execute unless block -1976 124 3026 minecraft:oak_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] run setblock -1976 124 3026 minecraft:oak_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] replace
execute unless block -1982 126 3024 minecraft:light[level=4,waterlogged=false] run setblock -1982 126 3024 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1976 126 3024 minecraft:light[level=4,waterlogged=false] run setblock -1976 126 3024 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2010 100 3040 minecraft:light[level=4,waterlogged=false] run setblock -2010 100 3040 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2009 100 3041 minecraft:light[level=4,waterlogged=false] run setblock -2009 100 3041 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2002 104 3042 minecraft:light[level=4,waterlogged=false] run setblock -2002 104 3042 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2003 106 3042 minecraft:light[level=4,waterlogged=false] run setblock -2003 106 3042 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2001 110 3046 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] run setblock -2001 110 3046 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] replace
execute unless block -2003 111 3042 minecraft:light[level=4,waterlogged=false] run setblock -2003 111 3042 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2016 111 3043 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] run setblock -2016 111 3043 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] replace
execute unless block -2003 112 3042 minecraft:light[level=4,waterlogged=false] run setblock -2003 112 3042 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2002 112 3042 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] run setblock -2002 112 3042 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] replace
execute unless block -2001 112 3042 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] run setblock -2001 112 3042 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] replace
execute unless block -2003 116 3042 minecraft:light[level=4,waterlogged=false] run setblock -2003 116 3042 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2010 128 3041 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -2010 128 3041 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -2012 128 3042 minecraft:iron_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] run setblock -2012 128 3042 minecraft:iron_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] replace
execute unless block -2008 128 3042 minecraft:iron_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] run setblock -2008 128 3042 minecraft:iron_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] replace
execute unless block -2010 128 3043 minecraft:iron_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] run setblock -2010 128 3043 minecraft:iron_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] replace
execute unless block -1985 93 3024 minecraft:oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] run setblock -1985 93 3024 minecraft:oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] replace
execute unless block -1986 93 3026 minecraft:light[level=4,waterlogged=false] run setblock -1986 93 3026 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1999 94 3028 minecraft:light[level=4,waterlogged=false] run setblock -1999 94 3028 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1994 94 3028 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1994 94 3028 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1994 94 3031 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1994 94 3031 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1992 94 3032 minecraft:iron_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1992 94 3032 minecraft:iron_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1988 94 3032 minecraft:iron_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1988 94 3032 minecraft:iron_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1992 94 3034 minecraft:iron_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1992 94 3034 minecraft:iron_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1988 94 3034 minecraft:iron_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] run setblock -1988 94 3034 minecraft:iron_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] replace
execute unless block -1986 94 3037 minecraft:light[level=4,waterlogged=false] run setblock -1986 94 3037 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1986 94 3038 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -1986 94 3038 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -1985 94 3038 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -1985 94 3038 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -1994 95 3028 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1994 95 3028 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1994 95 3029 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] run setblock -1994 95 3029 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] replace
execute unless block -1994 95 3030 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] run setblock -1994 95 3030 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] replace
execute unless block -1986 95 3030 minecraft:lever[powered=false,facing=south,face=wall] run setblock -1986 95 3030 minecraft:lever[powered=false,facing=south,face=wall] replace
execute unless block -2000 95 3031 minecraft:mangrove_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] run setblock -2000 95 3031 minecraft:mangrove_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1999 95 3031 minecraft:mangrove_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] run setblock -1999 95 3031 minecraft:mangrove_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1998 95 3031 minecraft:mangrove_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] run setblock -1998 95 3031 minecraft:mangrove_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1994 95 3031 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1994 95 3031 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1992 95 3032 minecraft:iron_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1992 95 3032 minecraft:iron_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1988 95 3032 minecraft:birch_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=top] run setblock -1988 95 3032 minecraft:birch_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=top] replace
execute unless block -1992 95 3034 minecraft:iron_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1992 95 3034 minecraft:iron_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1988 95 3034 minecraft:birch_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] run setblock -1988 95 3034 minecraft:birch_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] replace
execute unless block -1989 95 3037 minecraft:potted_cornflower run setblock -1989 95 3037 minecraft:potted_cornflower replace
execute unless block -1994 96 3028 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1994 96 3028 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1986 96 3030 minecraft:lever[powered=false,facing=west,face=wall] run setblock -1986 96 3030 minecraft:lever[powered=false,facing=west,face=wall] replace
execute unless block -1994 96 3031 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1994 96 3031 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1992 96 3032 minecraft:iron_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1992 96 3032 minecraft:iron_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1988 96 3032 minecraft:birch_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=top] run setblock -1988 96 3032 minecraft:birch_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=top] replace
execute unless block -1992 96 3034 minecraft:iron_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1992 96 3034 minecraft:iron_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1988 96 3034 minecraft:birch_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] run setblock -1988 96 3034 minecraft:birch_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] replace
execute unless block -1986 96 3038 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -1986 96 3038 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -1985 96 3038 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -1985 96 3038 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -1986 97 3025 minecraft:dark_oak_fence_gate[powered=false,open=false,facing=west,in_wall=false] run setblock -1986 97 3025 minecraft:dark_oak_fence_gate[powered=false,open=false,facing=west,in_wall=false] replace
execute unless block -1994 97 3028 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1994 97 3028 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1994 97 3029 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] run setblock -1994 97 3029 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] replace
execute unless block -1994 97 3030 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] run setblock -1994 97 3030 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] replace
execute unless block -1994 97 3031 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1994 97 3031 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1992 97 3032 minecraft:iron_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1992 97 3032 minecraft:iron_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1988 97 3032 minecraft:birch_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=top] run setblock -1988 97 3032 minecraft:birch_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=top] replace
execute unless block -1992 97 3034 minecraft:iron_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1992 97 3034 minecraft:iron_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1988 97 3034 minecraft:birch_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] run setblock -1988 97 3034 minecraft:birch_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] replace
execute unless block -1994 97 3037 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] run setblock -1994 97 3037 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] replace
execute unless block -1994 97 3038 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] run setblock -1994 97 3038 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] replace
execute unless block -1986 97 3038 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -1986 97 3038 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -1985 97 3038 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -1985 97 3038 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -1996 97 3039 minecraft:lantern[hanging=false,waterlogged=false] run setblock -1996 97 3039 minecraft:lantern[hanging=false,waterlogged=false] replace
execute unless block -1994 97 3039 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] run setblock -1994 97 3039 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] replace
execute unless block -1986 98 3026 minecraft:light[level=4,waterlogged=false] run setblock -1986 98 3026 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1994 98 3028 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1994 98 3028 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1994 98 3031 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1994 98 3031 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1992 98 3032 minecraft:iron_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1992 98 3032 minecraft:iron_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1988 98 3032 minecraft:birch_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=top] run setblock -1988 98 3032 minecraft:birch_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=top] replace
execute unless block -1987 98 3032 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -1987 98 3032 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -1987 98 3033 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -1987 98 3033 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -1992 98 3034 minecraft:iron_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1992 98 3034 minecraft:iron_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1988 98 3034 minecraft:birch_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] run setblock -1988 98 3034 minecraft:birch_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] replace
execute unless block -1987 98 3034 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -1987 98 3034 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -1994 99 3029 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] run setblock -1994 99 3029 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] replace
execute unless block -1994 99 3030 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] run setblock -1994 99 3030 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] replace
execute unless block -2000 99 3038 minecraft:iron_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] run setblock -2000 99 3038 minecraft:iron_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] replace
execute unless block -1999 99 3038 minecraft:iron_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] run setblock -1999 99 3038 minecraft:iron_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] replace
execute unless block -1991 100 3024 minecraft:dark_oak_fence_gate[powered=false,open=true,facing=west,in_wall=false] run setblock -1991 100 3024 minecraft:dark_oak_fence_gate[powered=false,open=true,facing=west,in_wall=false] replace
execute unless block -1991 100 3025 minecraft:dark_oak_fence_gate[powered=false,open=true,facing=west,in_wall=false] run setblock -1991 100 3025 minecraft:dark_oak_fence_gate[powered=false,open=true,facing=west,in_wall=false] replace
execute unless block -1991 100 3026 minecraft:dark_oak_fence_gate[powered=false,open=true,facing=west,in_wall=false] run setblock -1991 100 3026 minecraft:dark_oak_fence_gate[powered=false,open=true,facing=west,in_wall=false] replace
execute unless block -1994 100 3028 minecraft:light[level=4,waterlogged=false] run setblock -1994 100 3028 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1993 100 3032 minecraft:light[level=4,waterlogged=false] run setblock -1993 100 3032 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1986 100 3032 minecraft:light[level=4,waterlogged=false] run setblock -1986 100 3032 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1993 100 3034 minecraft:light[level=4,waterlogged=false] run setblock -1993 100 3034 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1986 100 3036 minecraft:light[level=4,waterlogged=false] run setblock -1986 100 3036 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1987 104 3024 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock -1987 104 3024 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block -1985 104 3024 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] run setblock -1985 104 3024 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] replace
execute unless block -1987 104 3025 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock -1987 104 3025 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block -1985 104 3025 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] run setblock -1985 104 3025 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] replace
execute unless block -1997 104 3026 minecraft:light[level=4,waterlogged=false] run setblock -1997 104 3026 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1989 104 3028 minecraft:light[level=4,waterlogged=false] run setblock -1989 104 3028 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1992 104 3029 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=top] run setblock -1992 104 3029 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=top] replace
execute unless block -1991 104 3029 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=top] run setblock -1991 104 3029 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=top] replace
execute unless block -1992 104 3031 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1992 104 3031 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1991 104 3031 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1991 104 3031 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1988 104 3031 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -1988 104 3031 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -1987 104 3031 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -1987 104 3031 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -1986 104 3031 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -1986 104 3031 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -1993 104 3032 minecraft:light[level=4,waterlogged=false] run setblock -1993 104 3032 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1997 104 3033 minecraft:light[level=4,waterlogged=false] run setblock -1997 104 3033 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1993 104 3033 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1993 104 3033 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1997 104 3034 minecraft:light[level=4,waterlogged=false] run setblock -1997 104 3034 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1997 104 3035 minecraft:light[level=4,waterlogged=false] run setblock -1997 104 3035 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1993 104 3035 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1993 104 3035 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1988 104 3035 minecraft:iron_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] run setblock -1988 104 3035 minecraft:iron_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] replace
execute unless block -1987 104 3035 minecraft:iron_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] run setblock -1987 104 3035 minecraft:iron_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] replace
execute unless block -1986 104 3035 minecraft:iron_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] run setblock -1986 104 3035 minecraft:iron_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] replace
execute unless block -1987 105 3024 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock -1987 105 3024 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block -1985 105 3024 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] run setblock -1985 105 3024 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] replace
execute unless block -1987 105 3025 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock -1987 105 3025 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block -1985 105 3025 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] run setblock -1985 105 3025 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] replace
execute unless block -1988 105 3028 minecraft:lever[powered=false,facing=south,face=wall] run setblock -1988 105 3028 minecraft:lever[powered=false,facing=south,face=wall] replace
execute unless block -1992 105 3029 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=top] run setblock -1992 105 3029 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=top] replace
execute unless block -1991 105 3029 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=top] run setblock -1991 105 3029 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=top] replace
execute unless block -1992 105 3031 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] run setblock -1992 105 3031 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] replace
execute unless block -1991 105 3031 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] run setblock -1991 105 3031 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] replace
execute unless block -1993 105 3032 minecraft:light[level=4,waterlogged=false] run setblock -1993 105 3032 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1993 105 3033 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1993 105 3033 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1993 105 3034 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] run setblock -1993 105 3034 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] replace
execute unless block -1993 105 3035 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1993 105 3035 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1985 105 3039 minecraft:light[level=4,waterlogged=false] run setblock -1985 105 3039 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1987 106 3024 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock -1987 106 3024 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block -1985 106 3024 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] run setblock -1985 106 3024 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] replace
execute unless block -1987 106 3025 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock -1987 106 3025 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block -1985 106 3025 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] run setblock -1985 106 3025 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] replace
execute unless block -1988 106 3028 minecraft:lever[powered=false,facing=south,face=wall] run setblock -1988 106 3028 minecraft:lever[powered=false,facing=south,face=wall] replace
execute unless block -1992 106 3029 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=top] run setblock -1992 106 3029 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=top] replace
execute unless block -1991 106 3029 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=top] run setblock -1991 106 3029 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=top] replace
execute unless block -1992 106 3031 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1992 106 3031 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1991 106 3031 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1991 106 3031 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1993 106 3032 minecraft:light[level=4,waterlogged=false] run setblock -1993 106 3032 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1993 106 3033 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1993 106 3033 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1993 106 3035 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1993 106 3035 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1990 106 3037 minecraft:light[level=4,waterlogged=false] run setblock -1990 106 3037 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1990 106 3038 minecraft:light[level=4,waterlogged=false] run setblock -1990 106 3038 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1987 107 3024 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock -1987 107 3024 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block -1985 107 3024 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] run setblock -1985 107 3024 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] replace
execute unless block -1987 107 3025 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock -1987 107 3025 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block -1985 107 3025 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] run setblock -1985 107 3025 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] replace
execute unless block -1992 107 3029 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1992 107 3029 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1991 107 3029 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1991 107 3029 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1992 107 3031 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1992 107 3031 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1991 107 3031 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1991 107 3031 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1993 107 3033 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1993 107 3033 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1993 107 3035 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1993 107 3035 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1987 108 3024 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock -1987 108 3024 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block -1985 108 3024 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] run setblock -1985 108 3024 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] replace
execute unless block -1987 108 3025 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] run setblock -1987 108 3025 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] replace
execute unless block -1985 108 3025 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] run setblock -1985 108 3025 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] replace
execute unless block -1992 108 3029 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1992 108 3029 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1991 108 3029 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1991 108 3029 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1992 108 3031 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1992 108 3031 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1991 108 3031 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1991 108 3031 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1990 108 3032 minecraft:warped_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1990 108 3032 minecraft:warped_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1993 108 3033 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] run setblock -1993 108 3033 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=top] replace
execute unless block -1990 108 3033 minecraft:warped_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1990 108 3033 minecraft:warped_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1993 108 3035 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=top] run setblock -1993 108 3035 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=top] replace
execute unless block -1992 109 3029 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1992 109 3029 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1992 109 3031 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1992 109 3031 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1996 110 3025 minecraft:lantern[hanging=false,waterlogged=false] run setblock -1996 110 3025 minecraft:lantern[hanging=false,waterlogged=false] replace
execute unless block -1996 110 3026 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] run setblock -1996 110 3026 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] replace
execute unless block -1993 110 3038 minecraft:light[level=4,waterlogged=false] run setblock -1993 110 3038 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1996 111 3026 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] run setblock -1996 111 3026 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] replace
execute unless block -1993 111 3036 minecraft:oak_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] run setblock -1993 111 3036 minecraft:oak_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] replace
execute unless block -1993 111 3037 minecraft:oak_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] run setblock -1993 111 3037 minecraft:oak_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] replace
execute unless block -1993 111 3038 minecraft:light[level=4,waterlogged=false] run setblock -1993 111 3038 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1989 111 3039 minecraft:light[level=4,waterlogged=false] run setblock -1989 111 3039 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1985 111 3039 minecraft:light[level=4,waterlogged=false] run setblock -1985 111 3039 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1996 112 3026 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] run setblock -1996 112 3026 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] replace
execute unless block -1993 112 3029 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] run setblock -1993 112 3029 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] replace
execute unless block -1993 112 3030 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] run setblock -1993 112 3030 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] replace
execute unless block -1999 112 3032 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] run setblock -1999 112 3032 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] replace
execute unless block -1997 112 3032 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] run setblock -1997 112 3032 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] replace
execute unless block -1995 112 3032 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] run setblock -1995 112 3032 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] replace
execute unless block -1993 112 3032 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] run setblock -1993 112 3032 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] replace
execute unless block -1999 112 3033 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] run setblock -1999 112 3033 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] replace
execute unless block -1997 112 3033 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] run setblock -1997 112 3033 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] replace
execute unless block -1995 112 3033 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] run setblock -1995 112 3033 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] replace
execute unless block -1993 112 3033 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] run setblock -1993 112 3033 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] replace
execute unless block -1999 112 3034 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] run setblock -1999 112 3034 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] replace
execute unless block -1997 112 3034 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] run setblock -1997 112 3034 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] replace
execute unless block -1995 112 3034 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] run setblock -1995 112 3034 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] replace
execute unless block -1993 112 3034 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] run setblock -1993 112 3034 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] replace
execute unless block -1999 112 3035 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] run setblock -1999 112 3035 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] replace
execute unless block -1997 112 3035 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] run setblock -1997 112 3035 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] replace
execute unless block -1995 112 3035 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] run setblock -1995 112 3035 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] replace
execute unless block -1993 112 3035 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] run setblock -1993 112 3035 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] replace
execute unless block -1993 112 3037 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] run setblock -1993 112 3037 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] replace
execute unless block -1993 112 3038 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] run setblock -1993 112 3038 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] replace
execute unless block -1996 113 3026 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] run setblock -1996 113 3026 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] replace
execute unless block -1996 114 3026 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] run setblock -1996 114 3026 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] replace
execute unless block -1991 114 3027 minecraft:light[level=4,waterlogged=false] run setblock -1991 114 3027 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1994 114 3028 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1994 114 3028 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1992 114 3028 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1992 114 3028 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1989 114 3028 minecraft:oak_fence_gate[powered=false,open=true,facing=south,in_wall=false] run setblock -1989 114 3028 minecraft:oak_fence_gate[powered=false,open=true,facing=south,in_wall=false] replace
execute unless block -1994 114 3029 minecraft:spruce_fence_gate[powered=false,open=false,facing=east,in_wall=false] run setblock -1994 114 3029 minecraft:spruce_fence_gate[powered=false,open=false,facing=east,in_wall=false] replace
execute unless block -1989 114 3029 minecraft:oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=top] run setblock -1989 114 3029 minecraft:oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=top] replace
execute unless block -1994 114 3030 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1994 114 3030 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1992 114 3030 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1992 114 3030 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2000 114 3031 minecraft:spruce_fence_gate[powered=false,open=false,facing=north,in_wall=false] run setblock -2000 114 3031 minecraft:spruce_fence_gate[powered=false,open=false,facing=north,in_wall=false] replace
execute unless block -1999 114 3031 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock -1999 114 3031 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block -1998 114 3031 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] run setblock -1998 114 3031 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] replace
execute unless block -1997 114 3031 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock -1997 114 3031 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block -1996 114 3031 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] run setblock -1996 114 3031 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] replace
execute unless block -1991 114 3031 minecraft:light[level=4,waterlogged=false] run setblock -1991 114 3031 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1995 114 3032 minecraft:light[level=4,waterlogged=false] run setblock -1995 114 3032 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1991 114 3033 minecraft:light[level=4,waterlogged=false] run setblock -1991 114 3033 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1988 114 3033 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1988 114 3033 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1987 114 3033 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1987 114 3033 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1992 114 3034 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1992 114 3034 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1988 114 3034 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1988 114 3034 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1987 114 3034 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1987 114 3034 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1995 114 3035 minecraft:light[level=4,waterlogged=false] run setblock -1995 114 3035 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1992 114 3035 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1992 114 3035 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2000 114 3036 minecraft:spruce_fence_gate[powered=false,open=false,facing=south,in_wall=false] run setblock -2000 114 3036 minecraft:spruce_fence_gate[powered=false,open=false,facing=south,in_wall=false] replace
execute unless block -1999 114 3036 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock -1999 114 3036 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block -1998 114 3036 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] run setblock -1998 114 3036 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] replace
execute unless block -1997 114 3036 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] run setblock -1997 114 3036 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=bottom] replace
execute unless block -1996 114 3036 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] run setblock -1996 114 3036 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] replace
execute unless block -1993 114 3037 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1993 114 3037 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1993 114 3038 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1993 114 3038 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1989 114 3038 minecraft:light[level=4,waterlogged=false] run setblock -1989 114 3038 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1996 115 3026 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] run setblock -1996 115 3026 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] replace
execute unless block -1986 115 3026 minecraft:lever[powered=false,facing=east,face=wall] run setblock -1986 115 3026 minecraft:lever[powered=false,facing=east,face=wall] replace
execute unless block -1994 115 3028 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] run setblock -1994 115 3028 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1992 115 3028 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1992 115 3028 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -2000 100 3004 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -2000 100 3004 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -1986 115 3028 minecraft:lever[powered=false,facing=east,face=wall] run setblock -1986 115 3028 minecraft:lever[powered=false,facing=east,face=wall] replace
execute unless block -1999 100 3004 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -1999 100 3004 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -1994 115 3029 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] run setblock -1994 115 3029 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1998 100 3004 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -1998 100 3004 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -1994 115 3030 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] run setblock -1994 115 3030 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1997 100 3004 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -1997 100 3004 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -1992 115 3030 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1992 115 3030 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1996 100 3004 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -1996 100 3004 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -2000 115 3031 minecraft:spruce_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=bottom] run setblock -2000 115 3031 minecraft:spruce_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=bottom] replace
execute unless block -1995 100 3004 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -1995 100 3004 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -1999 115 3031 minecraft:spruce_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=bottom] run setblock -1999 115 3031 minecraft:spruce_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=bottom] replace
execute unless block -1994 100 3004 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -1994 100 3004 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -1998 115 3031 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] run setblock -1998 115 3031 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] replace
execute unless block -1993 100 3004 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -1993 100 3004 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -1997 115 3031 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] run setblock -1997 115 3031 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] replace
execute unless block -1992 100 3004 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -1992 100 3004 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -1996 115 3031 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] run setblock -1996 115 3031 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] replace
execute unless block -2000 100 3005 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] run setblock -2000 100 3005 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] replace
execute unless block -1988 115 3033 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1988 115 3033 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1998 100 3005 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] run setblock -1998 100 3005 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] replace
execute unless block -1987 115 3033 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1987 115 3033 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1996 100 3005 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] run setblock -1996 100 3005 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] replace
execute unless block -1992 115 3034 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1992 115 3034 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1994 100 3005 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] run setblock -1994 100 3005 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] replace
execute unless block -1988 115 3034 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1988 115 3034 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1993 102 3004 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] run setblock -1993 102 3004 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1987 115 3034 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1987 115 3034 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1993 102 3005 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] run setblock -1993 102 3005 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1992 115 3035 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1992 115 3035 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -2000 115 3036 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] run setblock -2000 115 3036 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] replace
execute unless block -1999 115 3036 minecraft:spruce_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=bottom] run setblock -1999 115 3036 minecraft:spruce_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=bottom] replace
execute unless block -1998 115 3036 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] run setblock -1998 115 3036 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] replace
execute unless block -1997 115 3036 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] run setblock -1997 115 3036 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] replace
execute unless block -1996 115 3036 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] run setblock -1996 115 3036 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=bottom] replace
execute unless block -1993 115 3037 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] run setblock -1993 115 3037 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1993 115 3038 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] run setblock -1993 115 3038 minecraft:spruce_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1986 115 3038 minecraft:light[level=4,waterlogged=false] run setblock -1986 115 3038 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1985 115 3038 minecraft:light[level=4,waterlogged=false] run setblock -1985 115 3038 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1996 116 3024 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] run setblock -1996 116 3024 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] replace
execute unless block -1996 116 3025 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] run setblock -1996 116 3025 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] replace
execute unless block -1996 116 3026 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] run setblock -1996 116 3026 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] replace
execute unless block -1989 116 3027 minecraft:oak_fence_gate[powered=false,open=true,facing=south,in_wall=false] run setblock -1989 116 3027 minecraft:oak_fence_gate[powered=false,open=true,facing=south,in_wall=false] replace
execute unless block -1992 116 3028 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1992 116 3028 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1989 116 3028 minecraft:oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1989 116 3028 minecraft:oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1992 116 3030 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1992 116 3030 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1988 116 3033 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1988 116 3033 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1987 116 3033 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1987 116 3033 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1992 116 3034 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1992 116 3034 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1988 116 3034 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1988 116 3034 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1987 116 3034 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1987 116 3034 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1992 116 3035 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1992 116 3035 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1991 117 3024 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] run setblock -1991 117 3024 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] replace
execute unless block -1991 117 3025 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] run setblock -1991 117 3025 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] replace
execute unless block -1992 117 3028 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1992 117 3028 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1992 117 3030 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1992 117 3030 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1988 117 3033 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1988 117 3033 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1987 117 3033 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1987 117 3033 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1992 117 3034 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1992 117 3034 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1988 117 3034 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1988 117 3034 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1987 117 3034 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1987 117 3034 minecraft:dark_oak_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1992 117 3035 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1992 117 3035 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1986 117 3035 minecraft:spruce_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] run setblock -1986 117 3035 minecraft:spruce_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] replace
execute unless block -1986 117 3036 minecraft:spruce_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] run setblock -1986 117 3036 minecraft:spruce_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] replace
execute unless block -1986 117 3037 minecraft:spruce_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] run setblock -1986 117 3037 minecraft:spruce_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] replace
execute unless block -1986 117 3038 minecraft:spruce_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] run setblock -1986 117 3038 minecraft:spruce_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] replace
execute unless block -1989 118 3026 minecraft:oak_fence_gate[powered=false,open=true,facing=south,in_wall=false] run setblock -1989 118 3026 minecraft:oak_fence_gate[powered=false,open=true,facing=south,in_wall=false] replace
execute unless block -1989 118 3027 minecraft:oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1989 118 3027 minecraft:oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1992 118 3028 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1992 118 3028 minecraft:spruce_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1992 118 3030 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] run setblock -1992 118 3030 minecraft:spruce_trapdoor[powered=false,open=true,facing=north,waterlogged=false,half=bottom] replace
execute unless block -1986 118 3037 minecraft:potted_azure_bluet run setblock -1986 118 3037 minecraft:potted_azure_bluet replace
execute unless block -1986 119 3025 minecraft:light[level=4,waterlogged=false] run setblock -1986 119 3025 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1985 119 3025 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] run setblock -1985 119 3025 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] replace
execute unless block -1985 119 3026 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] run setblock -1985 119 3026 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] replace
execute unless block -1985 119 3027 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] run setblock -1985 119 3027 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] replace
execute unless block -1985 119 3028 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] run setblock -1985 119 3028 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] replace
execute unless block -1986 119 3029 minecraft:light[level=4,waterlogged=false] run setblock -1986 119 3029 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1985 119 3029 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] run setblock -1985 119 3029 minecraft:dark_oak_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] replace
execute unless block -1986 119 3036 minecraft:spruce_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] run setblock -1986 119 3036 minecraft:spruce_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] replace
execute unless block -1986 119 3037 minecraft:spruce_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] run setblock -1986 119 3037 minecraft:spruce_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] replace
execute unless block -1991 119 3038 minecraft:light[level=4,waterlogged=false] run setblock -1991 119 3038 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1989 119 3038 minecraft:light[level=4,waterlogged=false] run setblock -1989 119 3038 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1986 119 3038 minecraft:spruce_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] run setblock -1986 119 3038 minecraft:spruce_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] replace
execute unless block -1996 120 3025 minecraft:light[level=4,waterlogged=false] run setblock -1996 120 3025 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1989 120 3026 minecraft:oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1989 120 3026 minecraft:oak_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1996 120 3028 minecraft:light[level=4,waterlogged=false] run setblock -1996 120 3028 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1999 120 3031 minecraft:spruce_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] run setblock -1999 120 3031 minecraft:spruce_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] replace
execute unless block -1996 120 3031 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] run setblock -1996 120 3031 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] replace
execute unless block -1986 120 3032 minecraft:light[level=4,waterlogged=false] run setblock -1986 120 3032 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1999 120 3036 minecraft:spruce_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] run setblock -1999 120 3036 minecraft:spruce_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] replace
execute unless block -1996 120 3036 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] run setblock -1996 120 3036 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] replace
execute unless block -1986 120 3036 minecraft:potted_fern run setblock -1986 120 3036 minecraft:potted_fern replace
execute unless block -1991 121 3029 minecraft:iron_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] run setblock -1991 121 3029 minecraft:iron_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] replace
execute unless block -1989 121 3029 minecraft:light[level=4,waterlogged=false] run setblock -1989 121 3029 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1991 121 3030 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -1991 121 3030 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -1989 121 3030 minecraft:light[level=4,waterlogged=false] run setblock -1989 121 3030 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1991 121 3031 minecraft:iron_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] run setblock -1991 121 3031 minecraft:iron_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] replace
execute unless block -1991 121 3032 minecraft:iron_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] run setblock -1991 121 3032 minecraft:iron_trapdoor[powered=false,open=true,facing=south,waterlogged=false,half=bottom] replace
execute unless block -1991 121 3035 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -1991 121 3035 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -1990 121 3035 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -1990 121 3035 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -1989 121 3035 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -1989 121 3035 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -1997 121 3037 minecraft:light[level=4,waterlogged=false] run setblock -1997 121 3037 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1986 121 3038 minecraft:light[level=4,waterlogged=false] run setblock -1986 121 3038 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1992 122 3024 minecraft:light[level=4,waterlogged=false] run setblock -1992 122 3024 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1985 122 3024 minecraft:light[level=4,waterlogged=false] run setblock -1985 122 3024 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1992 122 3027 minecraft:light[level=4,waterlogged=false] run setblock -1992 122 3027 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1986 122 3030 minecraft:oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] run setblock -1986 122 3030 minecraft:oak_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=bottom] replace
execute unless block -1985 122 3030 minecraft:oak_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -1985 122 3030 minecraft:oak_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -1993 123 3033 minecraft:light[level=4,waterlogged=false] run setblock -1993 123 3033 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1993 123 3034 minecraft:light[level=4,waterlogged=false] run setblock -1993 123 3034 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2000 124 3030 minecraft:light[level=4,waterlogged=false] run setblock -2000 124 3030 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2000 124 3032 minecraft:light[level=4,waterlogged=false] run setblock -2000 124 3032 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1992 126 3024 minecraft:light[level=4,waterlogged=false] run setblock -1992 126 3024 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1995 134 3024 minecraft:iron_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] run setblock -1995 134 3024 minecraft:iron_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] replace
execute unless block -1993 134 3024 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] run setblock -1993 134 3024 minecraft:iron_trapdoor[powered=false,open=false,facing=north,waterlogged=false,half=top] replace
execute unless block -1995 134 3025 minecraft:iron_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] run setblock -1995 134 3025 minecraft:iron_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] replace
execute unless block -1993 134 3025 minecraft:iron_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] run setblock -1993 134 3025 minecraft:iron_trapdoor[powered=false,open=false,facing=east,waterlogged=false,half=top] replace
execute unless block -1994 134 3027 minecraft:iron_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] run setblock -1994 134 3027 minecraft:iron_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] replace
execute unless block -1997 97 3041 minecraft:turtle_egg[hatch=0,eggs=3] run setblock -1997 97 3041 minecraft:turtle_egg[hatch=0,eggs=3] replace
execute unless block -1994 100 3040 minecraft:light[level=4,waterlogged=false] run setblock -1994 100 3040 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1995 100 3041 minecraft:light[level=4,waterlogged=false] run setblock -1995 100 3041 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1998 104 3042 minecraft:light[level=4,waterlogged=false] run setblock -1998 104 3042 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1999 105 3043 minecraft:lever[powered=false,facing=north,face=wall] run setblock -1999 105 3043 minecraft:lever[powered=false,facing=north,face=wall] replace
execute unless block -1997 106 3042 minecraft:light[level=4,waterlogged=false] run setblock -1997 106 3042 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1999 108 3043 minecraft:lever[powered=false,facing=north,face=wall] run setblock -1999 108 3043 minecraft:lever[powered=false,facing=north,face=wall] replace
execute unless block -2000 109 3043 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] run setblock -2000 109 3043 minecraft:spruce_trapdoor[powered=false,open=false,facing=west,waterlogged=false,half=top] replace
execute unless block -1989 110 3042 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] run setblock -1989 110 3042 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] replace
execute unless block -1987 110 3042 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] run setblock -1987 110 3042 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] replace
execute unless block -1985 110 3042 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] run setblock -1985 110 3042 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] replace
execute unless block -1991 110 3043 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] run setblock -1991 110 3043 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] replace
execute unless block -1989 110 3043 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] run setblock -1989 110 3043 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] replace
execute unless block -1987 110 3043 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] run setblock -1987 110 3043 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] replace
execute unless block -1985 110 3043 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] run setblock -1985 110 3043 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] replace
execute unless block -1991 110 3044 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] run setblock -1991 110 3044 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] replace
execute unless block -1989 110 3044 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] run setblock -1989 110 3044 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] replace
execute unless block -1987 110 3044 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] run setblock -1987 110 3044 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] replace
execute unless block -1985 110 3044 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] run setblock -1985 110 3044 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] replace
execute unless block -2000 110 3045 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] run setblock -2000 110 3045 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] replace
execute unless block -2000 110 3046 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] run setblock -2000 110 3046 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] replace
execute unless block -1999 110 3046 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] run setblock -1999 110 3046 minecraft:spruce_trapdoor[powered=false,open=false,facing=south,waterlogged=false,half=top] replace
execute unless block -1997 111 3042 minecraft:light[level=4,waterlogged=false] run setblock -1997 111 3042 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1999 112 3042 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] run setblock -1999 112 3042 minecraft:spruce_trapdoor[powered=false,open=true,facing=west,waterlogged=false,half=top] replace
execute unless block -1998 112 3042 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] run setblock -1998 112 3042 minecraft:spruce_trapdoor[powered=false,open=true,facing=east,waterlogged=false,half=top] replace
execute unless block -1997 112 3042 minecraft:light[level=4,waterlogged=false] run setblock -1997 112 3042 minecraft:light[level=4,waterlogged=false] replace
execute unless block -1997 116 3042 minecraft:light[level=4,waterlogged=false] run setblock -1997 116 3042 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2000 120 3043 minecraft:light[level=4,waterlogged=false] run setblock -2000 120 3043 minecraft:light[level=4,waterlogged=false] replace
execute unless block -2000 124 3042 minecraft:light[level=4,waterlogged=false] run setblock -2000 124 3042 minecraft:light[level=4,waterlogged=false] replace

execute unless block -2012 94 3024 minecraft:dark_oak_door[powered=false,open=false,facing=west,half=lower,hinge=right] run setblock -2012 94 3024 minecraft:dark_oak_door[powered=false,open=false,facing=west,half=lower,hinge=right] destroy
execute unless block -2012 95 3024 minecraft:dark_oak_door[powered=false,open=false,facing=west,half=upper,hinge=right] run setblock -2012 95 3024 minecraft:dark_oak_door[powered=false,open=false,facing=west,half=upper,hinge=right] destroy
execute unless block -1977 104 3015 minecraft:birch_door[powered=false,open=false,facing=north,half=lower,hinge=left] run setblock -1977 104 3015 minecraft:birch_door[powered=false,open=false,facing=north,half=lower,hinge=left] destroy
execute unless block -1976 104 3015 minecraft:birch_door[powered=false,open=false,facing=north,half=lower,hinge=right] run setblock -1976 104 3015 minecraft:birch_door[powered=false,open=false,facing=north,half=lower,hinge=right] destroy
execute unless block -1983 104 3017 minecraft:mangrove_door[powered=false,open=false,facing=north,half=lower,hinge=right] run setblock -1983 104 3017 minecraft:mangrove_door[powered=false,open=false,facing=north,half=lower,hinge=right] destroy
execute unless block -1978 104 3017 minecraft:mangrove_door[powered=false,open=false,facing=north,half=lower,hinge=right] run setblock -1978 104 3017 minecraft:mangrove_door[powered=false,open=false,facing=north,half=lower,hinge=right] destroy
execute unless block -1981 104 3018 minecraft:mangrove_door[powered=false,open=false,facing=north,half=lower,hinge=left] run setblock -1981 104 3018 minecraft:mangrove_door[powered=false,open=false,facing=north,half=lower,hinge=left] destroy
execute unless block -1983 104 3022 minecraft:mangrove_door[powered=false,open=false,facing=south,half=lower,hinge=right] run setblock -1983 104 3022 minecraft:mangrove_door[powered=false,open=false,facing=south,half=lower,hinge=right] destroy
execute unless block -1981 104 3023 minecraft:mangrove_door[powered=false,open=false,facing=south,half=lower,hinge=left] run setblock -1981 104 3023 minecraft:mangrove_door[powered=false,open=false,facing=south,half=lower,hinge=left] destroy
execute unless block -1978 104 3023 minecraft:mangrove_door[powered=false,open=false,facing=south,half=lower,hinge=right] run setblock -1978 104 3023 minecraft:mangrove_door[powered=false,open=false,facing=south,half=lower,hinge=right] destroy
execute unless block -1977 105 3015 minecraft:birch_door[powered=false,open=false,facing=north,half=upper,hinge=left] run setblock -1977 105 3015 minecraft:birch_door[powered=false,open=false,facing=north,half=upper,hinge=left] destroy
execute unless block -1976 105 3015 minecraft:birch_door[powered=false,open=false,facing=north,half=upper,hinge=right] run setblock -1976 105 3015 minecraft:birch_door[powered=false,open=false,facing=north,half=upper,hinge=right] destroy
execute unless block -1983 105 3017 minecraft:mangrove_door[powered=false,open=false,facing=north,half=upper,hinge=right] run setblock -1983 105 3017 minecraft:mangrove_door[powered=false,open=false,facing=north,half=upper,hinge=right] destroy
execute unless block -1978 105 3017 minecraft:mangrove_door[powered=false,open=false,facing=north,half=upper,hinge=right] run setblock -1978 105 3017 minecraft:mangrove_door[powered=false,open=false,facing=north,half=upper,hinge=right] destroy
execute unless block -1981 105 3018 minecraft:mangrove_door[powered=false,open=false,facing=north,half=upper,hinge=left] run setblock -1981 105 3018 minecraft:mangrove_door[powered=false,open=false,facing=north,half=upper,hinge=left] destroy
execute unless block -1983 105 3022 minecraft:mangrove_door[powered=false,open=false,facing=south,half=upper,hinge=right] run setblock -1983 105 3022 minecraft:mangrove_door[powered=false,open=false,facing=south,half=upper,hinge=right] destroy
execute unless block -1981 105 3023 minecraft:mangrove_door[powered=false,open=false,facing=south,half=upper,hinge=left] run setblock -1981 105 3023 minecraft:mangrove_door[powered=false,open=false,facing=south,half=upper,hinge=left] destroy
execute unless block -1978 105 3023 minecraft:mangrove_door[powered=false,open=false,facing=south,half=upper,hinge=right] run setblock -1978 105 3023 minecraft:mangrove_door[powered=false,open=false,facing=south,half=upper,hinge=right] destroy
execute unless block -1986 104 3017 minecraft:mangrove_door[powered=false,open=false,facing=north,half=lower,hinge=left] run setblock -1986 104 3017 minecraft:mangrove_door[powered=false,open=false,facing=north,half=lower,hinge=left] destroy
execute unless block -1989 104 3020 minecraft:mangrove_door[powered=false,open=false,facing=west,half=lower,hinge=right] run setblock -1989 104 3020 minecraft:mangrove_door[powered=false,open=false,facing=west,half=lower,hinge=right] destroy
execute unless block -1986 104 3023 minecraft:mangrove_door[powered=false,open=false,facing=south,half=lower,hinge=left] run setblock -1986 104 3023 minecraft:mangrove_door[powered=false,open=false,facing=south,half=lower,hinge=left] destroy
execute unless block -1986 105 3017 minecraft:mangrove_door[powered=false,open=false,facing=north,half=upper,hinge=left] run setblock -1986 105 3017 minecraft:mangrove_door[powered=false,open=false,facing=north,half=upper,hinge=left] destroy
execute unless block -1989 105 3020 minecraft:mangrove_door[powered=false,open=false,facing=west,half=upper,hinge=right] run setblock -1989 105 3020 minecraft:mangrove_door[powered=false,open=false,facing=west,half=upper,hinge=right] destroy
execute unless block -1986 105 3023 minecraft:mangrove_door[powered=false,open=false,facing=south,half=upper,hinge=left] run setblock -1986 105 3023 minecraft:mangrove_door[powered=false,open=false,facing=south,half=upper,hinge=left] destroy
execute unless block -1952 106 3046 minecraft:iron_door[powered=false,open=false,facing=south,half=lower,hinge=right] run setblock -1952 106 3046 minecraft:iron_door[powered=false,open=false,facing=south,half=lower,hinge=right] destroy
execute unless block -1952 107 3046 minecraft:iron_door[powered=false,open=false,facing=south,half=upper,hinge=right] run setblock -1952 107 3046 minecraft:iron_door[powered=false,open=false,facing=south,half=upper,hinge=right] destroy
