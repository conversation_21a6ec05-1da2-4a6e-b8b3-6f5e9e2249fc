#> Add a card to the card table

# Mainhand
execute if score $cards riverboat matches 0 if items entity @s weapon.mainhand stick[item_model="misc/playingcard"] run summon item_display 2048.25 66.078 -1993.75 {Tags:["riverboat_card", "card1", "ace"],item:{id:"stick",Count:1b,components:{"minecraft:item_model":"misc/playingcard"}},brightness:{block:15,sky:15},Rotation:[225f,0f],transformation:[-1.2, 0.0, 0.0, 0.5062, 0.0, -1.2, 0.0, -0.6, 0.0, 0.0, 1.2, -0.45, 0.0, 0.0, 0.0, 1.0]}
execute if score $cards riverboat matches 0 if items entity @s weapon.mainhand stick[item_model="misc/playingcard8"] run summon item_display 2048.25 66.078 -1993.75 {Tags:["riverboat_card", "card1", "eight"],item:{id:"stick",Count:1b,components:{"minecraft:item_model":"misc/playingcard8"}},brightness:{block:15,sky:15},Rotation:[225f,0f],transformation:[-1.2, 0.0, 0.0, 0.5062, 0.0, -1.2, 0.0, -0.6, 0.0, 0.0, 1.2, -0.45, 0.0, 0.0, 0.0, 1.0]}

execute if score $cards riverboat matches 1 if items entity @s weapon.mainhand stick[item_model="misc/playingcard"] run summon item_display 2048.125 66.066 -1993.78 {Tags:["riverboat_card", "card2", "ace"],item:{id:"stick",Count:1b,components:{"minecraft:item_model":"misc/playingcard"}},brightness:{block:15,sky:15},Rotation:[225f,0f],transformation:[-1.2, 0.0, 0.0, 0.5062, 0.0, 1.2, 0.0, 0.6, 0.0, 0.0, -1.2, 0.45, 0.0, 0.0, 0.0, 1.0]}
execute if score $cards riverboat matches 1 if items entity @s weapon.mainhand stick[item_model="misc/playingcard8"] run summon item_display 2048.125 66.066 -1993.78 {Tags:["riverboat_card", "card2", "eight"],item:{id:"stick",Count:1b,components:{"minecraft:item_model":"misc/playingcard8"}},brightness:{block:15,sky:15},Rotation:[225f,0f],transformation:[-1.2, 0.0, 0.0, 0.5062, 0.0, 1.2, 0.0, 0.6, 0.0, 0.0, -1.2, 0.45, 0.0, 0.0, 0.0, 1.0]}

execute if score $cards riverboat matches 2 if items entity @s weapon.mainhand stick[item_model="misc/playingcard"] run summon item_display 2048.0 66.07 -1993.80 {Tags:["riverboat_card", "card3", "ace"],item:{id:"stick",Count:1b,components:{"minecraft:item_model":"misc/playingcard"}},brightness:{block:15,sky:15},Rotation:[225f,0f],transformation:[-1.2, 0.0, 0.0, 0.5062, 0.0, 1.2, 0.0, 0.6, 0.0, 0.0, -1.2, 0.45, 0.0, 0.0, 0.0, 1.0]}
execute if score $cards riverboat matches 2 if items entity @s weapon.mainhand stick[item_model="misc/playingcard8"] run summon item_display 2048.0 66.07 -1993.80 {Tags:["riverboat_card", "card3", "eight"],item:{id:"stick",Count:1b,components:{"minecraft:item_model":"misc/playingcard8"}},brightness:{block:15,sky:15},Rotation:[225f,0f],transformation:[-1.2, 0.0, 0.0, 0.5062, 0.0, 1.2, 0.0, 0.6, 0.0, 0.0, -1.2, 0.45, 0.0, 0.0, 0.0, 1.0]}

execute if score $cards riverboat matches 3 if items entity @s weapon.mainhand stick[item_model="misc/playingcard"] run summon item_display 2047.875 66.074 -1993.82 {Tags:["riverboat_card", "card4", "ace"],item:{id:"stick",Count:1b,components:{"minecraft:item_model":"misc/playingcard"}},brightness:{block:15,sky:15},Rotation:[225f,0f],transformation:[-1.2, 0.0, 0.0, 0.5062, 0.0, 1.2, 0.0, 0.6, 0.0, 0.0, -1.2, 0.45, 0.0, 0.0, 0.0, 1.0]}
execute if score $cards riverboat matches 3 if items entity @s weapon.mainhand stick[item_model="misc/playingcard8"] run summon item_display 2047.875 66.074 -1993.82 {Tags:["riverboat_card", "card4", "eight"],item:{id:"stick",Count:1b,components:{"minecraft:item_model":"misc/playingcard8"}},brightness:{block:15,sky:15},Rotation:[225f,0f],transformation:[-1.2, 0.0, 0.0, 0.5062, 0.0, 1.2, 0.0, 0.6, 0.0, 0.0, -1.2, 0.45, 0.0, 0.0, 0.0, 1.0]}

execute if score $cards riverboat matches 4 if items entity @s weapon.mainhand stick[item_model="misc/playingcard"] run summon item_display 2047.750 66.078 -1993.84 {Tags:["riverboat_card", "card5", "ace"],item:{id:"stick",Count:1b,components:{"minecraft:item_model":"misc/playingcard"}},brightness:{block:15,sky:15},Rotation:[225f,0f],transformation:[-1.2, 0.0, 0.0, 0.5062, 0.0, 1.2, 0.0, 0.6, 0.0, 0.0, -1.2, 0.45, 0.0, 0.0, 0.0, 1.0]}
execute if score $cards riverboat matches 4 if items entity @s weapon.mainhand stick[item_model="misc/playingcard8"] run summon item_display 2047.750 66.078 -1993.84 {Tags:["riverboat_card", "card5", "eight"],item:{id:"stick",Count:1b,components:{"minecraft:item_model":"misc/playingcard8"}},brightness:{block:15,sky:15},Rotation:[225f,0f],transformation:[-1.2, 0.0, 0.0, 0.5062, 0.0, 1.2, 0.0, 0.6, 0.0, 0.0, -1.2, 0.45, 0.0, 0.0, 0.0, 1.0]}

execute if items entity @s weapon.mainhand stick[custom_data~{riverboat_card:1b}] run scoreboard players add $cards riverboat 1
execute if items entity @s weapon.mainhand stick[custom_data~{riverboat_card:1b}] run item replace entity @s weapon.mainhand with air

# Offhand
execute if score $cards riverboat matches 0 if items entity @s weapon.offhand stick[item_model="misc/playingcard"] run summon item_display 2048.25 66.078 -1993.75 {Tags:["riverboat_card", "card1", "ace"],item:{id:"stick",Count:1b,components:{"minecraft:item_model":"misc/playingcard"}},brightness:{block:15,sky:15},Rotation:[225f,0f],transformation:[-1.2, 0.0, 0.0, 0.5062, 0.0, -1.2, 0.0, -0.6, 0.0, 0.0, 1.2, -0.45, 0.0, 0.0, 0.0, 1.0]}
execute if score $cards riverboat matches 0 if items entity @s weapon.offhand stick[item_model="misc/playingcard8"] run summon item_display 2048.25 66.078 -1993.75 {Tags:["riverboat_card", "card1", "eight"],item:{id:"stick",Count:1b,components:{"minecraft:item_model":"misc/playingcard8"}},brightness:{block:15,sky:15},Rotation:[225f,0f],transformation:[-1.2, 0.0, 0.0, 0.5062, 0.0, -1.2, 0.0, -0.6, 0.0, 0.0, 1.2, -0.45, 0.0, 0.0, 0.0, 1.0]}

execute if score $cards riverboat matches 1 if items entity @s weapon.offhand stick[item_model="misc/playingcard"] run summon item_display 2048.125 66.066 -1993.78 {Tags:["riverboat_card", "card2", "ace"],item:{id:"stick",Count:1b,components:{"minecraft:item_model":"misc/playingcard"}},brightness:{block:15,sky:15},Rotation:[225f,0f],transformation:[-1.2, 0.0, 0.0, 0.5062, 0.0, 1.2, 0.0, 0.6, 0.0, 0.0, -1.2, 0.45, 0.0, 0.0, 0.0, 1.0]}
execute if score $cards riverboat matches 1 if items entity @s weapon.offhand stick[item_model="misc/playingcard8"] run summon item_display 2048.125 66.066 -1993.78 {Tags:["riverboat_card", "card2", "eight"],item:{id:"stick",Count:1b,components:{"minecraft:item_model":"misc/playingcard8"}},brightness:{block:15,sky:15},Rotation:[225f,0f],transformation:[-1.2, 0.0, 0.0, 0.5062, 0.0, 1.2, 0.0, 0.6, 0.0, 0.0, -1.2, 0.45, 0.0, 0.0, 0.0, 1.0]}

execute if score $cards riverboat matches 2 if items entity @s weapon.offhand stick[item_model="misc/playingcard"] run summon item_display 2048.0 66.07 -1993.80 {Tags:["riverboat_card", "card3", "ace"],item:{id:"stick",Count:1b,components:{"minecraft:item_model":"misc/playingcard"}},brightness:{block:15,sky:15},Rotation:[225f,0f],transformation:[-1.2, 0.0, 0.0, 0.5062, 0.0, 1.2, 0.0, 0.6, 0.0, 0.0, -1.2, 0.45, 0.0, 0.0, 0.0, 1.0]}
execute if score $cards riverboat matches 2 if items entity @s weapon.offhand stick[item_model="misc/playingcard8"] run summon item_display 2048.0 66.07 -1993.80 {Tags:["riverboat_card", "card3", "eight"],item:{id:"stick",Count:1b,components:{"minecraft:item_model":"misc/playingcard8"}},brightness:{block:15,sky:15},Rotation:[225f,0f],transformation:[-1.2, 0.0, 0.0, 0.5062, 0.0, 1.2, 0.0, 0.6, 0.0, 0.0, -1.2, 0.45, 0.0, 0.0, 0.0, 1.0]}

execute if score $cards riverboat matches 3 if items entity @s weapon.offhand stick[item_model="misc/playingcard"] run summon item_display 2047.875 66.074 -1993.82 {Tags:["riverboat_card", "card4", "ace"],item:{id:"stick",Count:1b,components:{"minecraft:item_model":"misc/playingcard"}},brightness:{block:15,sky:15},Rotation:[225f,0f],transformation:[-1.2, 0.0, 0.0, 0.5062, 0.0, 1.2, 0.0, 0.6, 0.0, 0.0, -1.2, 0.45, 0.0, 0.0, 0.0, 1.0]}
execute if score $cards riverboat matches 3 if items entity @s weapon.offhand stick[item_model="misc/playingcard8"] run summon item_display 2047.875 66.074 -1993.82 {Tags:["riverboat_card", "card4", "eight"],item:{id:"stick",Count:1b,components:{"minecraft:item_model":"misc/playingcard8"}},brightness:{block:15,sky:15},Rotation:[225f,0f],transformation:[-1.2, 0.0, 0.0, 0.5062, 0.0, 1.2, 0.0, 0.6, 0.0, 0.0, -1.2, 0.45, 0.0, 0.0, 0.0, 1.0]}

execute if score $cards riverboat matches 4 if items entity @s weapon.offhand stick[item_model="misc/playingcard"] run summon item_display 2047.750 66.078 -1993.84 {Tags:["riverboat_card", "card5", "ace"],item:{id:"stick",Count:1b,components:{"minecraft:item_model":"misc/playingcard"}},brightness:{block:15,sky:15},Rotation:[225f,0f],transformation:[-1.2, 0.0, 0.0, 0.5062, 0.0, 1.2, 0.0, 0.6, 0.0, 0.0, -1.2, 0.45, 0.0, 0.0, 0.0, 1.0]}
execute if score $cards riverboat matches 4 if items entity @s weapon.offhand stick[item_model="misc/playingcard8"] run summon item_display 2047.750 66.078 -1993.84 {Tags:["riverboat_card", "card5", "eight"],item:{id:"stick",Count:1b,components:{"minecraft:item_model":"misc/playingcard8"}},brightness:{block:15,sky:15},Rotation:[225f,0f],transformation:[-1.2, 0.0, 0.0, 0.5062, 0.0, 1.2, 0.0, 0.6, 0.0, 0.0, -1.2, 0.45, 0.0, 0.0, 0.0, 1.0]}

execute if items entity @s weapon.offhand stick[custom_data~{riverboat_card:1b}] run scoreboard players add $cards riverboat 1
execute if items entity @s weapon.offhand stick[custom_data~{riverboat_card:1b}] run item replace entity @s weapon.offhand with air

