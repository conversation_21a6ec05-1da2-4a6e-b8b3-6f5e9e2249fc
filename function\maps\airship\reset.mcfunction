#> Restore original propeller state
place template mcm:airship_propeller_1 -649 41 -17

scoreboard players reset @a airship

#> Fans
kill @e[type=marker,tag=airship_fan]

#> Tp Markers
kill @e[type=marker,tag=airship_teleport_beacon]

#> Captain's room
kill @e[type=interaction,tag=captains_room_door]
scoreboard players set captains_room airship 0
summon interaction -635.5 56 -1.3 {Tags:["captains_room_door"],width:3,height:2.1,response:true}

#> Crystals
setblock -685 69 36 air replace
setblock -692 69 17 air replace
setblock -671 32 18 air replace
setblock -708 32 14 air replace
kill @e[type=interaction,tag=airship_crystal_spawn]

#> Kill Remaining crystal displays
kill @e[type=armor_stand,tag=tp_beacon_display_mount]
kill @e[type=item_display,tag=tp_beacon_display]

#> Reset captain's room
scoreboard players set $captain_room CmdData 0
setblock -633 53 -6 lever[powered=false,facing=east]
execute unless block -705 30 13 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] run setblock -705 30 13 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] replace 
execute unless block -705 30 30 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] run setblock -705 30 30 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] replace 
execute unless block -705 30 31 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] run setblock -705 30 31 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] replace 
execute unless block -707 48 19 minecraft:lantern[waterlogged=false,hanging=true] run setblock -707 48 19 minecraft:lantern[waterlogged=false,hanging=true] replace 
execute unless block -705 48 31 minecraft:lantern[waterlogged=false,hanging=true] run setblock -705 48 31 minecraft:lantern[waterlogged=false,hanging=true] replace 
execute unless block -708 68 25 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] run setblock -708 68 25 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] replace 
execute unless block -706 68 25 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] run setblock -706 68 25 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] replace 
execute unless block -708 69 23 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] run setblock -708 69 23 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] replace 
execute unless block -706 69 23 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] run setblock -706 69 23 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] replace 
execute unless block -708 69 27 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -708 69 27 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -706 69 27 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -706 69 27 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -708 70 23 minecraft:ladder[waterlogged=false,facing=north] run setblock -708 70 23 minecraft:ladder[waterlogged=false,facing=north] replace 
execute unless block -706 70 23 minecraft:ladder[waterlogged=false,facing=north] run setblock -706 70 23 minecraft:ladder[waterlogged=false,facing=north] replace 
execute unless block -708 70 27 minecraft:ladder[waterlogged=false,facing=south] run setblock -708 70 27 minecraft:ladder[waterlogged=false,facing=south] replace 
execute unless block -706 70 27 minecraft:ladder[waterlogged=false,facing=south] run setblock -706 70 27 minecraft:ladder[waterlogged=false,facing=south] replace 
execute unless block -708 71 23 minecraft:ladder[waterlogged=false,facing=north] run setblock -708 71 23 minecraft:ladder[waterlogged=false,facing=north] replace 
execute unless block -706 71 23 minecraft:ladder[waterlogged=false,facing=north] run setblock -706 71 23 minecraft:ladder[waterlogged=false,facing=north] replace 
execute unless block -708 71 27 minecraft:ladder[waterlogged=false,facing=south] run setblock -708 71 27 minecraft:ladder[waterlogged=false,facing=south] replace 
execute unless block -706 71 27 minecraft:ladder[waterlogged=false,facing=south] run setblock -706 71 27 minecraft:ladder[waterlogged=false,facing=south] replace 
execute unless block -708 72 23 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=true] run setblock -708 72 23 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=true] replace 
execute unless block -706 72 23 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=true] run setblock -706 72 23 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=true] replace 
execute unless block -708 72 27 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=true] run setblock -708 72 27 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=true] replace 
execute unless block -706 72 27 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=true] run setblock -706 72 27 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=true] replace 
execute unless block -708 73 24 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=false] run setblock -708 73 24 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=false] replace 
execute unless block -706 73 24 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=false] run setblock -706 73 24 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=false] replace 
execute unless block -709 73 25 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] run setblock -709 73 25 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] replace 
execute unless block -708 73 26 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=false] run setblock -708 73 26 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=false] replace 
execute unless block -706 73 26 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=false] run setblock -706 73 26 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=false] replace 
execute unless block -708 74 25 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] run setblock -708 74 25 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] replace 
execute unless block -707 74 25 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] run setblock -707 74 25 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] replace 
execute unless block -706 74 25 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] run setblock -706 74 25 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] replace 
execute unless block -705 74 25 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] run setblock -705 74 25 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] replace 
execute unless block -696 25 10 minecraft:iron_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] run setblock -696 25 10 minecraft:iron_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] replace 
execute unless block -695 25 10 minecraft:iron_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] run setblock -695 25 10 minecraft:iron_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] replace 
execute unless block -694 25 10 minecraft:iron_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] run setblock -694 25 10 minecraft:iron_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] replace 
execute unless block -696 25 11 minecraft:iron_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] run setblock -696 25 11 minecraft:iron_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] replace 
execute unless block -695 25 11 minecraft:iron_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] run setblock -695 25 11 minecraft:iron_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] replace 
execute unless block -694 25 11 minecraft:iron_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] run setblock -694 25 11 minecraft:iron_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] replace 
execute unless block -696 25 12 minecraft:iron_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] run setblock -696 25 12 minecraft:iron_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] replace 
execute unless block -695 25 12 minecraft:iron_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] run setblock -695 25 12 minecraft:iron_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] replace 
execute unless block -694 25 12 minecraft:iron_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] run setblock -694 25 12 minecraft:iron_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] replace 
execute unless block -697 26 9 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=false] run setblock -697 26 9 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=false] replace 
execute unless block -696 26 9 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=false] run setblock -696 26 9 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=false] replace 
execute unless block -695 26 9 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=false] run setblock -695 26 9 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=false] replace 
execute unless block -694 26 9 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=false] run setblock -694 26 9 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=false] replace 
execute unless block -693 26 9 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] run setblock -693 26 9 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] replace 
execute unless block -697 26 10 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] run setblock -697 26 10 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] replace 
execute unless block -696 26 10 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=false] run setblock -696 26 10 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=false] replace 
execute unless block -695 26 10 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=false] run setblock -695 26 10 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=false] replace 
execute unless block -694 26 10 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] run setblock -694 26 10 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] replace 
execute unless block -693 26 10 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] run setblock -693 26 10 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] replace 
execute unless block -697 26 11 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] run setblock -697 26 11 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] replace 
execute unless block -696 26 11 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] run setblock -696 26 11 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] replace 
execute unless block -694 26 11 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] run setblock -694 26 11 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] replace 
execute unless block -693 26 11 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] run setblock -693 26 11 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] replace 
execute unless block -697 26 12 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=false] run setblock -697 26 12 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=false] replace 
execute unless block -696 26 12 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] run setblock -696 26 12 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] replace 
execute unless block -695 26 12 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] run setblock -695 26 12 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] replace 
execute unless block -694 26 12 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] run setblock -694 26 12 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] replace 
execute unless block -693 26 12 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] run setblock -693 26 12 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] replace 
execute unless block -697 26 13 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] run setblock -697 26 13 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] replace 
execute unless block -696 26 13 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] run setblock -696 26 13 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] replace 
execute unless block -695 26 13 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] run setblock -695 26 13 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] replace 
execute unless block -694 26 13 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] run setblock -694 26 13 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] replace 
execute unless block -693 26 13 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] run setblock -693 26 13 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] replace 
execute unless block -702 30 11 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] run setblock -702 30 11 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] replace 
execute unless block -701 30 11 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] run setblock -701 30 11 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] replace 
execute unless block -689 30 11 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] run setblock -689 30 11 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] replace 
execute unless block -704 30 12 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] run setblock -704 30 12 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] replace 
execute unless block -703 30 12 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] run setblock -703 30 12 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] replace 
execute unless block -702 30 12 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] run setblock -702 30 12 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] replace 
execute unless block -701 30 12 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] run setblock -701 30 12 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] replace 
execute unless block -700 30 12 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] run setblock -700 30 12 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] replace 
execute unless block -690 30 12 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] run setblock -690 30 12 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] replace 
execute unless block -689 30 12 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] run setblock -689 30 12 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] replace 
execute unless block -689 30 13 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] run setblock -689 30 13 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] replace 
execute unless block -704 33 14 minecraft:lantern[waterlogged=false,hanging=true] run setblock -704 33 14 minecraft:lantern[waterlogged=false,hanging=true] replace 
execute unless block -695 38 12 minecraft:ladder[waterlogged=false,facing=north] run setblock -695 38 12 minecraft:ladder[waterlogged=false,facing=north] replace 
execute unless block -701 39 11 minecraft:iron_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] run setblock -701 39 11 minecraft:iron_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] replace 
execute unless block -700 39 11 minecraft:iron_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] run setblock -700 39 11 minecraft:iron_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] replace 
execute unless block -699 39 11 minecraft:iron_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] run setblock -699 39 11 minecraft:iron_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] replace 
execute unless block -701 39 12 minecraft:iron_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] run setblock -701 39 12 minecraft:iron_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] replace 
execute unless block -700 39 12 minecraft:iron_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] run setblock -700 39 12 minecraft:iron_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] replace 
execute unless block -699 39 12 minecraft:iron_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] run setblock -699 39 12 minecraft:iron_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] replace 
execute unless block -696 39 12 minecraft:ladder[waterlogged=false,facing=north] run setblock -696 39 12 minecraft:ladder[waterlogged=false,facing=north] replace 
execute unless block -695 39 12 minecraft:ladder[waterlogged=false,facing=north] run setblock -695 39 12 minecraft:ladder[waterlogged=false,facing=north] replace 
execute unless block -701 39 13 minecraft:iron_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] run setblock -701 39 13 minecraft:iron_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] replace 
execute unless block -700 39 13 minecraft:iron_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] run setblock -700 39 13 minecraft:iron_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] replace 
execute unless block -699 39 13 minecraft:iron_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] run setblock -699 39 13 minecraft:iron_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] replace 
execute unless block -702 40 10 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=false] run setblock -702 40 10 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=false] replace 
execute unless block -701 40 10 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=false] run setblock -701 40 10 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=false] replace 
execute unless block -700 40 10 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=false] run setblock -700 40 10 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=false] replace 
execute unless block -699 40 10 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=false] run setblock -699 40 10 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=false] replace 
execute unless block -698 40 10 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] run setblock -698 40 10 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] replace 
execute unless block -702 40 11 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] run setblock -702 40 11 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] replace 
execute unless block -701 40 11 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=false] run setblock -701 40 11 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=false] replace 
execute unless block -700 40 11 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=false] run setblock -700 40 11 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=false] replace 
execute unless block -699 40 11 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] run setblock -699 40 11 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] replace 
execute unless block -698 40 11 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] run setblock -698 40 11 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] replace 
execute unless block -702 40 12 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] run setblock -702 40 12 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] replace 
execute unless block -701 40 12 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] run setblock -701 40 12 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] replace 
execute unless block -699 40 12 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] run setblock -699 40 12 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] replace 
execute unless block -698 40 12 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] run setblock -698 40 12 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] replace 
execute unless block -696 40 12 minecraft:ladder[waterlogged=false,facing=north] run setblock -696 40 12 minecraft:ladder[waterlogged=false,facing=north] replace 
execute unless block -695 40 12 minecraft:ladder[waterlogged=false,facing=north] run setblock -695 40 12 minecraft:ladder[waterlogged=false,facing=north] replace 
execute unless block -694 40 12 minecraft:ladder[waterlogged=false,facing=north] run setblock -694 40 12 minecraft:ladder[waterlogged=false,facing=north] replace 
execute unless block -702 40 13 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] run setblock -702 40 13 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] replace 
execute unless block -701 40 13 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] run setblock -701 40 13 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] replace 
execute unless block -700 40 13 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] run setblock -700 40 13 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] replace 
execute unless block -699 40 13 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] run setblock -699 40 13 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] replace 
execute unless block -698 40 13 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] run setblock -698 40 13 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] replace 
execute unless block -702 40 14 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] run setblock -702 40 14 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] replace 
execute unless block -701 40 14 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] run setblock -701 40 14 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] replace 
execute unless block -700 40 14 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] run setblock -700 40 14 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] replace 
execute unless block -699 40 14 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] run setblock -699 40 14 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] replace 
execute unless block -698 40 14 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] run setblock -698 40 14 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] replace 
execute unless block -696 41 12 minecraft:ladder[waterlogged=false,facing=north] run setblock -696 41 12 minecraft:ladder[waterlogged=false,facing=north] replace 
execute unless block -695 41 12 minecraft:ladder[waterlogged=false,facing=north] run setblock -695 41 12 minecraft:ladder[waterlogged=false,facing=north] replace 
execute unless block -694 41 12 minecraft:ladder[waterlogged=false,facing=north] run setblock -694 41 12 minecraft:ladder[waterlogged=false,facing=north] replace 
execute unless block -696 42 12 minecraft:ladder[waterlogged=false,facing=north] run setblock -696 42 12 minecraft:ladder[waterlogged=false,facing=north] replace 
execute unless block -695 42 12 minecraft:ladder[waterlogged=false,facing=north] run setblock -695 42 12 minecraft:ladder[waterlogged=false,facing=north] replace 
execute unless block -694 42 12 minecraft:ladder[waterlogged=false,facing=north] run setblock -694 42 12 minecraft:ladder[waterlogged=false,facing=north] replace 
execute unless block -696 43 12 minecraft:ladder[waterlogged=false,facing=north] run setblock -696 43 12 minecraft:ladder[waterlogged=false,facing=north] replace 
execute unless block -695 43 12 minecraft:ladder[waterlogged=false,facing=north] run setblock -695 43 12 minecraft:ladder[waterlogged=false,facing=north] replace 
execute unless block -694 43 12 minecraft:ladder[waterlogged=false,facing=north] run setblock -694 43 12 minecraft:ladder[waterlogged=false,facing=north] replace 
execute unless block -696 44 12 minecraft:ladder[waterlogged=false,facing=north] run setblock -696 44 12 minecraft:ladder[waterlogged=false,facing=north] replace 
execute unless block -695 44 12 minecraft:ladder[waterlogged=false,facing=north] run setblock -695 44 12 minecraft:ladder[waterlogged=false,facing=north] replace 
execute unless block -694 44 12 minecraft:ladder[waterlogged=false,facing=north] run setblock -694 44 12 minecraft:ladder[waterlogged=false,facing=north] replace 
execute unless block -696 45 12 minecraft:ladder[waterlogged=false,facing=north] run setblock -696 45 12 minecraft:ladder[waterlogged=false,facing=north] replace 
execute unless block -695 45 12 minecraft:ladder[waterlogged=false,facing=north] run setblock -695 45 12 minecraft:ladder[waterlogged=false,facing=north] replace 
execute unless block -694 45 12 minecraft:ladder[waterlogged=false,facing=north] run setblock -694 45 12 minecraft:ladder[waterlogged=false,facing=north] replace 
execute unless block -696 46 12 minecraft:ladder[waterlogged=false,facing=north] run setblock -696 46 12 minecraft:ladder[waterlogged=false,facing=north] replace 
execute unless block -695 46 12 minecraft:ladder[waterlogged=false,facing=north] run setblock -695 46 12 minecraft:ladder[waterlogged=false,facing=north] replace 
execute unless block -702 56 12 minecraft:ladder[waterlogged=false,facing=east] run setblock -702 56 12 minecraft:ladder[waterlogged=false,facing=east] replace 
execute unless block -702 57 11 minecraft:ladder[waterlogged=false,facing=east] run setblock -702 57 11 minecraft:ladder[waterlogged=false,facing=east] replace 
execute unless block -702 57 12 minecraft:ladder[waterlogged=false,facing=east] run setblock -702 57 12 minecraft:ladder[waterlogged=false,facing=east] replace 
execute unless block -702 57 13 minecraft:ladder[waterlogged=false,facing=east] run setblock -702 57 13 minecraft:ladder[waterlogged=false,facing=east] replace 
execute unless block -690 57 15 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] run setblock -690 57 15 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] replace 
execute unless block -689 57 15 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] run setblock -689 57 15 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] replace 
execute unless block -702 58 11 minecraft:ladder[waterlogged=false,facing=east] run setblock -702 58 11 minecraft:ladder[waterlogged=false,facing=east] replace 
execute unless block -702 58 12 minecraft:ladder[waterlogged=false,facing=east] run setblock -702 58 12 minecraft:ladder[waterlogged=false,facing=east] replace 
execute unless block -702 58 13 minecraft:ladder[waterlogged=false,facing=east] run setblock -702 58 13 minecraft:ladder[waterlogged=false,facing=east] replace 
execute unless block -702 59 11 minecraft:ladder[waterlogged=false,facing=east] run setblock -702 59 11 minecraft:ladder[waterlogged=false,facing=east] replace 
execute unless block -702 59 12 minecraft:ladder[waterlogged=false,facing=east] run setblock -702 59 12 minecraft:ladder[waterlogged=false,facing=east] replace 
execute unless block -702 59 13 minecraft:ladder[waterlogged=false,facing=east] run setblock -702 59 13 minecraft:ladder[waterlogged=false,facing=east] replace 
execute unless block -702 60 11 minecraft:ladder[waterlogged=false,facing=east] run setblock -702 60 11 minecraft:ladder[waterlogged=false,facing=east] replace 
execute unless block -702 60 12 minecraft:ladder[waterlogged=false,facing=east] run setblock -702 60 12 minecraft:ladder[waterlogged=false,facing=east] replace 
execute unless block -702 60 13 minecraft:ladder[waterlogged=false,facing=east] run setblock -702 60 13 minecraft:ladder[waterlogged=false,facing=east] replace 
execute unless block -702 61 11 minecraft:ladder[waterlogged=false,facing=east] run setblock -702 61 11 minecraft:ladder[waterlogged=false,facing=east] replace 
execute unless block -702 61 12 minecraft:ladder[waterlogged=false,facing=east] run setblock -702 61 12 minecraft:ladder[waterlogged=false,facing=east] replace 
execute unless block -702 61 13 minecraft:ladder[waterlogged=false,facing=east] run setblock -702 61 13 minecraft:ladder[waterlogged=false,facing=east] replace 
execute unless block -700 63 13 minecraft:ladder[waterlogged=false,facing=north] run setblock -700 63 13 minecraft:ladder[waterlogged=false,facing=north] replace 
execute unless block -699 63 13 minecraft:ladder[waterlogged=false,facing=north] run setblock -699 63 13 minecraft:ladder[waterlogged=false,facing=north] replace 
execute unless block -701 64 13 minecraft:ladder[waterlogged=false,facing=north] run setblock -701 64 13 minecraft:ladder[waterlogged=false,facing=north] replace 
execute unless block -700 64 13 minecraft:ladder[waterlogged=false,facing=north] run setblock -700 64 13 minecraft:ladder[waterlogged=false,facing=north] replace 
execute unless block -699 64 13 minecraft:ladder[waterlogged=false,facing=north] run setblock -699 64 13 minecraft:ladder[waterlogged=false,facing=north] replace 
execute unless block -701 65 13 minecraft:ladder[waterlogged=false,facing=north] run setblock -701 65 13 minecraft:ladder[waterlogged=false,facing=north] replace 
execute unless block -700 65 13 minecraft:ladder[waterlogged=false,facing=north] run setblock -700 65 13 minecraft:ladder[waterlogged=false,facing=north] replace 
execute unless block -699 65 13 minecraft:ladder[waterlogged=false,facing=north] run setblock -699 65 13 minecraft:ladder[waterlogged=false,facing=north] replace 
execute unless block -701 66 13 minecraft:ladder[waterlogged=false,facing=north] run setblock -701 66 13 minecraft:ladder[waterlogged=false,facing=north] replace 
execute unless block -700 66 13 minecraft:ladder[waterlogged=false,facing=north] run setblock -700 66 13 minecraft:ladder[waterlogged=false,facing=north] replace 
execute unless block -699 66 13 minecraft:ladder[waterlogged=false,facing=north] run setblock -699 66 13 minecraft:ladder[waterlogged=false,facing=north] replace 
execute unless block -701 67 13 minecraft:ladder[waterlogged=false,facing=north] run setblock -701 67 13 minecraft:ladder[waterlogged=false,facing=north] replace 
execute unless block -700 67 13 minecraft:ladder[waterlogged=false,facing=north] run setblock -700 67 13 minecraft:ladder[waterlogged=false,facing=north] replace 
execute unless block -699 67 13 minecraft:ladder[waterlogged=false,facing=north] run setblock -699 67 13 minecraft:ladder[waterlogged=false,facing=north] replace 
execute unless block -694 30 16 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] run setblock -694 30 16 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] replace 
execute unless block -693 30 16 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] run setblock -693 30 16 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] replace 
execute unless block -695 30 17 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] run setblock -695 30 17 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] replace 
execute unless block -694 30 17 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] run setblock -694 30 17 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] replace 
execute unless block -693 30 17 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] run setblock -693 30 17 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] replace 
execute unless block -695 30 18 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] run setblock -695 30 18 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] replace 
execute unless block -694 30 18 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] run setblock -694 30 18 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] replace 
execute unless block -694 30 19 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] run setblock -694 30 19 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] replace 
execute unless block -691 30 26 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] run setblock -691 30 26 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] replace 
execute unless block -691 30 27 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] run setblock -691 30 27 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] replace 
execute unless block -690 30 27 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] run setblock -690 30 27 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] replace 
execute unless block -692 30 28 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] run setblock -692 30 28 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] replace 
execute unless block -691 30 28 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] run setblock -691 30 28 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] replace 
execute unless block -690 30 28 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] run setblock -690 30 28 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] replace 
execute unless block -692 30 29 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] run setblock -692 30 29 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] replace 
execute unless block -691 30 29 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] run setblock -691 30 29 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] replace 
execute unless block -704 30 30 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] run setblock -704 30 30 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] replace 
execute unless block -703 30 30 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] run setblock -703 30 30 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] replace 
execute unless block -704 30 31 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] run setblock -704 30 31 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] replace 
execute unless block -703 30 31 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] run setblock -703 30 31 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] replace 
execute unless block -690 30 31 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] run setblock -690 30 31 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] replace 
execute unless block -689 30 31 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] run setblock -689 30 31 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] replace 
execute unless block -690 32 21 minecraft:lantern[waterlogged=false,hanging=true] run setblock -690 32 21 minecraft:lantern[waterlogged=false,hanging=true] replace 
execute unless block -696 33 19 minecraft:lantern[waterlogged=false,hanging=true] run setblock -696 33 19 minecraft:lantern[waterlogged=false,hanging=true] replace 
execute unless block -690 56 16 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] run setblock -690 56 16 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] replace 
execute unless block -689 56 16 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] run setblock -689 56 16 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] replace 
execute unless block -690 57 16 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] run setblock -690 57 16 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] replace 
execute unless block -690 57 17 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -690 57 17 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -689 57 17 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -689 57 17 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -697 57 23 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] run setblock -697 57 23 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] replace 
execute unless block -696 57 23 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] run setblock -696 57 23 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] replace 
execute unless block -695 57 23 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] run setblock -695 57 23 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] replace 
execute unless block -698 57 24 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] run setblock -698 57 24 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] replace 
execute unless block -694 57 24 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] run setblock -694 57 24 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] replace 
execute unless block -698 57 25 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] run setblock -698 57 25 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] replace 
execute unless block -694 57 25 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] run setblock -694 57 25 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] replace 
execute unless block -698 57 26 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] run setblock -698 57 26 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] replace 
execute unless block -694 57 26 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] run setblock -694 57 26 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] replace 
execute unless block -697 57 27 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -697 57 27 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -696 57 27 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -696 57 27 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -695 57 27 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -695 57 27 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -698 58 24 minecraft:ladder[waterlogged=false,facing=west] run setblock -698 58 24 minecraft:ladder[waterlogged=false,facing=west] replace 
execute unless block -694 58 24 minecraft:ladder[waterlogged=false,facing=east] run setblock -694 58 24 minecraft:ladder[waterlogged=false,facing=east] replace 
execute unless block -698 58 26 minecraft:ladder[waterlogged=false,facing=west] run setblock -698 58 26 minecraft:ladder[waterlogged=false,facing=west] replace 
execute unless block -694 58 26 minecraft:ladder[waterlogged=false,facing=east] run setblock -694 58 26 minecraft:ladder[waterlogged=false,facing=east] replace 
execute unless block -698 59 24 minecraft:ladder[waterlogged=false,facing=west] run setblock -698 59 24 minecraft:ladder[waterlogged=false,facing=west] replace 
execute unless block -694 59 24 minecraft:ladder[waterlogged=false,facing=east] run setblock -694 59 24 minecraft:ladder[waterlogged=false,facing=east] replace 
execute unless block -698 59 26 minecraft:ladder[waterlogged=false,facing=west] run setblock -698 59 26 minecraft:ladder[waterlogged=false,facing=west] replace 
execute unless block -694 59 26 minecraft:ladder[waterlogged=false,facing=east] run setblock -694 59 26 minecraft:ladder[waterlogged=false,facing=east] replace 
execute unless block -690 60 22 minecraft:lantern[waterlogged=false,hanging=true] run setblock -690 60 22 minecraft:lantern[waterlogged=false,hanging=true] replace 
execute unless block -698 60 24 minecraft:ladder[waterlogged=false,facing=west] run setblock -698 60 24 minecraft:ladder[waterlogged=false,facing=west] replace 
execute unless block -694 60 24 minecraft:ladder[waterlogged=false,facing=east] run setblock -694 60 24 minecraft:ladder[waterlogged=false,facing=east] replace 
execute unless block -698 60 26 minecraft:ladder[waterlogged=false,facing=west] run setblock -698 60 26 minecraft:ladder[waterlogged=false,facing=west] replace 
execute unless block -694 60 26 minecraft:ladder[waterlogged=false,facing=east] run setblock -694 60 26 minecraft:ladder[waterlogged=false,facing=east] replace 
execute unless block -691 61 18 minecraft:lantern[waterlogged=false,hanging=true] run setblock -691 61 18 minecraft:lantern[waterlogged=false,hanging=true] replace 
execute unless block -697 67 20 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] run setblock -697 67 20 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] replace 
execute unless block -697 67 21 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] run setblock -697 67 21 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] replace 
execute unless block -689 67 25 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] run setblock -689 67 25 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] replace 
execute unless block -689 67 26 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] run setblock -689 67 26 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] replace 
execute unless block -689 67 27 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] run setblock -689 67 27 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] replace 
execute unless block -694 71 25 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] run setblock -694 71 25 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] replace 
execute unless block -694 72 25 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] run setblock -694 72 25 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] replace 
execute unless block -694 73 25 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] run setblock -694 73 25 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] replace 
execute unless block -704 74 25 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] run setblock -704 74 25 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] replace 
execute unless block -703 74 25 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] run setblock -703 74 25 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] replace 
execute unless block -702 74 25 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] run setblock -702 74 25 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] replace 
execute unless block -701 74 25 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] run setblock -701 74 25 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] replace 
execute unless block -700 74 25 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] run setblock -700 74 25 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] replace 
execute unless block -699 74 25 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] run setblock -699 74 25 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] replace 
execute unless block -698 74 25 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] run setblock -698 74 25 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] replace 
execute unless block -697 74 25 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] run setblock -697 74 25 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] replace 
execute unless block -696 74 25 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] run setblock -696 74 25 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] replace 
execute unless block -695 74 25 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] run setblock -695 74 25 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] replace 
execute unless block -704 30 32 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] run setblock -704 30 32 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] replace 
execute unless block -703 30 32 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] run setblock -703 30 32 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] replace 
execute unless block -702 30 32 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] run setblock -702 30 32 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] replace 
execute unless block -699 33 35 minecraft:lantern[waterlogged=false,hanging=true] run setblock -699 33 35 minecraft:lantern[waterlogged=false,hanging=true] replace 
execute unless block -690 34 33 minecraft:lantern[waterlogged=false,hanging=true] run setblock -690 34 33 minecraft:lantern[waterlogged=false,hanging=true] replace 
execute unless block -694 51 39 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -694 51 39 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -693 51 39 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -693 51 39 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -692 51 39 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -692 51 39 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -694 51 41 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -694 51 41 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -693 51 41 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -693 51 41 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -692 51 41 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -692 51 41 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -694 51 43 minecraft:dark_oak_trapdoor[waterlogged=true,half=bottom,powered=false,facing=south,open=false] run setblock -694 51 43 minecraft:dark_oak_trapdoor[waterlogged=true,half=bottom,powered=false,facing=south,open=false] replace 
execute unless block -693 51 43 minecraft:dark_oak_trapdoor[waterlogged=true,half=bottom,powered=false,facing=south,open=false] run setblock -693 51 43 minecraft:dark_oak_trapdoor[waterlogged=true,half=bottom,powered=false,facing=south,open=false] replace 
execute unless block -692 51 43 minecraft:dark_oak_trapdoor[waterlogged=true,half=bottom,powered=false,facing=south,open=false] run setblock -692 51 43 minecraft:dark_oak_trapdoor[waterlogged=true,half=bottom,powered=false,facing=south,open=false] replace 
execute unless block -694 52 37 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -694 52 37 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -693 52 37 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -693 52 37 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -692 52 37 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -692 52 37 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -694 52 45 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -694 52 45 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -693 52 45 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -693 52 45 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -692 52 45 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -692 52 45 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -694 54 36 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -694 54 36 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -693 54 36 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -693 54 36 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -692 54 36 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -692 54 36 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -694 54 46 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -694 54 46 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -693 54 46 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -693 54 46 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -692 54 46 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -692 54 46 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -694 56 36 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -694 56 36 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -693 56 36 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -693 56 36 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -692 56 36 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -692 56 36 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -694 56 46 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -694 56 46 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -693 56 46 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -693 56 46 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -692 56 46 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -692 56 46 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -694 58 36 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -694 58 36 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -693 58 36 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -693 58 36 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -692 58 36 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -692 58 36 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -694 58 46 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -694 58 46 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -693 58 46 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -693 58 46 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -692 58 46 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -692 58 46 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -694 60 37 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -694 60 37 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -693 60 37 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -693 60 37 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -692 60 37 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -692 60 37 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -694 60 45 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=true] run setblock -694 60 45 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=true] replace 
execute unless block -693 60 45 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=true] run setblock -693 60 45 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=true] replace 
execute unless block -692 60 45 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=true] run setblock -692 60 45 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=true] replace 
execute unless block -694 61 39 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -694 61 39 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -693 61 39 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -693 61 39 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -692 61 39 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -692 61 39 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -694 61 41 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -694 61 41 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -693 61 41 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -693 61 41 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -692 61 41 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -692 61 41 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -694 61 43 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -694 61 43 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -693 61 43 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -693 61 43 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -692 61 43 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -692 61 43 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -690 67 34 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] run setblock -690 67 34 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] replace 
execute unless block -689 67 34 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] run setblock -689 67 34 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] replace 
execute unless block -690 67 35 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] run setblock -690 67 35 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] replace 
execute unless block -689 67 35 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] run setblock -689 67 35 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] replace 
execute unless block -689 67 36 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] run setblock -689 67 36 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] replace 
execute unless block -688 30 12 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] run setblock -688 30 12 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] replace 
execute unless block -688 30 13 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] run setblock -688 30 13 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] replace 
execute unless block -687 30 13 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] run setblock -687 30 13 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] replace 
execute unless block -685 30 14 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] run setblock -685 30 14 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] replace 
execute unless block -684 30 14 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] run setblock -684 30 14 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] replace 
execute unless block -684 30 15 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] run setblock -684 30 15 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] replace 
execute unless block -683 30 15 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] run setblock -683 30 15 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] replace 
execute unless block -681 33 14 minecraft:lantern[waterlogged=false,hanging=true] run setblock -681 33 14 minecraft:lantern[waterlogged=false,hanging=true] replace 
execute unless block -688 57 15 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] run setblock -688 57 15 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] replace 
execute unless block -683 67 13 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] run setblock -683 67 13 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] replace 
execute unless block -682 67 13 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] run setblock -682 67 13 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] replace 
execute unless block -681 67 13 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] run setblock -681 67 13 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] replace 
execute unless block -682 67 14 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] run setblock -682 67 14 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] replace 
execute unless block -681 67 14 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] run setblock -681 67 14 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] replace 
execute unless block -680 67 14 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] run setblock -680 67 14 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] replace 
execute unless block -679 67 15 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] run setblock -679 67 15 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] replace 
execute unless block -676 25 25 minecraft:iron_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] run setblock -676 25 25 minecraft:iron_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] replace 
execute unless block -675 25 25 minecraft:iron_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] run setblock -675 25 25 minecraft:iron_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] replace 
execute unless block -674 25 25 minecraft:iron_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] run setblock -674 25 25 minecraft:iron_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] replace 
execute unless block -676 25 26 minecraft:iron_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] run setblock -676 25 26 minecraft:iron_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] replace 
execute unless block -675 25 26 minecraft:iron_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] run setblock -675 25 26 minecraft:iron_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] replace 
execute unless block -674 25 26 minecraft:iron_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] run setblock -674 25 26 minecraft:iron_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] replace 
execute unless block -676 25 27 minecraft:iron_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] run setblock -676 25 27 minecraft:iron_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] replace 
execute unless block -675 25 27 minecraft:iron_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] run setblock -675 25 27 minecraft:iron_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] replace 
execute unless block -674 25 27 minecraft:iron_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] run setblock -674 25 27 minecraft:iron_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] replace 
execute unless block -677 26 24 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=false] run setblock -677 26 24 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=false] replace 
execute unless block -676 26 24 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=false] run setblock -676 26 24 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=false] replace 
execute unless block -675 26 24 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=false] run setblock -675 26 24 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=false] replace 
execute unless block -674 26 24 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=false] run setblock -674 26 24 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=false] replace 
execute unless block -673 26 24 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] run setblock -673 26 24 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] replace 
execute unless block -677 26 25 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] run setblock -677 26 25 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] replace 
execute unless block -676 26 25 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=false] run setblock -676 26 25 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=false] replace 
execute unless block -675 26 25 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=false] run setblock -675 26 25 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=false] replace 
execute unless block -674 26 25 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] run setblock -674 26 25 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] replace 
execute unless block -673 26 25 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] run setblock -673 26 25 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] replace 
execute unless block -677 26 26 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] run setblock -677 26 26 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] replace 
execute unless block -676 26 26 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] run setblock -676 26 26 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] replace 
execute unless block -674 26 26 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] run setblock -674 26 26 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] replace 
execute unless block -673 26 26 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] run setblock -673 26 26 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] replace 
execute unless block -677 26 27 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] run setblock -677 26 27 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] replace 
execute unless block -676 26 27 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] run setblock -676 26 27 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] replace 
execute unless block -675 26 27 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] run setblock -675 26 27 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] replace 
execute unless block -674 26 27 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] run setblock -674 26 27 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] replace 
execute unless block -673 26 27 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] run setblock -673 26 27 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] replace 
execute unless block -677 26 28 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] run setblock -677 26 28 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] replace 
execute unless block -676 26 28 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] run setblock -676 26 28 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] replace 
execute unless block -675 26 28 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] run setblock -675 26 28 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] replace 
execute unless block -674 26 28 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] run setblock -674 26 28 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] replace 
execute unless block -673 26 28 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] run setblock -673 26 28 minecraft:mangrove_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] replace 
execute unless block -676 30 18 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] run setblock -676 30 18 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] replace 
execute unless block -676 30 19 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] run setblock -676 30 19 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] replace 
execute unless block -675 30 19 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] run setblock -675 30 19 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] replace 
execute unless block -677 30 20 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] run setblock -677 30 20 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] replace 
execute unless block -676 30 20 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] run setblock -676 30 20 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] replace 
execute unless block -675 30 20 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] run setblock -675 30 20 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] replace 
execute unless block -676 30 21 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] run setblock -676 30 21 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] replace 
execute unless block -688 30 30 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] run setblock -688 30 30 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] replace 
execute unless block -687 30 30 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] run setblock -687 30 30 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] replace 
execute unless block -678 30 30 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] run setblock -678 30 30 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] replace 
execute unless block -688 30 31 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] run setblock -688 30 31 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] replace 
execute unless block -687 30 31 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] run setblock -687 30 31 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] replace 
execute unless block -686 30 31 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] run setblock -686 30 31 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] replace 
execute unless block -679 30 31 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] run setblock -679 30 31 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] replace 
execute unless block -678 30 31 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] run setblock -678 30 31 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] replace 
execute unless block -677 30 31 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] run setblock -677 30 31 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] replace 
execute unless block -674 34 19 minecraft:lantern[waterlogged=false,hanging=true] run setblock -674 34 19 minecraft:lantern[waterlogged=false,hanging=true] replace 
execute unless block -683 34 22 minecraft:lantern[waterlogged=false,hanging=true] run setblock -683 34 22 minecraft:lantern[waterlogged=false,hanging=true] replace 
execute unless block -674 37 26 minecraft:ladder[waterlogged=false,facing=west] run setblock -674 37 26 minecraft:ladder[waterlogged=false,facing=west] replace 
execute unless block -674 38 25 minecraft:ladder[waterlogged=false,facing=west] run setblock -674 38 25 minecraft:ladder[waterlogged=false,facing=west] replace 
execute unless block -674 38 26 minecraft:ladder[waterlogged=false,facing=west] run setblock -674 38 26 minecraft:ladder[waterlogged=false,facing=west] replace 
execute unless block -674 39 25 minecraft:ladder[waterlogged=false,facing=west] run setblock -674 39 25 minecraft:ladder[waterlogged=false,facing=west] replace 
execute unless block -674 39 26 minecraft:ladder[waterlogged=false,facing=west] run setblock -674 39 26 minecraft:ladder[waterlogged=false,facing=west] replace 
execute unless block -674 39 27 minecraft:ladder[waterlogged=false,facing=west] run setblock -674 39 27 minecraft:ladder[waterlogged=false,facing=west] replace 
execute unless block -674 40 25 minecraft:ladder[waterlogged=false,facing=west] run setblock -674 40 25 minecraft:ladder[waterlogged=false,facing=west] replace 
execute unless block -674 40 26 minecraft:ladder[waterlogged=false,facing=west] run setblock -674 40 26 minecraft:ladder[waterlogged=false,facing=west] replace 
execute unless block -674 40 27 minecraft:ladder[waterlogged=false,facing=west] run setblock -674 40 27 minecraft:ladder[waterlogged=false,facing=west] replace 
execute unless block -674 41 25 minecraft:ladder[waterlogged=false,facing=west] run setblock -674 41 25 minecraft:ladder[waterlogged=false,facing=west] replace 
execute unless block -674 41 26 minecraft:ladder[waterlogged=false,facing=west] run setblock -674 41 26 minecraft:ladder[waterlogged=false,facing=west] replace 
execute unless block -674 41 27 minecraft:ladder[waterlogged=false,facing=west] run setblock -674 41 27 minecraft:ladder[waterlogged=false,facing=west] replace 
execute unless block -674 42 25 minecraft:ladder[waterlogged=false,facing=west] run setblock -674 42 25 minecraft:ladder[waterlogged=false,facing=west] replace 
execute unless block -674 42 26 minecraft:ladder[waterlogged=false,facing=west] run setblock -674 42 26 minecraft:ladder[waterlogged=false,facing=west] replace 
execute unless block -674 42 27 minecraft:ladder[waterlogged=false,facing=west] run setblock -674 42 27 minecraft:ladder[waterlogged=false,facing=west] replace 
execute unless block -674 43 25 minecraft:ladder[waterlogged=false,facing=west] run setblock -674 43 25 minecraft:ladder[waterlogged=false,facing=west] replace 
execute unless block -674 43 26 minecraft:ladder[waterlogged=false,facing=west] run setblock -674 43 26 minecraft:ladder[waterlogged=false,facing=west] replace 
execute unless block -674 43 27 minecraft:ladder[waterlogged=false,facing=west] run setblock -674 43 27 minecraft:ladder[waterlogged=false,facing=west] replace 
execute unless block -674 44 25 minecraft:ladder[waterlogged=false,facing=west] run setblock -674 44 25 minecraft:ladder[waterlogged=false,facing=west] replace 
execute unless block -674 44 26 minecraft:ladder[waterlogged=false,facing=west] run setblock -674 44 26 minecraft:ladder[waterlogged=false,facing=west] replace 
execute unless block -674 44 27 minecraft:ladder[waterlogged=false,facing=west] run setblock -674 44 27 minecraft:ladder[waterlogged=false,facing=west] replace 
execute unless block -686 51 19 minecraft:lantern[waterlogged=false,hanging=true] run setblock -686 51 19 minecraft:lantern[waterlogged=false,hanging=true] replace 
execute unless block -686 51 31 minecraft:lantern[waterlogged=false,hanging=true] run setblock -686 51 31 minecraft:lantern[waterlogged=false,hanging=true] replace 
execute unless block -688 56 16 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] run setblock -688 56 16 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] replace 
execute unless block -688 57 16 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] run setblock -688 57 16 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] replace 
execute unless block -688 57 17 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -688 57 17 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -678 59 22 minecraft:lantern[waterlogged=false,hanging=true] run setblock -678 59 22 minecraft:lantern[waterlogged=false,hanging=true] replace 
execute unless block -683 61 22 minecraft:lantern[waterlogged=false,hanging=true] run setblock -683 61 22 minecraft:lantern[waterlogged=false,hanging=true] replace 
execute unless block -686 61 30 minecraft:lantern[waterlogged=false,hanging=true] run setblock -686 61 30 minecraft:lantern[waterlogged=false,hanging=true] replace 
execute unless block -681 67 23 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] run setblock -681 67 23 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] replace 
execute unless block -680 67 23 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] run setblock -680 67 23 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] replace 
execute unless block -679 67 23 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] run setblock -679 67 23 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] replace 
execute unless block -682 67 24 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] run setblock -682 67 24 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] replace 
execute unless block -681 67 24 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] run setblock -681 67 24 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] replace 
execute unless block -680 67 24 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] run setblock -680 67 24 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] replace 
execute unless block -679 67 24 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] run setblock -679 67 24 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] replace 
execute unless block -680 67 25 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] run setblock -680 67 25 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] replace 
execute unless block -679 67 25 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] run setblock -679 67 25 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] replace 
execute unless block -678 67 25 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] run setblock -678 67 25 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] replace 
execute unless block -688 67 26 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] run setblock -688 67 26 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] replace 
execute unless block -688 67 27 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] run setblock -688 67 27 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] replace 
execute unless block -688 67 28 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] run setblock -688 67 28 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] replace 
execute unless block -677 72 23 minecraft:lantern[waterlogged=false,hanging=true] run setblock -677 72 23 minecraft:lantern[waterlogged=false,hanging=true] replace 
execute unless block -688 72 26 minecraft:lantern[waterlogged=false,hanging=true] run setblock -688 72 26 minecraft:lantern[waterlogged=false,hanging=true] replace 
execute unless block -687 30 32 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] run setblock -687 30 32 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] replace 
execute unless block -686 30 32 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] run setblock -686 30 32 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] replace 
execute unless block -685 30 32 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] run setblock -685 30 32 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] replace 
execute unless block -679 30 32 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] run setblock -679 30 32 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] replace 
execute unless block -678 30 32 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] run setblock -678 30 32 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] replace 
execute unless block -684 30 33 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] run setblock -684 30 33 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] replace 
execute unless block -679 30 33 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] run setblock -679 30 33 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] replace 
execute unless block -678 30 33 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] run setblock -678 30 33 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] replace 
execute unless block -679 30 34 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] run setblock -679 30 34 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] replace 
execute unless block -682 31 35 minecraft:ladder[waterlogged=false,facing=north] run setblock -682 31 35 minecraft:ladder[waterlogged=false,facing=north] replace 
execute unless block -681 31 35 minecraft:ladder[waterlogged=false,facing=north] run setblock -681 31 35 minecraft:ladder[waterlogged=false,facing=north] replace 
execute unless block -680 31 35 minecraft:ladder[waterlogged=false,facing=north] run setblock -680 31 35 minecraft:ladder[waterlogged=false,facing=north] replace 
execute unless block -682 31 36 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -682 31 36 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -681 31 36 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -681 31 36 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -680 31 36 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -680 31 36 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -682 32 35 minecraft:ladder[waterlogged=false,facing=north] run setblock -682 32 35 minecraft:ladder[waterlogged=false,facing=north] replace 
execute unless block -681 32 35 minecraft:ladder[waterlogged=false,facing=north] run setblock -681 32 35 minecraft:ladder[waterlogged=false,facing=north] replace 
execute unless block -680 32 35 minecraft:ladder[waterlogged=false,facing=north] run setblock -680 32 35 minecraft:ladder[waterlogged=false,facing=north] replace 
execute unless block -682 32 36 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -682 32 36 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -681 32 36 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -681 32 36 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -680 32 36 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -680 32 36 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -682 33 35 minecraft:ladder[waterlogged=false,facing=north] run setblock -682 33 35 minecraft:ladder[waterlogged=false,facing=north] replace 
execute unless block -681 33 35 minecraft:ladder[waterlogged=false,facing=north] run setblock -681 33 35 minecraft:ladder[waterlogged=false,facing=north] replace 
execute unless block -680 33 35 minecraft:ladder[waterlogged=false,facing=north] run setblock -680 33 35 minecraft:ladder[waterlogged=false,facing=north] replace 
execute unless block -682 33 36 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -682 33 36 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -681 33 36 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -681 33 36 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -680 33 36 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -680 33 36 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -682 34 35 minecraft:ladder[waterlogged=false,facing=north] run setblock -682 34 35 minecraft:ladder[waterlogged=false,facing=north] replace 
execute unless block -681 34 35 minecraft:ladder[waterlogged=false,facing=north] run setblock -681 34 35 minecraft:ladder[waterlogged=false,facing=north] replace 
execute unless block -680 34 35 minecraft:ladder[waterlogged=false,facing=north] run setblock -680 34 35 minecraft:ladder[waterlogged=false,facing=north] replace 
execute unless block -682 34 36 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -682 34 36 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -681 34 36 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -681 34 36 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -680 34 36 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -680 34 36 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -682 34 39 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] run setblock -682 34 39 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] replace 
execute unless block -680 34 39 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] run setblock -680 34 39 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] replace 
execute unless block -682 34 40 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] run setblock -682 34 40 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] replace 
execute unless block -680 34 40 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] run setblock -680 34 40 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] replace 
execute unless block -682 34 41 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] run setblock -682 34 41 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] replace 
execute unless block -680 34 41 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] run setblock -680 34 41 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] replace 
execute unless block -684 35 33 minecraft:lantern[waterlogged=false,hanging=true] run setblock -684 35 33 minecraft:lantern[waterlogged=false,hanging=true] replace 
execute unless block -682 35 35 minecraft:ladder[waterlogged=false,facing=north] run setblock -682 35 35 minecraft:ladder[waterlogged=false,facing=north] replace 
execute unless block -681 35 35 minecraft:ladder[waterlogged=false,facing=north] run setblock -681 35 35 minecraft:ladder[waterlogged=false,facing=north] replace 
execute unless block -680 35 35 minecraft:ladder[waterlogged=false,facing=north] run setblock -680 35 35 minecraft:ladder[waterlogged=false,facing=north] replace 
execute unless block -682 35 38 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] run setblock -682 35 38 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] replace 
execute unless block -680 35 38 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] run setblock -680 35 38 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] replace 
execute unless block -682 35 42 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -682 35 42 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -680 35 42 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -680 35 42 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -682 36 38 minecraft:ladder[waterlogged=false,facing=north] run setblock -682 36 38 minecraft:ladder[waterlogged=false,facing=north] replace 
execute unless block -680 36 38 minecraft:ladder[waterlogged=false,facing=north] run setblock -680 36 38 minecraft:ladder[waterlogged=false,facing=north] replace 
execute unless block -682 36 42 minecraft:ladder[waterlogged=false,facing=south] run setblock -682 36 42 minecraft:ladder[waterlogged=false,facing=south] replace 
execute unless block -680 36 42 minecraft:ladder[waterlogged=false,facing=south] run setblock -680 36 42 minecraft:ladder[waterlogged=false,facing=south] replace 
execute unless block -682 37 38 minecraft:ladder[waterlogged=false,facing=north] run setblock -682 37 38 minecraft:ladder[waterlogged=false,facing=north] replace 
execute unless block -680 37 38 minecraft:ladder[waterlogged=false,facing=north] run setblock -680 37 38 minecraft:ladder[waterlogged=false,facing=north] replace 
execute unless block -682 37 42 minecraft:ladder[waterlogged=false,facing=south] run setblock -682 37 42 minecraft:ladder[waterlogged=false,facing=south] replace 
execute unless block -680 37 42 minecraft:ladder[waterlogged=false,facing=south] run setblock -680 37 42 minecraft:ladder[waterlogged=false,facing=south] replace 
execute unless block -682 38 38 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] run setblock -682 38 38 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] replace 
execute unless block -681 38 38 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] run setblock -681 38 38 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] replace 
execute unless block -680 38 38 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] run setblock -680 38 38 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] replace 
execute unless block -682 38 39 minecraft:ladder[waterlogged=false,facing=south] run setblock -682 38 39 minecraft:ladder[waterlogged=false,facing=south] replace 
execute unless block -680 38 39 minecraft:ladder[waterlogged=false,facing=south] run setblock -680 38 39 minecraft:ladder[waterlogged=false,facing=south] replace 
execute unless block -682 38 41 minecraft:ladder[waterlogged=false,facing=north] run setblock -682 38 41 minecraft:ladder[waterlogged=false,facing=north] replace 
execute unless block -680 38 41 minecraft:ladder[waterlogged=false,facing=north] run setblock -680 38 41 minecraft:ladder[waterlogged=false,facing=north] replace 
execute unless block -682 38 42 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] run setblock -682 38 42 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] replace 
execute unless block -681 38 42 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] run setblock -681 38 42 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] replace 
execute unless block -680 38 42 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] run setblock -680 38 42 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] replace 
execute unless block -682 39 37 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] run setblock -682 39 37 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] replace 
execute unless block -681 39 37 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] run setblock -681 39 37 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] replace 
execute unless block -680 39 37 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] run setblock -680 39 37 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] replace 
execute unless block -683 39 38 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] run setblock -683 39 38 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] replace 
execute unless block -679 39 38 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] run setblock -679 39 38 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] replace 
execute unless block -682 39 39 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -682 39 39 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -681 39 39 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -681 39 39 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -680 39 39 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -680 39 39 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -682 39 41 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] run setblock -682 39 41 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] replace 
execute unless block -681 39 41 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] run setblock -681 39 41 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] replace 
execute unless block -680 39 41 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] run setblock -680 39 41 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] replace 
execute unless block -683 39 42 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] run setblock -683 39 42 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] replace 
execute unless block -679 39 42 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] run setblock -679 39 42 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] replace 
execute unless block -682 39 43 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -682 39 43 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -681 39 43 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -681 39 43 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -680 39 43 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -680 39 43 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -682 40 39 minecraft:ladder[waterlogged=false,facing=south] run setblock -682 40 39 minecraft:ladder[waterlogged=false,facing=south] replace 
execute unless block -680 40 39 minecraft:ladder[waterlogged=false,facing=south] run setblock -680 40 39 minecraft:ladder[waterlogged=false,facing=south] replace 
execute unless block -682 40 41 minecraft:ladder[waterlogged=false,facing=north] run setblock -682 40 41 minecraft:ladder[waterlogged=false,facing=north] replace 
execute unless block -680 40 41 minecraft:ladder[waterlogged=false,facing=north] run setblock -680 40 41 minecraft:ladder[waterlogged=false,facing=north] replace 
execute unless block -682 41 38 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] run setblock -682 41 38 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] replace 
execute unless block -681 41 38 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] run setblock -681 41 38 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] replace 
execute unless block -680 41 38 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] run setblock -680 41 38 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] replace 
execute unless block -682 41 39 minecraft:ladder[waterlogged=false,facing=south] run setblock -682 41 39 minecraft:ladder[waterlogged=false,facing=south] replace 
execute unless block -680 41 39 minecraft:ladder[waterlogged=false,facing=south] run setblock -680 41 39 minecraft:ladder[waterlogged=false,facing=south] replace 
execute unless block -682 41 41 minecraft:ladder[waterlogged=false,facing=north] run setblock -682 41 41 minecraft:ladder[waterlogged=false,facing=north] replace 
execute unless block -680 41 41 minecraft:ladder[waterlogged=false,facing=north] run setblock -680 41 41 minecraft:ladder[waterlogged=false,facing=north] replace 
execute unless block -682 41 42 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] run setblock -682 41 42 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] replace 
execute unless block -681 41 42 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] run setblock -681 41 42 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] replace 
execute unless block -680 41 42 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] run setblock -680 41 42 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] replace 
execute unless block -682 42 37 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] run setblock -682 42 37 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] replace 
execute unless block -681 42 37 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] run setblock -681 42 37 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] replace 
execute unless block -680 42 37 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] run setblock -680 42 37 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] replace 
execute unless block -683 42 38 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] run setblock -683 42 38 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] replace 
execute unless block -679 42 38 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] run setblock -679 42 38 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] replace 
execute unless block -682 42 39 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -682 42 39 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -681 42 39 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -681 42 39 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -680 42 39 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -680 42 39 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -682 42 41 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] run setblock -682 42 41 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] replace 
execute unless block -681 42 41 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] run setblock -681 42 41 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] replace 
execute unless block -680 42 41 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] run setblock -680 42 41 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] replace 
execute unless block -683 42 42 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] run setblock -683 42 42 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] replace 
execute unless block -679 42 42 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] run setblock -679 42 42 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] replace 
execute unless block -682 42 43 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -682 42 43 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -681 42 43 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -681 42 43 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -680 42 43 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -680 42 43 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -682 43 39 minecraft:ladder[waterlogged=false,facing=south] run setblock -682 43 39 minecraft:ladder[waterlogged=false,facing=south] replace 
execute unless block -680 43 39 minecraft:ladder[waterlogged=false,facing=south] run setblock -680 43 39 minecraft:ladder[waterlogged=false,facing=south] replace 
execute unless block -682 43 41 minecraft:ladder[waterlogged=false,facing=north] run setblock -682 43 41 minecraft:ladder[waterlogged=false,facing=north] replace 
execute unless block -680 43 41 minecraft:ladder[waterlogged=false,facing=north] run setblock -680 43 41 minecraft:ladder[waterlogged=false,facing=north] replace 
execute unless block -682 44 38 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] run setblock -682 44 38 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] replace 
execute unless block -681 44 38 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] run setblock -681 44 38 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] replace 
execute unless block -680 44 38 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] run setblock -680 44 38 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] replace 
execute unless block -682 44 39 minecraft:ladder[waterlogged=false,facing=south] run setblock -682 44 39 minecraft:ladder[waterlogged=false,facing=south] replace 
execute unless block -680 44 39 minecraft:ladder[waterlogged=false,facing=south] run setblock -680 44 39 minecraft:ladder[waterlogged=false,facing=south] replace 
execute unless block -682 44 41 minecraft:ladder[waterlogged=false,facing=north] run setblock -682 44 41 minecraft:ladder[waterlogged=false,facing=north] replace 
execute unless block -680 44 41 minecraft:ladder[waterlogged=false,facing=north] run setblock -680 44 41 minecraft:ladder[waterlogged=false,facing=north] replace 
execute unless block -682 44 42 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] run setblock -682 44 42 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] replace 
execute unless block -681 44 42 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] run setblock -681 44 42 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] replace 
execute unless block -680 44 42 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] run setblock -680 44 42 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] replace 
execute unless block -682 45 37 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] run setblock -682 45 37 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] replace 
execute unless block -681 45 37 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] run setblock -681 45 37 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] replace 
execute unless block -680 45 37 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] run setblock -680 45 37 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] replace 
execute unless block -683 45 38 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] run setblock -683 45 38 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] replace 
execute unless block -679 45 38 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] run setblock -679 45 38 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] replace 
execute unless block -682 45 39 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -682 45 39 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -681 45 39 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -681 45 39 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -680 45 39 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -680 45 39 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -682 45 41 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] run setblock -682 45 41 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] replace 
execute unless block -681 45 41 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] run setblock -681 45 41 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] replace 
execute unless block -680 45 41 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] run setblock -680 45 41 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] replace 
execute unless block -683 45 42 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] run setblock -683 45 42 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] replace 
execute unless block -679 45 42 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] run setblock -679 45 42 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] replace 
execute unless block -682 45 43 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -682 45 43 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -681 45 43 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -681 45 43 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -680 45 43 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -680 45 43 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -682 46 39 minecraft:ladder[waterlogged=false,facing=south] run setblock -682 46 39 minecraft:ladder[waterlogged=false,facing=south] replace 
execute unless block -680 46 39 minecraft:ladder[waterlogged=false,facing=south] run setblock -680 46 39 minecraft:ladder[waterlogged=false,facing=south] replace 
execute unless block -682 46 41 minecraft:ladder[waterlogged=false,facing=north] run setblock -682 46 41 minecraft:ladder[waterlogged=false,facing=north] replace 
execute unless block -680 46 41 minecraft:ladder[waterlogged=false,facing=north] run setblock -680 46 41 minecraft:ladder[waterlogged=false,facing=north] replace 
execute unless block -682 47 38 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] run setblock -682 47 38 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] replace 
execute unless block -681 47 38 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] run setblock -681 47 38 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] replace 
execute unless block -680 47 38 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] run setblock -680 47 38 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] replace 
execute unless block -682 47 39 minecraft:ladder[waterlogged=false,facing=south] run setblock -682 47 39 minecraft:ladder[waterlogged=false,facing=south] replace 
execute unless block -680 47 39 minecraft:ladder[waterlogged=false,facing=south] run setblock -680 47 39 minecraft:ladder[waterlogged=false,facing=south] replace 
execute unless block -682 47 41 minecraft:ladder[waterlogged=false,facing=north] run setblock -682 47 41 minecraft:ladder[waterlogged=false,facing=north] replace 
execute unless block -680 47 41 minecraft:ladder[waterlogged=false,facing=north] run setblock -680 47 41 minecraft:ladder[waterlogged=false,facing=north] replace 
execute unless block -682 47 42 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] run setblock -682 47 42 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] replace 
execute unless block -681 47 42 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] run setblock -681 47 42 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] replace 
execute unless block -680 47 42 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] run setblock -680 47 42 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] replace 
execute unless block -682 48 37 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] run setblock -682 48 37 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] replace 
execute unless block -681 48 37 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] run setblock -681 48 37 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] replace 
execute unless block -680 48 37 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] run setblock -680 48 37 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] replace 
execute unless block -683 48 38 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] run setblock -683 48 38 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] replace 
execute unless block -679 48 38 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] run setblock -679 48 38 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] replace 
execute unless block -682 48 39 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -682 48 39 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -681 48 39 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -681 48 39 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -680 48 39 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -680 48 39 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -682 48 41 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] run setblock -682 48 41 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] replace 
execute unless block -681 48 41 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] run setblock -681 48 41 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] replace 
execute unless block -680 48 41 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] run setblock -680 48 41 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] replace 
execute unless block -683 48 42 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] run setblock -683 48 42 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] replace 
execute unless block -679 48 42 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] run setblock -679 48 42 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] replace 
execute unless block -682 48 43 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -682 48 43 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -681 48 43 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -681 48 43 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -680 48 43 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -680 48 43 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -682 49 39 minecraft:ladder[waterlogged=false,facing=south] run setblock -682 49 39 minecraft:ladder[waterlogged=false,facing=south] replace 
execute unless block -680 49 39 minecraft:ladder[waterlogged=false,facing=south] run setblock -680 49 39 minecraft:ladder[waterlogged=false,facing=south] replace 
execute unless block -682 49 41 minecraft:ladder[waterlogged=false,facing=north] run setblock -682 49 41 minecraft:ladder[waterlogged=false,facing=north] replace 
execute unless block -680 49 41 minecraft:ladder[waterlogged=false,facing=north] run setblock -680 49 41 minecraft:ladder[waterlogged=false,facing=north] replace 
execute unless block -682 50 38 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] run setblock -682 50 38 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] replace 
execute unless block -681 50 38 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] run setblock -681 50 38 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] replace 
execute unless block -680 50 38 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] run setblock -680 50 38 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] replace 
execute unless block -682 50 39 minecraft:ladder[waterlogged=false,facing=south] run setblock -682 50 39 minecraft:ladder[waterlogged=false,facing=south] replace 
execute unless block -680 50 39 minecraft:ladder[waterlogged=false,facing=south] run setblock -680 50 39 minecraft:ladder[waterlogged=false,facing=south] replace 
execute unless block -682 50 41 minecraft:ladder[waterlogged=false,facing=north] run setblock -682 50 41 minecraft:ladder[waterlogged=false,facing=north] replace 
execute unless block -680 50 41 minecraft:ladder[waterlogged=false,facing=north] run setblock -680 50 41 minecraft:ladder[waterlogged=false,facing=north] replace 
execute unless block -682 50 42 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] run setblock -682 50 42 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] replace 
execute unless block -681 50 42 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] run setblock -681 50 42 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] replace 
execute unless block -680 50 42 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] run setblock -680 50 42 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] replace 
execute unless block -682 51 37 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] run setblock -682 51 37 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] replace 
execute unless block -681 51 37 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] run setblock -681 51 37 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] replace 
execute unless block -680 51 37 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] run setblock -680 51 37 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] replace 
execute unless block -683 51 38 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] run setblock -683 51 38 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] replace 
execute unless block -679 51 38 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] run setblock -679 51 38 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] replace 
execute unless block -682 51 39 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -682 51 39 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -681 51 39 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -681 51 39 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -680 51 39 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -680 51 39 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -682 51 41 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] run setblock -682 51 41 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] replace 
execute unless block -681 51 41 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] run setblock -681 51 41 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] replace 
execute unless block -680 51 41 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] run setblock -680 51 41 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] replace 
execute unless block -683 51 42 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] run setblock -683 51 42 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] replace 
execute unless block -679 51 42 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] run setblock -679 51 42 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] replace 
execute unless block -682 51 43 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -682 51 43 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -681 51 43 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -681 51 43 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -680 51 43 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -680 51 43 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -682 52 39 minecraft:ladder[waterlogged=false,facing=south] run setblock -682 52 39 minecraft:ladder[waterlogged=false,facing=south] replace 
execute unless block -680 52 39 minecraft:ladder[waterlogged=false,facing=south] run setblock -680 52 39 minecraft:ladder[waterlogged=false,facing=south] replace 
execute unless block -682 52 41 minecraft:ladder[waterlogged=false,facing=north] run setblock -682 52 41 minecraft:ladder[waterlogged=false,facing=north] replace 
execute unless block -680 52 41 minecraft:ladder[waterlogged=false,facing=north] run setblock -680 52 41 minecraft:ladder[waterlogged=false,facing=north] replace 
execute unless block -682 53 38 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] run setblock -682 53 38 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] replace 
execute unless block -681 53 38 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] run setblock -681 53 38 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] replace 
execute unless block -680 53 38 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] run setblock -680 53 38 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] replace 
execute unless block -682 53 39 minecraft:ladder[waterlogged=false,facing=south] run setblock -682 53 39 minecraft:ladder[waterlogged=false,facing=south] replace 
execute unless block -680 53 39 minecraft:ladder[waterlogged=false,facing=south] run setblock -680 53 39 minecraft:ladder[waterlogged=false,facing=south] replace 
execute unless block -682 53 41 minecraft:ladder[waterlogged=false,facing=north] run setblock -682 53 41 minecraft:ladder[waterlogged=false,facing=north] replace 
execute unless block -680 53 41 minecraft:ladder[waterlogged=false,facing=north] run setblock -680 53 41 minecraft:ladder[waterlogged=false,facing=north] replace 
execute unless block -682 53 42 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] run setblock -682 53 42 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] replace 
execute unless block -681 53 42 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] run setblock -681 53 42 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] replace 
execute unless block -680 53 42 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] run setblock -680 53 42 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] replace 
execute unless block -682 54 37 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] run setblock -682 54 37 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] replace 
execute unless block -681 54 37 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] run setblock -681 54 37 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] replace 
execute unless block -680 54 37 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] run setblock -680 54 37 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] replace 
execute unless block -683 54 38 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] run setblock -683 54 38 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] replace 
execute unless block -679 54 38 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] run setblock -679 54 38 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] replace 
execute unless block -682 54 39 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -682 54 39 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -681 54 39 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -681 54 39 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -680 54 39 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -680 54 39 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -682 54 41 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] run setblock -682 54 41 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] replace 
execute unless block -681 54 41 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] run setblock -681 54 41 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] replace 
execute unless block -680 54 41 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] run setblock -680 54 41 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] replace 
execute unless block -683 54 42 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] run setblock -683 54 42 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] replace 
execute unless block -679 54 42 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] run setblock -679 54 42 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] replace 
execute unless block -682 54 43 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -682 54 43 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -681 54 43 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -681 54 43 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -680 54 43 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -680 54 43 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -682 55 38 minecraft:ladder[waterlogged=false,facing=north] run setblock -682 55 38 minecraft:ladder[waterlogged=false,facing=north] replace 
execute unless block -680 55 38 minecraft:ladder[waterlogged=false,facing=north] run setblock -680 55 38 minecraft:ladder[waterlogged=false,facing=north] replace 
execute unless block -682 55 42 minecraft:ladder[waterlogged=false,facing=south] run setblock -682 55 42 minecraft:ladder[waterlogged=false,facing=south] replace 
execute unless block -680 55 42 minecraft:ladder[waterlogged=false,facing=south] run setblock -680 55 42 minecraft:ladder[waterlogged=false,facing=south] replace 
execute unless block -682 56 38 minecraft:ladder[waterlogged=false,facing=north] run setblock -682 56 38 minecraft:ladder[waterlogged=false,facing=north] replace 
execute unless block -680 56 38 minecraft:ladder[waterlogged=false,facing=north] run setblock -680 56 38 minecraft:ladder[waterlogged=false,facing=north] replace 
execute unless block -682 56 42 minecraft:ladder[waterlogged=false,facing=south] run setblock -682 56 42 minecraft:ladder[waterlogged=false,facing=south] replace 
execute unless block -680 56 42 minecraft:ladder[waterlogged=false,facing=south] run setblock -680 56 42 minecraft:ladder[waterlogged=false,facing=south] replace 
execute unless block -678 57 36 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] run setblock -678 57 36 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] replace 
execute unless block -677 57 36 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] run setblock -677 57 36 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] replace 
execute unless block -676 57 36 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] run setblock -676 57 36 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] replace 
execute unless block -682 57 38 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] run setblock -682 57 38 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] replace 
execute unless block -680 57 38 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] run setblock -680 57 38 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] replace 
execute unless block -682 57 42 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -682 57 42 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -680 57 42 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -680 57 42 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -678 58 35 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] run setblock -678 58 35 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] replace 
execute unless block -677 58 35 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] run setblock -677 58 35 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] replace 
execute unless block -676 58 35 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] run setblock -676 58 35 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] replace 
execute unless block -678 58 36 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] run setblock -678 58 36 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] replace 
execute unless block -676 58 36 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] run setblock -676 58 36 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] replace 
execute unless block -678 58 37 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -678 58 37 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -677 58 37 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -677 58 37 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -676 58 37 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -676 58 37 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -682 58 39 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=false] run setblock -682 58 39 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=false] replace 
execute unless block -680 58 39 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=false] run setblock -680 58 39 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=false] replace 
execute unless block -682 58 40 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=false] run setblock -682 58 40 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=false] replace 
execute unless block -680 58 40 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=false] run setblock -680 58 40 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=false] replace 
execute unless block -682 58 41 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] run setblock -682 58 41 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] replace 
execute unless block -680 58 41 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] run setblock -680 58 41 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] replace 
execute unless block -688 67 35 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] run setblock -688 67 35 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] replace 
execute unless block -688 67 36 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] run setblock -688 67 36 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] replace 
execute unless block -668 45 -1 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] run setblock -668 45 -1 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] replace 
execute unless block -667 45 -1 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] run setblock -667 45 -1 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] replace 
execute unless block -668 44 0 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] run setblock -668 44 0 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] replace 
execute unless block -668 44 1 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] run setblock -668 44 1 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] replace 
execute unless block -668 44 2 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] run setblock -668 44 2 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] replace 
execute unless block -667 44 2 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] run setblock -667 44 2 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] replace 
execute unless block -667 45 0 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] run setblock -667 45 0 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] replace 
execute unless block -667 45 1 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] run setblock -667 45 1 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] replace 
execute unless block -668 47 15 minecraft:lantern[waterlogged=false,hanging=true] run setblock -668 47 15 minecraft:lantern[waterlogged=false,hanging=true] replace 
execute unless block -657 56 10 minecraft:oak_fence_gate[in_wall=false,powered=false,facing=south,open=false] run setblock -657 56 10 minecraft:oak_fence_gate[in_wall=false,powered=false,facing=south,open=false] replace 
execute unless block -664 56 13 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] run setblock -664 56 13 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] replace 
execute unless block -663 56 13 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] run setblock -663 56 13 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] replace 
execute unless block -665 56 14 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] run setblock -665 56 14 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] replace 
execute unless block -662 56 14 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] run setblock -662 56 14 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] replace 
execute unless block -657 56 14 minecraft:oak_fence_gate[in_wall=false,powered=false,facing=north,open=false] run setblock -657 56 14 minecraft:oak_fence_gate[in_wall=false,powered=false,facing=north,open=false] replace 
execute unless block -665 56 15 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] run setblock -665 56 15 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] replace 
execute unless block -662 56 15 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] run setblock -662 56 15 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] replace 
execute unless block -671 32 31 minecraft:lantern[waterlogged=false,hanging=true] run setblock -671 32 31 minecraft:lantern[waterlogged=false,hanging=true] replace 
execute unless block -658 45 23 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] run setblock -658 45 23 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] replace 
execute unless block -658 45 24 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] run setblock -658 45 24 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] replace 
execute unless block -659 45 25 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -659 45 25 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -671 51 22 minecraft:lantern[waterlogged=false,hanging=true] run setblock -671 51 22 minecraft:lantern[waterlogged=false,hanging=true] replace 
execute unless block -660 51 22 minecraft:ladder[waterlogged=false,facing=south] run setblock -660 51 22 minecraft:ladder[waterlogged=false,facing=south] replace 
execute unless block -668 51 31 minecraft:lantern[waterlogged=false,hanging=true] run setblock -668 51 31 minecraft:lantern[waterlogged=false,hanging=true] replace 
execute unless block -660 52 22 minecraft:ladder[waterlogged=false,facing=south] run setblock -660 52 22 minecraft:ladder[waterlogged=false,facing=south] replace 
execute unless block -659 52 22 minecraft:ladder[waterlogged=false,facing=south] run setblock -659 52 22 minecraft:ladder[waterlogged=false,facing=south] replace 
execute unless block -661 53 22 minecraft:ladder[waterlogged=false,facing=south] run setblock -661 53 22 minecraft:ladder[waterlogged=false,facing=south] replace 
execute unless block -660 53 22 minecraft:ladder[waterlogged=false,facing=south] run setblock -660 53 22 minecraft:ladder[waterlogged=false,facing=south] replace 
execute unless block -659 53 22 minecraft:ladder[waterlogged=false,facing=south] run setblock -659 53 22 minecraft:ladder[waterlogged=false,facing=south] replace 
execute unless block -661 54 22 minecraft:ladder[waterlogged=false,facing=south] run setblock -661 54 22 minecraft:ladder[waterlogged=false,facing=south] replace 
execute unless block -660 54 22 minecraft:ladder[waterlogged=false,facing=south] run setblock -660 54 22 minecraft:ladder[waterlogged=false,facing=south] replace 
execute unless block -659 54 22 minecraft:ladder[waterlogged=false,facing=south] run setblock -659 54 22 minecraft:ladder[waterlogged=false,facing=south] replace 
execute unless block -661 55 22 minecraft:ladder[waterlogged=false,facing=south] run setblock -661 55 22 minecraft:ladder[waterlogged=false,facing=south] replace 
execute unless block -660 55 22 minecraft:ladder[waterlogged=false,facing=south] run setblock -660 55 22 minecraft:ladder[waterlogged=false,facing=south] replace 
execute unless block -659 55 22 minecraft:ladder[waterlogged=false,facing=south] run setblock -659 55 22 minecraft:ladder[waterlogged=false,facing=south] replace 
execute unless block -664 56 16 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -664 56 16 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -663 56 16 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -663 56 16 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -660 56 16 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] run setblock -660 56 16 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] replace 
execute unless block -661 56 17 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] run setblock -661 56 17 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] replace 
execute unless block -659 56 17 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] run setblock -659 56 17 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] replace 
execute unless block -661 56 18 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] run setblock -661 56 18 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] replace 
execute unless block -659 56 18 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] run setblock -659 56 18 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] replace 
execute unless block -661 56 19 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] run setblock -661 56 19 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] replace 
execute unless block -659 56 19 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] run setblock -659 56 19 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] replace 
execute unless block -660 56 20 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -660 56 20 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -672 62 25 minecraft:ladder[waterlogged=false,facing=east] run setblock -672 62 25 minecraft:ladder[waterlogged=false,facing=east] replace 
execute unless block -672 62 26 minecraft:ladder[waterlogged=false,facing=east] run setblock -672 62 26 minecraft:ladder[waterlogged=false,facing=east] replace 
execute unless block -672 63 25 minecraft:ladder[waterlogged=false,facing=east] run setblock -672 63 25 minecraft:ladder[waterlogged=false,facing=east] replace 
execute unless block -672 63 26 minecraft:ladder[waterlogged=false,facing=east] run setblock -672 63 26 minecraft:ladder[waterlogged=false,facing=east] replace 
execute unless block -672 63 27 minecraft:ladder[waterlogged=false,facing=east] run setblock -672 63 27 minecraft:ladder[waterlogged=false,facing=east] replace 
execute unless block -672 64 25 minecraft:ladder[waterlogged=false,facing=east] run setblock -672 64 25 minecraft:ladder[waterlogged=false,facing=east] replace 
execute unless block -672 64 26 minecraft:ladder[waterlogged=false,facing=east] run setblock -672 64 26 minecraft:ladder[waterlogged=false,facing=east] replace 
execute unless block -672 64 27 minecraft:ladder[waterlogged=false,facing=east] run setblock -672 64 27 minecraft:ladder[waterlogged=false,facing=east] replace 
execute unless block -672 65 25 minecraft:ladder[waterlogged=false,facing=east] run setblock -672 65 25 minecraft:ladder[waterlogged=false,facing=east] replace 
execute unless block -672 65 26 minecraft:ladder[waterlogged=false,facing=east] run setblock -672 65 26 minecraft:ladder[waterlogged=false,facing=east] replace 
execute unless block -672 65 27 minecraft:ladder[waterlogged=false,facing=east] run setblock -672 65 27 minecraft:ladder[waterlogged=false,facing=east] replace 
execute unless block -672 66 25 minecraft:ladder[waterlogged=false,facing=east] run setblock -672 66 25 minecraft:ladder[waterlogged=false,facing=east] replace 
execute unless block -672 66 26 minecraft:ladder[waterlogged=false,facing=east] run setblock -672 66 26 minecraft:ladder[waterlogged=false,facing=east] replace 
execute unless block -672 66 27 minecraft:ladder[waterlogged=false,facing=east] run setblock -672 66 27 minecraft:ladder[waterlogged=false,facing=east] replace 
execute unless block -672 67 25 minecraft:ladder[waterlogged=false,facing=east] run setblock -672 67 25 minecraft:ladder[waterlogged=false,facing=east] replace 
execute unless block -672 67 26 minecraft:ladder[waterlogged=false,facing=east] run setblock -672 67 26 minecraft:ladder[waterlogged=false,facing=east] replace 
execute unless block -672 67 27 minecraft:ladder[waterlogged=false,facing=east] run setblock -672 67 27 minecraft:ladder[waterlogged=false,facing=east] replace 
execute unless block -649 44 -17 minecraft:ladder[waterlogged=false,facing=east] run setblock -649 44 -17 minecraft:ladder[waterlogged=false,facing=east] replace 
execute unless block -649 45 -17 minecraft:ladder[waterlogged=false,facing=east] run setblock -649 45 -17 minecraft:ladder[waterlogged=false,facing=east] replace 
execute unless block -649 46 -17 minecraft:ladder[waterlogged=false,facing=east] run setblock -649 46 -17 minecraft:ladder[waterlogged=false,facing=east] replace 
execute unless block -649 47 -17 minecraft:ladder[waterlogged=false,facing=east] run setblock -649 47 -17 minecraft:ladder[waterlogged=false,facing=east] replace 
execute unless block -649 48 -17 minecraft:ladder[waterlogged=false,facing=east] run setblock -649 48 -17 minecraft:ladder[waterlogged=false,facing=east] replace 
execute unless block -649 49 -17 minecraft:ladder[waterlogged=false,facing=east] run setblock -649 49 -17 minecraft:ladder[waterlogged=false,facing=east] replace 
execute unless block -653 81 -21 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=true] run setblock -653 81 -21 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=true] replace 
execute unless block -656 81 -20 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] run setblock -656 81 -20 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] replace 
execute unless block -656 81 -19 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] run setblock -656 81 -19 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] replace 
execute unless block -656 81 -18 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] run setblock -656 81 -18 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] replace 
execute unless block -656 81 -17 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] run setblock -656 81 -17 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] replace 
execute unless block -649 44 -16 minecraft:ladder[waterlogged=false,facing=east] run setblock -649 44 -16 minecraft:ladder[waterlogged=false,facing=east] replace 
execute unless block -649 45 -16 minecraft:ladder[waterlogged=false,facing=east] run setblock -649 45 -16 minecraft:ladder[waterlogged=false,facing=east] replace 
execute unless block -649 45 -15 minecraft:ladder[waterlogged=false,facing=east] run setblock -649 45 -15 minecraft:ladder[waterlogged=false,facing=east] replace 
execute unless block -649 46 -16 minecraft:ladder[waterlogged=false,facing=east] run setblock -649 46 -16 minecraft:ladder[waterlogged=false,facing=east] replace 
execute unless block -649 46 -15 minecraft:ladder[waterlogged=false,facing=east] run setblock -649 46 -15 minecraft:ladder[waterlogged=false,facing=east] replace 
execute unless block -649 47 -16 minecraft:ladder[waterlogged=false,facing=east] run setblock -649 47 -16 minecraft:ladder[waterlogged=false,facing=east] replace 
execute unless block -649 47 -15 minecraft:ladder[waterlogged=false,facing=east] run setblock -649 47 -15 minecraft:ladder[waterlogged=false,facing=east] replace 
execute unless block -649 48 -16 minecraft:ladder[waterlogged=false,facing=east] run setblock -649 48 -16 minecraft:ladder[waterlogged=false,facing=east] replace 
execute unless block -649 48 -15 minecraft:ladder[waterlogged=false,facing=east] run setblock -649 48 -15 minecraft:ladder[waterlogged=false,facing=east] replace 
execute unless block -649 49 -16 minecraft:ladder[waterlogged=false,facing=east] run setblock -649 49 -16 minecraft:ladder[waterlogged=false,facing=east] replace 
execute unless block -649 49 -15 minecraft:ladder[waterlogged=false,facing=east] run setblock -649 49 -15 minecraft:ladder[waterlogged=false,facing=east] replace 
execute unless block -641 52 -11 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] run setblock -641 52 -11 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] replace 
execute unless block -641 52 -10 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] run setblock -641 52 -10 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] replace 
execute unless block -642 52 -8 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] run setblock -642 52 -8 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] replace 
execute unless block -642 52 -7 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] run setblock -642 52 -7 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] replace 
execute unless block -643 52 -5 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] run setblock -643 52 -5 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] replace 
execute unless block -643 52 -4 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] run setblock -643 52 -4 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] replace 
execute unless block -642 56 -6 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] run setblock -642 56 -6 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] replace 
execute unless block -642 56 -5 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] run setblock -642 56 -5 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] replace 
execute unless block -642 56 -3 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] run setblock -642 56 -3 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] replace 
execute unless block -642 56 -2 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] run setblock -642 56 -2 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] replace 
execute unless block -642 57 -6 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] run setblock -642 57 -6 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] replace 
execute unless block -642 57 -5 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] run setblock -642 57 -5 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] replace 
execute unless block -642 57 -3 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] run setblock -642 57 -3 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] replace 
execute unless block -642 57 -2 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] run setblock -642 57 -2 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] replace 
execute unless block -642 58 -6 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] run setblock -642 58 -6 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] replace 
execute unless block -642 58 -5 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] run setblock -642 58 -5 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] replace 
execute unless block -642 58 -3 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] run setblock -642 58 -3 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] replace 
execute unless block -642 58 -2 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] run setblock -642 58 -2 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] replace 
execute unless block -642 59 -6 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] run setblock -642 59 -6 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] replace 
execute unless block -642 59 -5 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] run setblock -642 59 -5 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] replace 
execute unless block -642 59 -3 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] run setblock -642 59 -3 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] replace 
execute unless block -642 59 -2 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] run setblock -642 59 -2 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] replace 
execute unless block -641 59 -1 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] run setblock -641 59 -1 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] replace 
execute unless block -641 61 -6 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] run setblock -641 61 -6 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] replace 
execute unless block -642 61 -5 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] run setblock -642 61 -5 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] replace 
execute unless block -642 61 -4 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] run setblock -642 61 -4 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] replace 
execute unless block -642 61 -3 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] run setblock -642 61 -3 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] replace 
execute unless block -641 61 -2 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -641 61 -2 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -656 81 -16 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] run setblock -656 81 -16 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] replace 
execute unless block -650 81 -16 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=true] run setblock -650 81 -16 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=true] replace 
execute unless block -656 81 -15 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] run setblock -656 81 -15 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] replace 
execute unless block -656 81 -14 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] run setblock -656 81 -14 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] replace 
execute unless block -649 81 -14 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=true] run setblock -649 81 -14 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=true] replace 
execute unless block -656 81 -13 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] run setblock -656 81 -13 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] replace 
execute unless block -656 81 -12 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] run setblock -656 81 -12 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] replace 
execute unless block -656 81 -11 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] run setblock -656 81 -11 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] replace 
execute unless block -655 81 -3 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] run setblock -655 81 -3 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] replace 
execute unless block -655 81 -2 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] run setblock -655 81 -2 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] replace 
execute unless block -655 81 -1 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] run setblock -655 81 -1 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] replace 
execute unless block -642 49 8 minecraft:ladder[waterlogged=false,facing=east] run setblock -642 49 8 minecraft:ladder[waterlogged=false,facing=east] replace 
execute unless block -642 50 8 minecraft:ladder[waterlogged=false,facing=east] run setblock -642 50 8 minecraft:ladder[waterlogged=false,facing=east] replace 
execute unless block -642 51 8 minecraft:ladder[waterlogged=false,facing=east] run setblock -642 51 8 minecraft:ladder[waterlogged=false,facing=east] replace 
execute unless block -642 52 8 minecraft:ladder[waterlogged=false,facing=east] run setblock -642 52 8 minecraft:ladder[waterlogged=false,facing=east] replace 
execute unless block -642 53 8 minecraft:ladder[waterlogged=false,facing=east] run setblock -642 53 8 minecraft:ladder[waterlogged=false,facing=east] replace 
execute unless block -642 54 8 minecraft:ladder[waterlogged=false,facing=east] run setblock -642 54 8 minecraft:ladder[waterlogged=false,facing=east] replace 
execute unless block -642 55 8 minecraft:spruce_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=true] run setblock -642 55 8 minecraft:spruce_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=true] replace 
execute unless block -656 56 10 minecraft:oak_fence_gate[in_wall=false,powered=false,facing=south,open=false] run setblock -656 56 10 minecraft:oak_fence_gate[in_wall=false,powered=false,facing=south,open=false] replace 
execute unless block -654 56 10 minecraft:oak_fence_gate[in_wall=false,powered=false,facing=south,open=false] run setblock -654 56 10 minecraft:oak_fence_gate[in_wall=false,powered=false,facing=south,open=false] replace 
execute unless block -653 56 10 minecraft:oak_fence_gate[in_wall=false,powered=false,facing=south,open=false] run setblock -653 56 10 minecraft:oak_fence_gate[in_wall=false,powered=false,facing=south,open=false] replace 
execute unless block -648 56 12 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] run setblock -648 56 12 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] replace 
execute unless block -647 56 12 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] run setblock -647 56 12 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] replace 
execute unless block -646 56 12 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] run setblock -646 56 12 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] replace 
execute unless block -649 56 13 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] run setblock -649 56 13 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] replace 
execute unless block -645 56 13 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] run setblock -645 56 13 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] replace 
execute unless block -656 56 14 minecraft:oak_fence_gate[in_wall=false,powered=false,facing=north,open=false] run setblock -656 56 14 minecraft:oak_fence_gate[in_wall=false,powered=false,facing=north,open=false] replace 
execute unless block -654 56 14 minecraft:oak_fence_gate[in_wall=false,powered=false,facing=north,open=false] run setblock -654 56 14 minecraft:oak_fence_gate[in_wall=false,powered=false,facing=north,open=false] replace 
execute unless block -653 56 14 minecraft:oak_fence_gate[in_wall=false,powered=false,facing=north,open=false] run setblock -653 56 14 minecraft:oak_fence_gate[in_wall=false,powered=false,facing=north,open=false] replace 
execute unless block -654 81 4 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] run setblock -654 81 4 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] replace 
execute unless block -654 81 5 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] run setblock -654 81 5 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] replace 
execute unless block -642 52 28 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] run setblock -642 52 28 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] replace 
execute unless block -642 52 29 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] run setblock -642 52 29 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] replace 
execute unless block -642 52 30 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] run setblock -642 52 30 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] replace 
execute unless block -641 52 35 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] run setblock -641 52 35 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] replace 
execute unless block -641 52 36 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] run setblock -641 52 36 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] replace 
execute unless block -641 52 37 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] run setblock -641 52 37 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] replace 
execute unless block -636 54 -17 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] run setblock -636 54 -17 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] replace 
execute unless block -636 98 -21 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=true] run setblock -636 98 -21 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=true] replace 
execute unless block -636 101 -20 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] run setblock -636 101 -20 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] replace 
execute unless block -636 101 -19 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] run setblock -636 101 -19 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] replace 
execute unless block -636 101 -18 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] run setblock -636 101 -18 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] replace 
execute unless block -638 52 -13 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] run setblock -638 52 -13 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] replace 
execute unless block -637 52 -13 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] run setblock -637 52 -13 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] replace 
execute unless block -635 52 -13 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] run setblock -635 52 -13 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] replace 
execute unless block -634 52 -13 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] run setblock -634 52 -13 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] replace 
execute unless block -640 52 -12 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] run setblock -640 52 -12 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] replace 
execute unless block -632 52 -12 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] run setblock -632 52 -12 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] replace 
execute unless block -631 52 -11 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] run setblock -631 52 -11 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] replace 
execute unless block -631 52 -10 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] run setblock -631 52 -10 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] replace 
execute unless block -630 52 -8 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] run setblock -630 52 -8 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] replace 
execute unless block -630 52 -7 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] run setblock -630 52 -7 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] replace 
execute unless block -629 52 -5 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] run setblock -629 52 -5 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] replace 
execute unless block -629 52 -4 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] run setblock -629 52 -4 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] replace 
execute unless block -637 53 -8 minecraft:lever[face=wall,powered=true,facing=south] run setblock -637 53 -8 minecraft:lever[face=wall,powered=true,facing=south] replace 
execute unless block -633 53 -6 minecraft:lever[face=wall,powered=false,facing=east] run setblock -633 53 -6 minecraft:lever[face=wall,powered=false,facing=east] replace 
execute unless block -636 54 -8 minecraft:lever[face=wall,powered=false,facing=south] run setblock -636 54 -8 minecraft:lever[face=wall,powered=false,facing=south] replace 
execute unless block -637 56 -8 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -637 56 -8 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -636 56 -8 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -636 56 -8 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -635 56 -8 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -635 56 -8 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -630 56 -6 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] run setblock -630 56 -6 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] replace 
execute unless block -630 56 -5 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] run setblock -630 56 -5 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] replace 
execute unless block -630 56 -3 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] run setblock -630 56 -3 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] replace 
execute unless block -630 56 -2 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] run setblock -630 56 -2 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] replace 
execute unless block -635 57 -9 minecraft:flower_pot run setblock -635 57 -9 minecraft:flower_pot replace 
execute unless block -630 57 -6 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] run setblock -630 57 -6 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] replace 
execute unless block -630 57 -5 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] run setblock -630 57 -5 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] replace 
execute unless block -630 57 -3 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] run setblock -630 57 -3 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] replace 
execute unless block -630 57 -2 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] run setblock -630 57 -2 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] replace 
execute unless block -630 58 -6 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] run setblock -630 58 -6 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] replace 
execute unless block -630 58 -5 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] run setblock -630 58 -5 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] replace 
execute unless block -630 58 -3 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] run setblock -630 58 -3 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] replace 
execute unless block -630 58 -2 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] run setblock -630 58 -2 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] replace 
execute unless block -640 59 -10 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] run setblock -640 59 -10 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] replace 
execute unless block -639 59 -10 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] run setblock -639 59 -10 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] replace 
execute unless block -638 59 -10 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] run setblock -638 59 -10 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] replace 
execute unless block -637 59 -10 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] run setblock -637 59 -10 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] replace 
execute unless block -636 59 -10 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] run setblock -636 59 -10 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] replace 
execute unless block -635 59 -10 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] run setblock -635 59 -10 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] replace 
execute unless block -634 59 -10 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] run setblock -634 59 -10 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] replace 
execute unless block -633 59 -10 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] run setblock -633 59 -10 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] replace 
execute unless block -632 59 -10 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] run setblock -632 59 -10 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] replace 
execute unless block -638 59 -8 minecraft:lantern[waterlogged=false,hanging=true] run setblock -638 59 -8 minecraft:lantern[waterlogged=false,hanging=true] replace 
execute unless block -634 59 -8 minecraft:lantern[waterlogged=false,hanging=true] run setblock -634 59 -8 minecraft:lantern[waterlogged=false,hanging=true] replace 
execute unless block -630 59 -6 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] run setblock -630 59 -6 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] replace 
execute unless block -630 59 -5 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] run setblock -630 59 -5 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] replace 
execute unless block -638 59 -3 minecraft:lantern[waterlogged=false,hanging=true] run setblock -638 59 -3 minecraft:lantern[waterlogged=false,hanging=true] replace 
execute unless block -634 59 -3 minecraft:lantern[waterlogged=false,hanging=true] run setblock -634 59 -3 minecraft:lantern[waterlogged=false,hanging=true] replace 
execute unless block -630 59 -3 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] run setblock -630 59 -3 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] replace 
execute unless block -630 59 -2 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] run setblock -630 59 -2 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] replace 
execute unless block -640 59 -1 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] run setblock -640 59 -1 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] replace 
execute unless block -639 59 -1 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] run setblock -639 59 -1 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] replace 
execute unless block -638 59 -1 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] run setblock -638 59 -1 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] replace 
execute unless block -637 59 -1 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] run setblock -637 59 -1 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] replace 
execute unless block -636 59 -1 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] run setblock -636 59 -1 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] replace 
execute unless block -635 59 -1 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] run setblock -635 59 -1 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] replace 
execute unless block -634 59 -1 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] run setblock -634 59 -1 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] replace 
execute unless block -633 59 -1 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] run setblock -633 59 -1 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] replace 
execute unless block -632 59 -1 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] run setblock -632 59 -1 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] replace 
execute unless block -631 59 -1 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] run setblock -631 59 -1 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] replace 
execute unless block -638 61 -11 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] run setblock -638 61 -11 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] replace 
execute unless block -637 61 -11 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] run setblock -637 61 -11 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] replace 
execute unless block -636 61 -11 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] run setblock -636 61 -11 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] replace 
execute unless block -639 61 -10 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] run setblock -639 61 -10 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] replace 
execute unless block -635 61 -10 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] run setblock -635 61 -10 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] replace 
execute unless block -639 61 -9 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] run setblock -639 61 -9 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] replace 
execute unless block -635 61 -9 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] run setblock -635 61 -9 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] replace 
execute unless block -639 61 -8 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] run setblock -639 61 -8 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] replace 
execute unless block -635 61 -8 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] run setblock -635 61 -8 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] replace 
execute unless block -633 61 -8 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] run setblock -633 61 -8 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] replace 
execute unless block -632 61 -8 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] run setblock -632 61 -8 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] replace 
execute unless block -639 61 -7 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] run setblock -639 61 -7 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] replace 
execute unless block -635 61 -7 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] run setblock -635 61 -7 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] replace 
execute unless block -634 61 -7 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] run setblock -634 61 -7 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] replace 
execute unless block -631 61 -7 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] run setblock -631 61 -7 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] replace 
execute unless block -640 61 -6 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] run setblock -640 61 -6 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] replace 
execute unless block -639 61 -6 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] run setblock -639 61 -6 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] replace 
execute unless block -635 61 -6 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] run setblock -635 61 -6 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] replace 
execute unless block -634 61 -6 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] run setblock -634 61 -6 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] replace 
execute unless block -631 61 -6 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] run setblock -631 61 -6 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] replace 
execute unless block -639 61 -5 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] run setblock -639 61 -5 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] replace 
execute unless block -638 61 -5 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -638 61 -5 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -637 61 -5 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -637 61 -5 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -636 61 -5 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -636 61 -5 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -634 61 -5 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] run setblock -634 61 -5 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] replace 
execute unless block -631 61 -5 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] run setblock -631 61 -5 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] replace 
execute unless block -639 61 -4 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] run setblock -639 61 -4 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] replace 
execute unless block -637 61 -4 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] run setblock -637 61 -4 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] replace 
execute unless block -636 61 -4 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] run setblock -636 61 -4 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] replace 
execute unless block -635 61 -4 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] run setblock -635 61 -4 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] replace 
execute unless block -634 61 -4 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] run setblock -634 61 -4 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] replace 
execute unless block -631 61 -4 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] run setblock -631 61 -4 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] replace 
execute unless block -639 61 -3 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] run setblock -639 61 -3 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] replace 
execute unless block -638 61 -3 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] run setblock -638 61 -3 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] replace 
execute unless block -634 61 -3 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] run setblock -634 61 -3 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] replace 
execute unless block -633 61 -3 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -633 61 -3 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -632 61 -3 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -632 61 -3 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -640 61 -2 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -640 61 -2 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -638 61 -2 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] run setblock -638 61 -2 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] replace 
execute unless block -634 61 -2 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] run setblock -634 61 -2 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] replace 
execute unless block -637 61 -1 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -637 61 -1 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -636 61 -1 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -636 61 -1 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -635 61 -1 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -635 61 -1 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -636 62 -12 minecraft:lantern[waterlogged=false,hanging=true] run setblock -636 62 -12 minecraft:lantern[waterlogged=false,hanging=true] replace 
execute unless block -636 94 -14 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=true] run setblock -636 94 -14 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=true] replace 
execute unless block -636 95 -16 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=true] run setblock -636 95 -16 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=true] replace 
execute unless block -636 100 -3 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] run setblock -636 100 -3 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] replace 
execute unless block -636 100 -2 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] run setblock -636 100 -2 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] replace 
execute unless block -636 100 -1 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] run setblock -636 100 -1 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] replace 
execute unless block -636 101 -13 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] run setblock -636 101 -13 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] replace 
execute unless block -636 101 -12 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] run setblock -636 101 -12 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] replace 
execute unless block -636 101 -11 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] run setblock -636 101 -11 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] replace 
execute unless block -636 101 -10 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] run setblock -636 101 -10 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] replace 
execute unless block -640 49 0 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -640 49 0 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -639 49 0 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -639 49 0 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -638 49 0 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -638 49 0 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -634 49 0 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -634 49 0 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -633 49 0 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -633 49 0 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -632 49 0 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -632 49 0 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -638 49 8 minecraft:ladder[waterlogged=false,facing=west] run setblock -638 49 8 minecraft:ladder[waterlogged=false,facing=west] replace 
execute unless block -634 49 8 minecraft:ladder[waterlogged=false,facing=east] run setblock -634 49 8 minecraft:ladder[waterlogged=false,facing=east] replace 
execute unless block -630 49 8 minecraft:ladder[waterlogged=false,facing=west] run setblock -630 49 8 minecraft:ladder[waterlogged=false,facing=west] replace 
execute unless block -639 50 0 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -639 50 0 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -638 50 0 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -638 50 0 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -634 50 0 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -634 50 0 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -633 50 0 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -633 50 0 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -638 50 8 minecraft:ladder[waterlogged=false,facing=west] run setblock -638 50 8 minecraft:ladder[waterlogged=false,facing=west] replace 
execute unless block -634 50 8 minecraft:ladder[waterlogged=false,facing=east] run setblock -634 50 8 minecraft:ladder[waterlogged=false,facing=east] replace 
execute unless block -630 50 8 minecraft:ladder[waterlogged=false,facing=west] run setblock -630 50 8 minecraft:ladder[waterlogged=false,facing=west] replace 
execute unless block -638 51 0 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -638 51 0 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -634 51 0 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -634 51 0 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -636 51 2 minecraft:spruce_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] run setblock -636 51 2 minecraft:spruce_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] replace 
execute unless block -636 51 3 minecraft:spruce_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] run setblock -636 51 3 minecraft:spruce_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] replace 
execute unless block -636 51 4 minecraft:spruce_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] run setblock -636 51 4 minecraft:spruce_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] replace 
execute unless block -636 51 5 minecraft:spruce_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] run setblock -636 51 5 minecraft:spruce_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] replace 
execute unless block -636 51 6 minecraft:spruce_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] run setblock -636 51 6 minecraft:spruce_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] replace 
execute unless block -638 51 8 minecraft:spruce_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=true] run setblock -638 51 8 minecraft:spruce_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=true] replace 
execute unless block -634 51 8 minecraft:spruce_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=true] run setblock -634 51 8 minecraft:spruce_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=true] replace 
execute unless block -630 51 8 minecraft:ladder[waterlogged=false,facing=west] run setblock -630 51 8 minecraft:ladder[waterlogged=false,facing=west] replace 
execute unless block -636 51 10 minecraft:spruce_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] run setblock -636 51 10 minecraft:spruce_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] replace 
execute unless block -636 51 11 minecraft:spruce_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] run setblock -636 51 11 minecraft:spruce_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] replace 
execute unless block -636 51 12 minecraft:spruce_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] run setblock -636 51 12 minecraft:spruce_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] replace 
execute unless block -636 51 13 minecraft:spruce_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] run setblock -636 51 13 minecraft:spruce_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] replace 
execute unless block -636 51 14 minecraft:spruce_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] run setblock -636 51 14 minecraft:spruce_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] replace 
execute unless block -630 52 8 minecraft:ladder[waterlogged=false,facing=west] run setblock -630 52 8 minecraft:ladder[waterlogged=false,facing=west] replace 
execute unless block -630 53 8 minecraft:ladder[waterlogged=false,facing=west] run setblock -630 53 8 minecraft:ladder[waterlogged=false,facing=west] replace 
execute unless block -639 54 0 minecraft:lantern[waterlogged=false,hanging=true] run setblock -639 54 0 minecraft:lantern[waterlogged=false,hanging=true] replace 
execute unless block -633 54 0 minecraft:lantern[waterlogged=false,hanging=true] run setblock -633 54 0 minecraft:lantern[waterlogged=false,hanging=true] replace 
execute unless block -639 54 8 minecraft:lantern[waterlogged=false,hanging=true] run setblock -639 54 8 minecraft:lantern[waterlogged=false,hanging=true] replace 
execute unless block -633 54 8 minecraft:lantern[waterlogged=false,hanging=true] run setblock -633 54 8 minecraft:lantern[waterlogged=false,hanging=true] replace 
execute unless block -630 54 8 minecraft:ladder[waterlogged=false,facing=west] run setblock -630 54 8 minecraft:ladder[waterlogged=false,facing=west] replace 
execute unless block -638 55 5 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] run setblock -638 55 5 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] replace 
execute unless block -637 55 5 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] run setblock -637 55 5 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] replace 
execute unless block -635 55 5 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] run setblock -635 55 5 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] replace 
execute unless block -634 55 5 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] run setblock -634 55 5 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] replace 
execute unless block -638 55 6 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] run setblock -638 55 6 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] replace 
execute unless block -637 55 6 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] run setblock -637 55 6 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] replace 
execute unless block -635 55 6 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] run setblock -635 55 6 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] replace 
execute unless block -634 55 6 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] run setblock -634 55 6 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] replace 
execute unless block -638 55 7 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] run setblock -638 55 7 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] replace 
execute unless block -637 55 7 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] run setblock -637 55 7 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] replace 
execute unless block -635 55 7 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] run setblock -635 55 7 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] replace 
execute unless block -634 55 7 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] run setblock -634 55 7 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] replace 
execute unless block -638 55 8 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] run setblock -638 55 8 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] replace 
execute unless block -637 55 8 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] run setblock -637 55 8 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] replace 
execute unless block -635 55 8 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] run setblock -635 55 8 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] replace 
execute unless block -634 55 8 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] run setblock -634 55 8 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] replace 
execute unless block -630 55 8 minecraft:spruce_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=true] run setblock -630 55 8 minecraft:spruce_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=true] replace 
execute unless block -638 55 9 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] run setblock -638 55 9 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] replace 
execute unless block -637 55 9 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] run setblock -637 55 9 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] replace 
execute unless block -635 55 9 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] run setblock -635 55 9 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] replace 
execute unless block -634 55 9 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] run setblock -634 55 9 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] replace 
execute unless block -638 55 10 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] run setblock -638 55 10 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] replace 
execute unless block -637 55 10 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] run setblock -637 55 10 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] replace 
execute unless block -635 55 10 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] run setblock -635 55 10 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] replace 
execute unless block -634 55 10 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] run setblock -634 55 10 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] replace 
execute unless block -638 55 11 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] run setblock -638 55 11 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] replace 
execute unless block -637 55 11 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] run setblock -637 55 11 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] replace 
execute unless block -635 55 11 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] run setblock -635 55 11 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] replace 
execute unless block -634 55 11 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] run setblock -634 55 11 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] replace 
execute unless block -638 55 13 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] run setblock -638 55 13 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] replace 
execute unless block -637 55 13 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] run setblock -637 55 13 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] replace 
execute unless block -635 55 13 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] run setblock -635 55 13 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] replace 
execute unless block -634 55 13 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] run setblock -634 55 13 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] replace 
execute unless block -638 55 14 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] run setblock -638 55 14 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] replace 
execute unless block -637 55 14 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] run setblock -637 55 14 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] replace 
execute unless block -635 55 14 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] run setblock -635 55 14 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] replace 
execute unless block -634 55 14 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] run setblock -634 55 14 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] replace 
execute unless block -638 55 15 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] run setblock -638 55 15 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] replace 
execute unless block -637 55 15 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] run setblock -637 55 15 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] replace 
execute unless block -635 55 15 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] run setblock -635 55 15 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] replace 
execute unless block -634 55 15 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] run setblock -634 55 15 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] replace 
execute unless block -637 56 0 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -637 56 0 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -635 56 0 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -635 56 0 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -639 56 1 minecraft:composter[level=0] run setblock -639 56 1 minecraft:composter[level=0] replace 
execute unless block -633 56 1 minecraft:composter[level=0] run setblock -633 56 1 minecraft:composter[level=0] replace 
execute unless block -637 57 0 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -637 57 0 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -635 57 0 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -635 57 0 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -636 59 1 minecraft:lantern[waterlogged=false,hanging=true] run setblock -636 59 1 minecraft:lantern[waterlogged=false,hanging=true] replace 
execute unless block -636 59 15 minecraft:lantern[waterlogged=false,hanging=true] run setblock -636 59 15 minecraft:lantern[waterlogged=false,hanging=true] replace 
execute unless block -636 60 2 minecraft:oak_fence_gate[in_wall=false,powered=false,facing=east,open=false] run setblock -636 60 2 minecraft:oak_fence_gate[in_wall=false,powered=false,facing=east,open=false] replace 
execute unless block -639 61 0 minecraft:oak_fence_gate[in_wall=false,powered=false,facing=north,open=false] run setblock -639 61 0 minecraft:oak_fence_gate[in_wall=false,powered=false,facing=north,open=false] replace 
execute unless block -638 61 0 minecraft:oak_fence_gate[in_wall=false,powered=false,facing=north,open=false] run setblock -638 61 0 minecraft:oak_fence_gate[in_wall=false,powered=false,facing=north,open=false] replace 
execute unless block -634 61 0 minecraft:oak_fence_gate[in_wall=false,powered=false,facing=north,open=false] run setblock -634 61 0 minecraft:oak_fence_gate[in_wall=false,powered=false,facing=north,open=false] replace 
execute unless block -633 61 0 minecraft:oak_fence_gate[in_wall=false,powered=false,facing=north,open=false] run setblock -633 61 0 minecraft:oak_fence_gate[in_wall=false,powered=false,facing=north,open=false] replace 
execute unless block -636 61 4 minecraft:oak_fence_gate[in_wall=false,powered=false,facing=east,open=false] run setblock -636 61 4 minecraft:oak_fence_gate[in_wall=false,powered=false,facing=east,open=false] replace 
execute unless block -636 61 7 minecraft:lantern[waterlogged=false,hanging=true] run setblock -636 61 7 minecraft:lantern[waterlogged=false,hanging=true] replace 
execute unless block -636 61 9 minecraft:lantern[waterlogged=false,hanging=true] run setblock -636 61 9 minecraft:lantern[waterlogged=false,hanging=true] replace 
execute unless block -636 61 11 minecraft:oak_fence_gate[in_wall=false,powered=false,facing=east,open=false] run setblock -636 61 11 minecraft:oak_fence_gate[in_wall=false,powered=false,facing=east,open=false] replace 
execute unless block -636 61 13 minecraft:oak_fence_gate[in_wall=false,powered=false,facing=east,open=false] run setblock -636 61 13 minecraft:oak_fence_gate[in_wall=false,powered=false,facing=east,open=false] replace 
execute unless block -636 62 6 minecraft:oak_fence_gate[in_wall=false,powered=false,facing=east,open=false] run setblock -636 62 6 minecraft:oak_fence_gate[in_wall=false,powered=false,facing=east,open=false] replace 
execute unless block -636 97 13 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] run setblock -636 97 13 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] replace 
execute unless block -636 98 11 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] run setblock -636 98 11 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] replace 
execute unless block -636 99 4 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] run setblock -636 99 4 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] replace 
execute unless block -636 99 5 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] run setblock -636 99 5 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] replace 
execute unless block -638 49 16 minecraft:ladder[waterlogged=false,facing=west] run setblock -638 49 16 minecraft:ladder[waterlogged=false,facing=west] replace 
execute unless block -634 49 16 minecraft:ladder[waterlogged=false,facing=east] run setblock -634 49 16 minecraft:ladder[waterlogged=false,facing=east] replace 
execute unless block -640 49 24 minecraft:ladder[waterlogged=false,facing=north] run setblock -640 49 24 minecraft:ladder[waterlogged=false,facing=north] replace 
execute unless block -639 49 24 minecraft:ladder[waterlogged=false,facing=north] run setblock -639 49 24 minecraft:ladder[waterlogged=false,facing=north] replace 
execute unless block -633 49 24 minecraft:ladder[waterlogged=false,facing=north] run setblock -633 49 24 minecraft:ladder[waterlogged=false,facing=north] replace 
execute unless block -632 49 24 minecraft:ladder[waterlogged=false,facing=north] run setblock -632 49 24 minecraft:ladder[waterlogged=false,facing=north] replace 
execute unless block -633 49 26 minecraft:flower_pot run setblock -633 49 26 minecraft:flower_pot replace 
execute unless block -632 49 28 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -632 49 28 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -639 49 29 minecraft:flower_pot run setblock -639 49 29 minecraft:flower_pot replace 
execute unless block -633 49 30 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] run setblock -633 49 30 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] replace 
execute unless block -638 50 16 minecraft:ladder[waterlogged=false,facing=west] run setblock -638 50 16 minecraft:ladder[waterlogged=false,facing=west] replace 
execute unless block -634 50 16 minecraft:ladder[waterlogged=false,facing=east] run setblock -634 50 16 minecraft:ladder[waterlogged=false,facing=east] replace 
execute unless block -640 50 24 minecraft:ladder[waterlogged=false,facing=north] run setblock -640 50 24 minecraft:ladder[waterlogged=false,facing=north] replace 
execute unless block -639 50 24 minecraft:ladder[waterlogged=false,facing=north] run setblock -639 50 24 minecraft:ladder[waterlogged=false,facing=north] replace 
execute unless block -633 50 24 minecraft:ladder[waterlogged=false,facing=north] run setblock -633 50 24 minecraft:ladder[waterlogged=false,facing=north] replace 
execute unless block -632 50 24 minecraft:ladder[waterlogged=false,facing=north] run setblock -632 50 24 minecraft:ladder[waterlogged=false,facing=north] replace 
execute unless block -636 50 25 minecraft:lantern[waterlogged=false,hanging=true] run setblock -636 50 25 minecraft:lantern[waterlogged=false,hanging=true] replace 
execute unless block -639 50 26 minecraft:flower_pot run setblock -639 50 26 minecraft:flower_pot replace 
execute unless block -632 50 27 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -632 50 27 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -632 50 29 minecraft:spruce_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=true] run setblock -632 50 29 minecraft:spruce_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=true] replace 
execute unless block -638 51 16 minecraft:spruce_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=true] run setblock -638 51 16 minecraft:spruce_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=true] replace 
execute unless block -634 51 16 minecraft:spruce_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=true] run setblock -634 51 16 minecraft:spruce_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=true] replace 
execute unless block -636 51 18 minecraft:spruce_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] run setblock -636 51 18 minecraft:spruce_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] replace 
execute unless block -636 51 19 minecraft:spruce_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] run setblock -636 51 19 minecraft:spruce_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] replace 
execute unless block -636 51 20 minecraft:spruce_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] run setblock -636 51 20 minecraft:spruce_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] replace 
execute unless block -636 51 21 minecraft:spruce_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] run setblock -636 51 21 minecraft:spruce_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] replace 
execute unless block -636 51 22 minecraft:spruce_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] run setblock -636 51 22 minecraft:spruce_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] replace 
execute unless block -640 51 24 minecraft:spruce_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=true] run setblock -640 51 24 minecraft:spruce_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=true] replace 
execute unless block -639 51 24 minecraft:spruce_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=true] run setblock -639 51 24 minecraft:spruce_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=true] replace 
execute unless block -633 51 24 minecraft:spruce_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=true] run setblock -633 51 24 minecraft:spruce_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=true] replace 
execute unless block -632 51 24 minecraft:spruce_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=true] run setblock -632 51 24 minecraft:spruce_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=true] replace 
execute unless block -640 52 24 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] run setblock -640 52 24 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] replace 
execute unless block -640 52 25 minecraft:red_bed[part=head,facing=north,occupied=true] run setblock -640 52 25 minecraft:red_bed[part=head,facing=north,occupied=true] replace 
execute unless block -640 52 26 minecraft:red_bed[part=foot,facing=north,occupied=true] run setblock -640 52 26 minecraft:red_bed[part=foot,facing=north,occupied=true] replace 
execute unless block -640 52 27 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -640 52 27 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -633 52 28 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=true] run setblock -633 52 28 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=true] replace 
execute unless block -630 52 28 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] run setblock -630 52 28 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] replace 
execute unless block -633 52 29 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=true] run setblock -633 52 29 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=true] replace 
execute unless block -630 52 29 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] run setblock -630 52 29 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] replace 
execute unless block -633 52 30 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=true] run setblock -633 52 30 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=true] replace 
execute unless block -630 52 30 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] run setblock -630 52 30 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] replace 
execute unless block -640 52 31 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] run setblock -640 52 31 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] replace 
execute unless block -640 53 24 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] run setblock -640 53 24 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] replace 
execute unless block -640 53 25 minecraft:red_bed[part=head,facing=north,occupied=true] run setblock -640 53 25 minecraft:red_bed[part=head,facing=north,occupied=true] replace 
execute unless block -640 53 26 minecraft:red_bed[part=foot,facing=north,occupied=true] run setblock -640 53 26 minecraft:red_bed[part=foot,facing=north,occupied=true] replace 
execute unless block -640 53 27 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -640 53 27 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -640 53 31 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] run setblock -640 53 31 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] replace 
execute unless block -639 54 16 minecraft:lantern[waterlogged=false,hanging=true] run setblock -639 54 16 minecraft:lantern[waterlogged=false,hanging=true] replace 
execute unless block -633 54 16 minecraft:lantern[waterlogged=false,hanging=true] run setblock -633 54 16 minecraft:lantern[waterlogged=false,hanging=true] replace 
execute unless block -640 54 24 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] run setblock -640 54 24 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] replace 
execute unless block -638 54 24 minecraft:lantern[waterlogged=false,hanging=true] run setblock -638 54 24 minecraft:lantern[waterlogged=false,hanging=true] replace 
execute unless block -634 54 24 minecraft:lantern[waterlogged=false,hanging=true] run setblock -634 54 24 minecraft:lantern[waterlogged=false,hanging=true] replace 
execute unless block -640 54 25 minecraft:red_bed[part=head,facing=north,occupied=true] run setblock -640 54 25 minecraft:red_bed[part=head,facing=north,occupied=true] replace 
execute unless block -640 54 26 minecraft:red_bed[part=foot,facing=north,occupied=true] run setblock -640 54 26 minecraft:red_bed[part=foot,facing=north,occupied=true] replace 
execute unless block -632 54 26 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] run setblock -632 54 26 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] replace 
execute unless block -640 54 27 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -640 54 27 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -632 54 27 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] run setblock -632 54 27 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] replace 
execute unless block -640 54 31 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] run setblock -640 54 31 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] replace 
execute unless block -632 54 31 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] run setblock -632 54 31 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] replace 
execute unless block -638 55 16 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] run setblock -638 55 16 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] replace 
execute unless block -637 55 16 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] run setblock -637 55 16 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] replace 
execute unless block -635 55 16 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] run setblock -635 55 16 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] replace 
execute unless block -634 55 16 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] run setblock -634 55 16 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] replace 
execute unless block -638 55 17 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] run setblock -638 55 17 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] replace 
execute unless block -637 55 17 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] run setblock -637 55 17 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] replace 
execute unless block -635 55 17 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] run setblock -635 55 17 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] replace 
execute unless block -634 55 17 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] run setblock -634 55 17 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] replace 
execute unless block -638 55 18 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] run setblock -638 55 18 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] replace 
execute unless block -637 55 18 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] run setblock -637 55 18 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] replace 
execute unless block -635 55 18 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] run setblock -635 55 18 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] replace 
execute unless block -634 55 18 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] run setblock -634 55 18 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] replace 
execute unless block -638 55 19 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] run setblock -638 55 19 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] replace 
execute unless block -637 55 19 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] run setblock -637 55 19 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] replace 
execute unless block -635 55 19 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] run setblock -635 55 19 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] replace 
execute unless block -634 55 19 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] run setblock -634 55 19 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] replace 
execute unless block -633 56 29 minecraft:flower_pot run setblock -633 56 29 minecraft:flower_pot replace 
execute unless block -633 56 31 minecraft:composter[level=0] run setblock -633 56 31 minecraft:composter[level=0] replace 
execute unless block -632 57 30 minecraft:flower_pot run setblock -632 57 30 minecraft:flower_pot replace 
execute unless block -636 59 17 minecraft:lantern[waterlogged=false,hanging=true] run setblock -636 59 17 minecraft:lantern[waterlogged=false,hanging=true] replace 
execute unless block -636 59 19 minecraft:oak_fence_gate[in_wall=false,powered=false,facing=east,open=false] run setblock -636 59 19 minecraft:oak_fence_gate[in_wall=false,powered=false,facing=east,open=false] replace 
execute unless block -636 59 21 minecraft:oak_fence_gate[in_wall=false,powered=false,facing=west,open=false] run setblock -636 59 21 minecraft:oak_fence_gate[in_wall=false,powered=false,facing=west,open=false] replace 
execute unless block -636 60 23 minecraft:oak_fence_gate[in_wall=false,powered=false,facing=east,open=false] run setblock -636 60 23 minecraft:oak_fence_gate[in_wall=false,powered=false,facing=east,open=false] replace 
execute unless block -636 60 27 minecraft:lantern[waterlogged=false,hanging=true] run setblock -636 60 27 minecraft:lantern[waterlogged=false,hanging=true] replace 
execute unless block -636 61 25 minecraft:oak_fence_gate[in_wall=false,powered=false,facing=east,open=false] run setblock -636 61 25 minecraft:oak_fence_gate[in_wall=false,powered=false,facing=east,open=false] replace 
execute unless block -636 61 26 minecraft:oak_fence_gate[in_wall=false,powered=false,facing=east,open=false] run setblock -636 61 26 minecraft:oak_fence_gate[in_wall=false,powered=false,facing=east,open=false] replace 
execute unless block -636 61 30 minecraft:oak_fence_gate[in_wall=false,powered=false,facing=east,open=false] run setblock -636 61 30 minecraft:oak_fence_gate[in_wall=false,powered=false,facing=east,open=false] replace 
execute unless block -636 49 36 minecraft:flower_pot run setblock -636 49 36 minecraft:flower_pot replace 
execute unless block -633 50 32 minecraft:spruce_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=true] run setblock -633 50 32 minecraft:spruce_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=true] replace 
execute unless block -634 50 35 minecraft:flower_pot run setblock -634 50 35 minecraft:flower_pot replace 
execute unless block -636 50 36 minecraft:lantern[waterlogged=false,hanging=true] run setblock -636 50 36 minecraft:lantern[waterlogged=false,hanging=true] replace 
execute unless block -640 52 32 minecraft:red_bed[part=foot,facing=south,occupied=true] run setblock -640 52 32 minecraft:red_bed[part=foot,facing=south,occupied=true] replace 
execute unless block -640 52 33 minecraft:red_bed[part=head,facing=south,occupied=true] run setblock -640 52 33 minecraft:red_bed[part=head,facing=south,occupied=true] replace 
execute unless block -631 52 35 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] run setblock -631 52 35 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] replace 
execute unless block -631 52 36 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] run setblock -631 52 36 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] replace 
execute unless block -631 52 37 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] run setblock -631 52 37 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] replace 
execute unless block -640 53 32 minecraft:red_bed[part=foot,facing=south,occupied=true] run setblock -640 53 32 minecraft:red_bed[part=foot,facing=south,occupied=true] replace 
execute unless block -640 53 33 minecraft:red_bed[part=head,facing=south,occupied=true] run setblock -640 53 33 minecraft:red_bed[part=head,facing=south,occupied=true] replace 
execute unless block -639 53 37 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=false] run setblock -639 53 37 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=false] replace 
execute unless block -633 53 37 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] run setblock -633 53 37 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] replace 
execute unless block -636 53 39 minecraft:spruce_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] run setblock -636 53 39 minecraft:spruce_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] replace 
execute unless block -640 54 32 minecraft:red_bed[part=foot,facing=south,occupied=true] run setblock -640 54 32 minecraft:red_bed[part=foot,facing=south,occupied=true] replace 
execute unless block -632 54 32 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] run setblock -632 54 32 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] replace 
execute unless block -640 54 33 minecraft:red_bed[part=head,facing=south,occupied=true] run setblock -640 54 33 minecraft:red_bed[part=head,facing=south,occupied=true] replace 
execute unless block -639 54 37 minecraft:lantern[waterlogged=false,hanging=true] run setblock -639 54 37 minecraft:lantern[waterlogged=false,hanging=true] replace 
execute unless block -633 54 37 minecraft:lantern[waterlogged=false,hanging=true] run setblock -633 54 37 minecraft:lantern[waterlogged=false,hanging=true] replace 
execute unless block -639 56 40 minecraft:composter[level=0] run setblock -639 56 40 minecraft:composter[level=0] replace 
execute unless block -638 56 40 minecraft:flower_pot run setblock -638 56 40 minecraft:flower_pot replace 
execute unless block -637 56 42 minecraft:flower_pot run setblock -637 56 42 minecraft:flower_pot replace 
execute unless block -636 62 32 minecraft:oak_fence_gate[in_wall=false,powered=false,facing=east,open=false] run setblock -636 62 32 minecraft:oak_fence_gate[in_wall=false,powered=false,facing=east,open=false] replace 
execute unless block -636 62 33 minecraft:oak_fence_gate[in_wall=false,powered=false,facing=east,open=false] run setblock -636 62 33 minecraft:oak_fence_gate[in_wall=false,powered=false,facing=east,open=false] replace 
execute unless block -636 62 36 minecraft:lantern[waterlogged=false,hanging=true] run setblock -636 62 36 minecraft:lantern[waterlogged=false,hanging=true] replace 
execute unless block -636 63 35 minecraft:oak_fence_gate[in_wall=false,powered=false,facing=east,open=false] run setblock -636 63 35 minecraft:oak_fence_gate[in_wall=false,powered=false,facing=east,open=false] replace 
execute unless block -636 64 37 minecraft:oak_fence_gate[in_wall=false,powered=false,facing=east,open=false] run setblock -636 64 37 minecraft:oak_fence_gate[in_wall=false,powered=false,facing=east,open=false] replace 
execute unless block -636 65 39 minecraft:oak_fence_gate[in_wall=false,powered=false,facing=east,open=false] run setblock -636 65 39 minecraft:oak_fence_gate[in_wall=false,powered=false,facing=east,open=false] replace 
execute unless block -636 65 42 minecraft:lantern[waterlogged=false,hanging=true] run setblock -636 65 42 minecraft:lantern[waterlogged=false,hanging=true] replace 
execute unless block -636 65 47 minecraft:lantern[waterlogged=false,hanging=true] run setblock -636 65 47 minecraft:lantern[waterlogged=false,hanging=true] replace 
execute unless block -636 66 41 minecraft:oak_fence_gate[in_wall=false,powered=false,facing=east,open=false] run setblock -636 66 41 minecraft:oak_fence_gate[in_wall=false,powered=false,facing=east,open=false] replace 
execute unless block -636 67 43 minecraft:oak_fence_gate[in_wall=false,powered=false,facing=east,open=false] run setblock -636 67 43 minecraft:oak_fence_gate[in_wall=false,powered=false,facing=east,open=false] replace 
execute unless block -636 56 57 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] run setblock -636 56 57 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] replace 
execute unless block -636 56 58 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] run setblock -636 56 58 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] replace 
execute unless block -636 56 59 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] run setblock -636 56 59 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] replace 
execute unless block -639 81 56 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] run setblock -639 81 56 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] replace 
execute unless block -633 81 56 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] run setblock -633 81 56 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] replace 
execute unless block -638 81 57 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] run setblock -638 81 57 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] replace 
execute unless block -637 81 57 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] run setblock -637 81 57 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] replace 
execute unless block -635 81 57 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] run setblock -635 81 57 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] replace 
execute unless block -634 81 57 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] run setblock -634 81 57 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] replace 
execute unless block -638 81 58 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] run setblock -638 81 58 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] replace 
execute unless block -637 81 58 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] run setblock -637 81 58 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] replace 
execute unless block -635 81 58 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] run setblock -635 81 58 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] replace 
execute unless block -634 81 58 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] run setblock -634 81 58 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] replace 
execute unless block -637 81 59 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] run setblock -637 81 59 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] replace 
execute unless block -635 81 59 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] run setblock -635 81 59 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] replace 
execute unless block -637 81 60 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] run setblock -637 81 60 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] replace 
execute unless block -635 81 60 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] run setblock -635 81 60 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] replace 
execute unless block -619 81 -21 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=true] run setblock -619 81 -21 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=true] replace 
execute unless block -616 81 -20 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] run setblock -616 81 -20 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] replace 
execute unless block -616 81 -19 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] run setblock -616 81 -19 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] replace 
execute unless block -616 81 -18 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] run setblock -616 81 -18 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] replace 
execute unless block -616 81 -17 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] run setblock -616 81 -17 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] replace 
execute unless block -622 81 -16 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=true] run setblock -622 81 -16 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=true] replace 
execute unless block -616 81 -16 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] run setblock -616 81 -16 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] replace 
execute unless block -616 81 -15 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] run setblock -616 81 -15 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] replace 
execute unless block -623 81 -14 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=true] run setblock -623 81 -14 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=true] replace 
execute unless block -616 81 -14 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] run setblock -616 81 -14 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] replace 
execute unless block -616 81 -13 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] run setblock -616 81 -13 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] replace 
execute unless block -616 81 -12 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] run setblock -616 81 -12 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] replace 
execute unless block -616 81 -11 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] run setblock -616 81 -11 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] replace 
execute unless block -617 81 -3 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] run setblock -617 81 -3 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] replace 
execute unless block -617 81 -2 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] run setblock -617 81 -2 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] replace 
execute unless block -617 81 -1 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] run setblock -617 81 -1 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] replace 
execute unless block -621 51 12 minecraft:ladder[waterlogged=false,facing=east] run setblock -621 51 12 minecraft:ladder[waterlogged=false,facing=east] replace 
execute unless block -621 52 11 minecraft:ladder[waterlogged=false,facing=east] run setblock -621 52 11 minecraft:ladder[waterlogged=false,facing=east] replace 
execute unless block -621 52 12 minecraft:ladder[waterlogged=false,facing=east] run setblock -621 52 12 minecraft:ladder[waterlogged=false,facing=east] replace 
execute unless block -621 53 11 minecraft:ladder[waterlogged=false,facing=east] run setblock -621 53 11 minecraft:ladder[waterlogged=false,facing=east] replace 
execute unless block -621 53 12 minecraft:ladder[waterlogged=false,facing=east] run setblock -621 53 12 minecraft:ladder[waterlogged=false,facing=east] replace 
execute unless block -621 53 13 minecraft:ladder[waterlogged=false,facing=east] run setblock -621 53 13 minecraft:ladder[waterlogged=false,facing=east] replace 
execute unless block -621 54 11 minecraft:ladder[waterlogged=false,facing=east] run setblock -621 54 11 minecraft:ladder[waterlogged=false,facing=east] replace 
execute unless block -621 54 12 minecraft:ladder[waterlogged=false,facing=east] run setblock -621 54 12 minecraft:ladder[waterlogged=false,facing=east] replace 
execute unless block -621 54 13 minecraft:ladder[waterlogged=false,facing=east] run setblock -621 54 13 minecraft:ladder[waterlogged=false,facing=east] replace 
execute unless block -621 55 11 minecraft:ladder[waterlogged=false,facing=east] run setblock -621 55 11 minecraft:ladder[waterlogged=false,facing=east] replace 
execute unless block -621 55 12 minecraft:ladder[waterlogged=false,facing=east] run setblock -621 55 12 minecraft:ladder[waterlogged=false,facing=east] replace 
execute unless block -621 55 13 minecraft:ladder[waterlogged=false,facing=east] run setblock -621 55 13 minecraft:ladder[waterlogged=false,facing=east] replace 
execute unless block -618 81 4 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] run setblock -618 81 4 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] replace 
execute unless block -618 81 5 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] run setblock -618 81 5 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] replace 
execute unless block -602 53 -17 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] run setblock -602 53 -17 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] replace 
execute unless block -598 55 -19 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] run setblock -598 55 -19 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] replace 
execute unless block -594 56 -18 minecraft:spruce_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=true] run setblock -594 56 -18 minecraft:spruce_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=true] replace 
execute unless block -594 57 -18 minecraft:spruce_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=true] run setblock -594 57 -18 minecraft:spruce_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=true] replace 
execute unless block -599 58 -20 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] run setblock -599 58 -20 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] replace 
execute unless block -597 58 -19 minecraft:lantern[waterlogged=false,hanging=true] run setblock -597 58 -19 minecraft:lantern[waterlogged=false,hanging=true] replace 
execute unless block -596 58 -19 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] run setblock -596 58 -19 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] replace 
execute unless block -596 58 -18 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] run setblock -596 58 -18 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] replace 
execute unless block -594 58 -18 minecraft:spruce_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=true] run setblock -594 58 -18 minecraft:spruce_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=true] replace 
execute unless block -594 59 -18 minecraft:spruce_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=true] run setblock -594 59 -18 minecraft:spruce_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=true] replace 
execute unless block -594 66 -17 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] run setblock -594 66 -17 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] replace 
execute unless block -594 70 -17 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] run setblock -594 70 -17 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] replace 
execute unless block -603 53 -16 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -603 53 -16 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -598 54 -16 minecraft:lantern[waterlogged=false,hanging=false] run setblock -598 54 -16 minecraft:lantern[waterlogged=false,hanging=false] replace 
execute unless block -599 56 -16 minecraft:spruce_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=true] run setblock -599 56 -16 minecraft:spruce_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=true] replace 
execute unless block -599 57 -16 minecraft:spruce_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=true] run setblock -599 57 -16 minecraft:spruce_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=true] replace 
execute unless block -600 58 -13 minecraft:lantern[waterlogged=false,hanging=true] run setblock -600 58 -13 minecraft:lantern[waterlogged=false,hanging=true] replace 
execute unless block -594 58 -11 minecraft:lantern[waterlogged=false,hanging=true] run setblock -594 58 -11 minecraft:lantern[waterlogged=false,hanging=true] replace 
execute unless block -603 62 -4 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] run setblock -603 62 -4 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] replace 
execute unless block -603 62 -2 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] run setblock -603 62 -2 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] replace 
execute unless block -599 63 -6 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] run setblock -599 63 -6 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] replace 
execute unless block -598 63 -5 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -598 63 -5 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -604 63 -4 minecraft:ladder[waterlogged=false,facing=south] run setblock -604 63 -4 minecraft:ladder[waterlogged=false,facing=south] replace 
execute unless block -603 63 -4 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] run setblock -603 63 -4 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] replace 
execute unless block -603 63 -2 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] run setblock -603 63 -2 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] replace 
execute unless block -598 63 -1 minecraft:lantern[waterlogged=false,hanging=true] run setblock -598 63 -1 minecraft:lantern[waterlogged=false,hanging=true] replace 
execute unless block -602 64 -12 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -602 64 -12 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -604 64 -4 minecraft:ladder[waterlogged=false,facing=south] run setblock -604 64 -4 minecraft:ladder[waterlogged=false,facing=south] replace 
execute unless block -603 64 -4 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] run setblock -603 64 -4 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] replace 
execute unless block -603 64 -3 minecraft:spruce_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=true] run setblock -603 64 -3 minecraft:spruce_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=true] replace 
execute unless block -603 64 -2 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] run setblock -603 64 -2 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] replace 
execute unless block -605 65 -10 minecraft:lantern[waterlogged=false,hanging=true] run setblock -605 65 -10 minecraft:lantern[waterlogged=false,hanging=true] replace 
execute unless block -606 65 -9 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] run setblock -606 65 -9 minecraft:oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] replace 
execute unless block -604 65 -4 minecraft:ladder[waterlogged=false,facing=south] run setblock -604 65 -4 minecraft:ladder[waterlogged=false,facing=south] replace 
execute unless block -603 65 -3 minecraft:spruce_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=true] run setblock -603 65 -3 minecraft:spruce_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=true] replace 
execute unless block -605 65 -2 minecraft:red_bed[part=foot,facing=east,occupied=true] run setblock -605 65 -2 minecraft:red_bed[part=foot,facing=east,occupied=true] replace 
execute unless block -604 65 -2 minecraft:red_bed[part=head,facing=east,occupied=true] run setblock -604 65 -2 minecraft:red_bed[part=head,facing=east,occupied=true] replace 
execute unless block -595 66 -16 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] run setblock -595 66 -16 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] replace 
execute unless block -593 66 -16 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] run setblock -593 66 -16 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] replace 
execute unless block -594 66 -15 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -594 66 -15 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -599 66 -11 minecraft:lantern[waterlogged=false,hanging=true] run setblock -599 66 -11 minecraft:lantern[waterlogged=false,hanging=true] replace 
execute unless block -605 66 -4 minecraft:lantern[waterlogged=false,hanging=true] run setblock -605 66 -4 minecraft:lantern[waterlogged=false,hanging=true] replace 
execute unless block -606 66 -2 minecraft:potted_bamboo run setblock -606 66 -2 minecraft:potted_bamboo replace 
execute unless block -603 67 -12 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] run setblock -603 67 -12 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] replace 
execute unless block -603 67 -11 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] run setblock -603 67 -11 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] replace 
execute unless block -603 67 -10 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] run setblock -603 67 -10 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] replace 
execute unless block -605 67 -9 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] run setblock -605 67 -9 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] replace 
execute unless block -603 67 -9 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] run setblock -603 67 -9 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] replace 
execute unless block -605 67 -8 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] run setblock -605 67 -8 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] replace 
execute unless block -603 67 -8 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] run setblock -603 67 -8 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] replace 
execute unless block -605 67 -7 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] run setblock -605 67 -7 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] replace 
execute unless block -603 67 -7 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] run setblock -603 67 -7 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] replace 
execute unless block -605 67 -6 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] run setblock -605 67 -6 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] replace 
execute unless block -603 67 -6 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] run setblock -603 67 -6 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=false] replace 
execute unless block -600 67 -4 minecraft:lantern[waterlogged=false,hanging=true] run setblock -600 67 -4 minecraft:lantern[waterlogged=false,hanging=true] replace 
execute unless block -597 68 -16 minecraft:lantern[waterlogged=false,hanging=true] run setblock -597 68 -16 minecraft:lantern[waterlogged=false,hanging=true] replace 
execute unless block -598 70 -15 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] run setblock -598 70 -15 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] replace 
execute unless block -597 70 -15 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] run setblock -597 70 -15 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] replace 
execute unless block -596 70 -15 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] run setblock -596 70 -15 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] replace 
execute unless block -595 70 -15 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] run setblock -595 70 -15 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] replace 
execute unless block -594 70 -15 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] run setblock -594 70 -15 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] replace 
execute unless block -593 70 -15 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] run setblock -593 70 -15 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] replace 
execute unless block -594 71 -5 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] run setblock -594 71 -5 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] replace 
execute unless block -595 71 -4 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -595 71 -4 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -598 75 -3 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] run setblock -598 75 -3 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] replace 
execute unless block -599 75 -2 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -599 75 -2 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -593 75 -2 minecraft:lantern[waterlogged=false,hanging=true] run setblock -593 75 -2 minecraft:lantern[waterlogged=false,hanging=true] replace 
execute unless block -599 60 3 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] run setblock -599 60 3 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] replace 
execute unless block -598 60 4 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] run setblock -598 60 4 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] replace 
execute unless block -597 60 6 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] run setblock -597 60 6 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] replace 
execute unless block -603 62 0 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] run setblock -603 62 0 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] replace 
execute unless block -594 62 0 minecraft:lantern[waterlogged=false,hanging=true] run setblock -594 62 0 minecraft:lantern[waterlogged=false,hanging=true] replace 
execute unless block -602 62 1 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] run setblock -602 62 1 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] replace 
execute unless block -601 62 2 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] run setblock -601 62 2 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] replace 
execute unless block -594 63 5 minecraft:lantern[waterlogged=false,hanging=true] run setblock -594 63 5 minecraft:lantern[waterlogged=false,hanging=true] replace 
execute unless block -593 73 3 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] run setblock -593 73 3 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] replace 
execute unless block -594 73 4 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] run setblock -594 73 4 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] replace 
execute unless block -600 74 1 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] run setblock -600 74 1 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] replace 
execute unless block -601 74 2 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -601 74 2 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -586 55 -15 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] run setblock -586 55 -15 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] replace 
execute unless block -584 56 -14 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] run setblock -584 56 -14 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] replace 
execute unless block -585 56 -13 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] run setblock -585 56 -13 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] replace 
execute unless block -584 56 -12 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -584 56 -12 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -585 57 -15 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] run setblock -585 57 -15 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] replace 
execute unless block -585 57 -11 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] run setblock -585 57 -11 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] replace 
execute unless block -586 57 -10 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] run setblock -586 57 -10 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] replace 
execute unless block -585 57 -7 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] run setblock -585 57 -7 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] replace 
execute unless block -582 57 -7 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] run setblock -582 57 -7 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] replace 
execute unless block -582 57 -6 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] run setblock -582 57 -6 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] replace 
execute unless block -585 57 -5 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] run setblock -585 57 -5 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] replace 
execute unless block -585 57 -4 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] run setblock -585 57 -4 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] replace 
execute unless block -584 57 -3 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] run setblock -584 57 -3 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] replace 
execute unless block -586 57 -2 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] run setblock -586 57 -2 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] replace 
execute unless block -585 57 -2 minecraft:composter[level=0] run setblock -585 57 -2 minecraft:composter[level=0] replace 
execute unless block -584 57 -2 minecraft:composter[level=0] run setblock -584 57 -2 minecraft:composter[level=0] replace 
execute unless block -583 57 -2 minecraft:composter[level=0] run setblock -583 57 -2 minecraft:composter[level=0] replace 
execute unless block -585 57 -1 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -585 57 -1 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -591 58 -16 minecraft:lantern[waterlogged=false,hanging=true] run setblock -591 58 -16 minecraft:lantern[waterlogged=false,hanging=true] replace 
execute unless block -585 58 -16 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] run setblock -585 58 -16 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] replace 
execute unless block -585 58 -11 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] run setblock -585 58 -11 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] replace 
execute unless block -586 58 -10 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] run setblock -586 58 -10 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] replace 
execute unless block -585 58 -7 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] run setblock -585 58 -7 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] replace 
execute unless block -582 58 -7 minecraft:potted_cornflower run setblock -582 58 -7 minecraft:potted_cornflower replace 
execute unless block -585 58 -5 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] run setblock -585 58 -5 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] replace 
execute unless block -582 58 -5 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] run setblock -582 58 -5 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] replace 
execute unless block -582 58 -4 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] run setblock -582 58 -4 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] replace 
execute unless block -582 58 -3 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] run setblock -582 58 -3 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=west,open=false] replace 
execute unless block -592 58 -2 minecraft:dark_oak_fence_gate[in_wall=false,powered=false,facing=east,open=false] run setblock -592 58 -2 minecraft:dark_oak_fence_gate[in_wall=false,powered=false,facing=east,open=false] replace 
execute unless block -586 58 -2 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] run setblock -586 58 -2 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] replace 
execute unless block -585 58 -1 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -585 58 -1 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -588 59 -14 minecraft:lantern[waterlogged=false,hanging=true] run setblock -588 59 -14 minecraft:lantern[waterlogged=false,hanging=true] replace 
execute unless block -585 59 -11 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] run setblock -585 59 -11 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] replace 
execute unless block -586 59 -10 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] run setblock -586 59 -10 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] replace 
execute unless block -585 59 -10 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] run setblock -585 59 -10 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=south,open=false] replace 
execute unless block -583 59 -10 minecraft:potted_crimson_fungus run setblock -583 59 -10 minecraft:potted_crimson_fungus replace 
execute unless block -585 59 -7 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] run setblock -585 59 -7 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] replace 
execute unless block -585 59 -6 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] run setblock -585 59 -6 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] replace 
execute unless block -585 59 -5 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] run setblock -585 59 -5 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] replace 
execute unless block -582 59 -5 minecraft:potted_red_tulip run setblock -582 59 -5 minecraft:potted_red_tulip replace 
execute unless block -582 59 -3 minecraft:potted_lily_of_the_valley run setblock -582 59 -3 minecraft:potted_lily_of_the_valley replace 
execute unless block -589 59 -2 minecraft:lantern[waterlogged=false,hanging=true] run setblock -589 59 -2 minecraft:lantern[waterlogged=false,hanging=true] replace 
execute unless block -586 59 -2 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] run setblock -586 59 -2 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] replace 
execute unless block -585 59 -2 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] run setblock -585 59 -2 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] replace 
execute unless block -584 59 -2 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] run setblock -584 59 -2 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=north,open=false] replace 
execute unless block -585 59 -1 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -585 59 -1 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -585 60 -11 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] run setblock -585 60 -11 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] replace 
execute unless block -586 60 -10 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] run setblock -586 60 -10 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] replace 
execute unless block -585 60 -10 minecraft:potted_crimson_roots run setblock -585 60 -10 minecraft:potted_crimson_roots replace 
execute unless block -585 60 -7 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] run setblock -585 60 -7 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] replace 
execute unless block -585 60 -6 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] run setblock -585 60 -6 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] replace 
execute unless block -583 60 -6 minecraft:lantern[waterlogged=false,hanging=true] run setblock -583 60 -6 minecraft:lantern[waterlogged=false,hanging=true] replace 
execute unless block -585 60 -5 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] run setblock -585 60 -5 minecraft:oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=false] replace 
execute unless block -586 60 -2 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] run setblock -586 60 -2 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] replace 
execute unless block -585 60 -1 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -585 60 -1 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -589 61 -13 minecraft:lantern[waterlogged=false,hanging=true] run setblock -589 61 -13 minecraft:lantern[waterlogged=false,hanging=true] replace 
execute unless block -586 61 -12 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] run setblock -586 61 -12 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] replace 
execute unless block -585 61 -6 minecraft:potted_cactus run setblock -585 61 -6 minecraft:potted_cactus replace 
execute unless block -585 61 -5 minecraft:potted_azalea_bush run setblock -585 61 -5 minecraft:potted_azalea_bush replace 
execute unless block -586 62 -10 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] run setblock -586 62 -10 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] replace 
execute unless block -586 62 -2 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] run setblock -586 62 -2 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] replace 
execute unless block -586 63 -7 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] run setblock -586 63 -7 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] replace 
execute unless block -586 63 -6 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] run setblock -586 63 -6 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] replace 
execute unless block -586 63 -5 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] run setblock -586 63 -5 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] replace 
execute unless block -588 64 -5 minecraft:lantern[waterlogged=false,hanging=true] run setblock -588 64 -5 minecraft:lantern[waterlogged=false,hanging=true] replace 
execute unless block -588 68 -12 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] run setblock -588 68 -12 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] replace 
execute unless block -589 68 -11 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] run setblock -589 68 -11 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] replace 
execute unless block -587 68 -11 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] run setblock -587 68 -11 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] replace 
execute unless block -588 68 -10 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] run setblock -588 68 -10 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=true] replace 
execute unless block -590 69 -12 minecraft:lantern[waterlogged=false,hanging=true] run setblock -590 69 -12 minecraft:lantern[waterlogged=false,hanging=true] replace 
execute unless block -592 69 -11 minecraft:lantern[waterlogged=false,hanging=true] run setblock -592 69 -11 minecraft:lantern[waterlogged=false,hanging=true] replace 
execute unless block -592 70 -15 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] run setblock -592 70 -15 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] replace 
execute unless block -591 70 -15 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] run setblock -591 70 -15 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] replace 
execute unless block -590 70 -15 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] run setblock -590 70 -15 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=south,open=false] replace 
execute unless block -587 70 -5 minecraft:spruce_fence_gate[in_wall=false,powered=false,facing=north,open=false] run setblock -587 70 -5 minecraft:spruce_fence_gate[in_wall=false,powered=false,facing=north,open=false] replace 
execute unless block -588 71 -2 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] run setblock -588 71 -2 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] replace 
execute unless block -589 71 -1 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] run setblock -589 71 -1 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] replace 
execute unless block -591 72 -8 minecraft:lantern[waterlogged=false,hanging=true] run setblock -591 72 -8 minecraft:lantern[waterlogged=false,hanging=true] replace 
execute unless block -588 72 -4 minecraft:lantern[waterlogged=false,hanging=true] run setblock -588 72 -4 minecraft:lantern[waterlogged=false,hanging=true] replace 
execute unless block -587 57 0 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] run setblock -587 57 0 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] replace 
execute unless block -588 57 1 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] run setblock -588 57 1 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] replace 
execute unless block -586 57 1 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] run setblock -586 57 1 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=east,open=true] replace 
execute unless block -586 58 1 minecraft:lantern[waterlogged=false,hanging=true] run setblock -586 58 1 minecraft:lantern[waterlogged=false,hanging=true] replace 
execute unless block -588 58 2 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] run setblock -588 58 2 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=north,open=true] replace 
execute unless block -589 58 3 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] run setblock -589 58 3 minecraft:dark_oak_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=true] replace 
execute unless block -587 58 3 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=true] run setblock -587 58 3 minecraft:dark_oak_trapdoor[waterlogged=false,half=top,powered=false,facing=east,open=true] replace 
execute unless block -586 61 0 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] run setblock -586 61 0 minecraft:spruce_trapdoor[waterlogged=false,half=bottom,powered=false,facing=west,open=false] replace 

execute unless block -636 56 0 minecraft:spruce_door[hinge=left,half=lower,powered=false,facing=south,open=false] run setblock -636 56 0 minecraft:spruce_door[hinge=left,half=lower,powered=false,facing=south,open=false] destroy 
execute unless block -636 57 0 minecraft:spruce_door[hinge=left,half=upper,powered=false,facing=south,open=false] run setblock -636 57 0 minecraft:spruce_door[hinge=left,half=upper,powered=false,facing=south,open=false] destroy 
execute unless block -603 62 -3 minecraft:dark_oak_door[hinge=right,half=lower,powered=false,facing=east,open=false] run setblock -603 62 -3 minecraft:dark_oak_door[hinge=right,half=lower,powered=false,facing=east,open=false] destroy 
execute unless block -603 63 -8 minecraft:dark_oak_door[hinge=right,half=lower,powered=false,facing=west,open=false] run setblock -603 63 -8 minecraft:dark_oak_door[hinge=right,half=lower,powered=false,facing=west,open=false] destroy 
execute unless block -605 63 -6 minecraft:dark_oak_door[hinge=left,half=lower,powered=false,facing=north,open=false] run setblock -605 63 -6 minecraft:dark_oak_door[hinge=left,half=lower,powered=false,facing=north,open=false] destroy 
execute unless block -603 63 -3 minecraft:dark_oak_door[hinge=right,half=upper,powered=false,facing=east,open=false] run setblock -603 63 -3 minecraft:dark_oak_door[hinge=right,half=upper,powered=false,facing=east,open=false] destroy 
execute unless block -603 64 -8 minecraft:dark_oak_door[hinge=right,half=upper,powered=false,facing=west,open=false] run setblock -603 64 -8 minecraft:dark_oak_door[hinge=right,half=upper,powered=false,facing=west,open=false] destroy 
execute unless block -605 64 -6 minecraft:dark_oak_door[hinge=left,half=upper,powered=false,facing=north,open=false] run setblock -605 64 -6 minecraft:dark_oak_door[hinge=left,half=upper,powered=false,facing=north,open=false] destroy 
execute unless block -594 68 -9 minecraft:dark_oak_door[hinge=right,half=lower,powered=false,facing=west,open=false] run setblock -594 68 -9 minecraft:dark_oak_door[hinge=right,half=lower,powered=false,facing=west,open=false] destroy 
execute unless block -594 69 -9 minecraft:dark_oak_door[hinge=right,half=upper,powered=false,facing=west,open=false] run setblock -594 69 -9 minecraft:dark_oak_door[hinge=right,half=upper,powered=false,facing=west,open=false] destroy 
execute unless block -594 59 3 minecraft:dark_oak_door[hinge=right,half=lower,powered=false,facing=north,open=false] run setblock -594 59 3 minecraft:dark_oak_door[hinge=right,half=lower,powered=false,facing=north,open=false] destroy 
execute unless block -594 60 3 minecraft:dark_oak_door[hinge=right,half=upper,powered=false,facing=north,open=false] run setblock -594 60 3 minecraft:dark_oak_door[hinge=right,half=upper,powered=false,facing=north,open=false] destroy 
execute unless block -585 57 -6 minecraft:jungle_door[hinge=right,half=lower,powered=false,facing=east,open=false] run setblock -585 57 -6 minecraft:jungle_door[hinge=right,half=lower,powered=false,facing=east,open=false] destroy 
execute unless block -585 58 -6 minecraft:jungle_door[hinge=right,half=upper,powered=false,facing=east,open=false] run setblock -585 58 -6 minecraft:jungle_door[hinge=right,half=upper,powered=false,facing=east,open=false] destroy 
