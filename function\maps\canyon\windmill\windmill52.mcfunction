execute as @e[type=block_display,tag=canyon_windmill,tag=1] run data merge entity @s {interpolation_duration:1,start_interpolation:0,transformation:[0.008660254f,0f,-0.4f,0.19566987f,0.004890738f,0.6237351f,0.67768055f,-0.21653862f,0.0010395584f,-2.9344428f,0.14404544f,-0.65943104f,0f,0f,0f,1f] }
execute as @e[type=block_display,tag=canyon_windmill,tag=2] run data merge entity @s {interpolation_duration:1,start_interpolation:0,transformation:[0.008660254f,0f,-0.4f,0.19566987f,0.0037157242f,2.007392f,0.5148658f,0.14218758f,0.003345653f,-2.2294345f,0.46358728f,-0.67935336f,0f,0f,0f,1f] }
execute as @e[type=block_display,tag=canyon_windmill,tag=3] run data merge entity @s {interpolation_duration:1,start_interpolation:0,transformation:[0.008660254f,0f,-0.4f,0.19566987f,0.001545085f,2.8531694f,0.21409325f,0.46281475f,0.004755283f,-0.927051f,0.6589113f,-0.5172435f,0f,0f,0f,1f] }
execute as @e[type=block_display,tag=canyon_windmill,tag=4] run data merge entity @s {interpolation_duration:1,start_interpolation:0,transformation:[0.008660254f,0f,-0.4f,0.19566987f,-0.0010395584f,2.9344428f,-0.14404544f,0.65943104f,0.004890738f,0.6237351f,0.67768055f,-0.21653862f,0f,0f,0f,1f] }
execute as @e[type=block_display,tag=canyon_windmill,tag=5] run data merge entity @s {interpolation_duration:1,start_interpolation:0,transformation:[0.008660254f,0f,-0.4f,0.19566987f,-0.003345653f,2.2294345f,-0.46358728f,0.67935336f,0.0037157242f,2.007392f,0.5148658f,0.14218758f,0f,0f,0f,1f] }
execute as @e[type=block_display,tag=canyon_windmill,tag=6] run data merge entity @s {interpolation_duration:1,start_interpolation:0,transformation:[0.008660254f,0f,-0.4f,0.19566987f,-0.004755283f,0.927051f,-0.6589113f,0.5172435f,0.001545085f,2.8531694f,0.21409325f,0.46281475f,0f,0f,0f,1f] }
execute as @e[type=block_display,tag=canyon_windmill,tag=7] run data merge entity @s {interpolation_duration:1,start_interpolation:0,transformation:[0.008660254f,0f,-0.4f,0.19566987f,-0.004890738f,-0.6237351f,-0.67768055f,0.21653862f,-0.0010395584f,2.9344428f,-0.14404544f,0.65943104f,0f,0f,0f,1f] }
execute as @e[type=block_display,tag=canyon_windmill,tag=8] run data merge entity @s {interpolation_duration:1,start_interpolation:0,transformation:[0.008660254f,0f,-0.4f,0.19566987f,-0.0037157242f,-2.007392f,-0.5148658f,-0.14218758f,-0.003345653f,2.2294345f,-0.46358728f,0.67935336f,0f,0f,0f,1f] }
execute as @e[type=block_display,tag=canyon_windmill,tag=9] run data merge entity @s {interpolation_duration:1,start_interpolation:0,transformation:[0.008660254f,0f,-0.4f,0.19566987f,-0.001545085f,-2.8531694f,-0.21409325f,-0.46281475f,-0.004755283f,0.927051f,-0.6589113f,0.5172435f,0f,0f,0f,1f] }
execute as @e[type=block_display,tag=canyon_windmill,tag=10] run data merge entity @s {interpolation_duration:1,start_interpolation:0,transformation:[0.008660254f,0f,-0.4f,0.19566987f,0.0010395584f,-2.9344428f,0.14404544f,-0.65943104f,-0.004890738f,-0.6237351f,-0.67768055f,0.21653862f,0f,0f,0f,1f] }
execute as @e[type=block_display,tag=canyon_windmill,tag=11] run data merge entity @s {interpolation_duration:1,start_interpolation:0,transformation:[0.008660254f,0f,-0.4f,0.19566987f,0.003345653f,-2.2294345f,0.46358728f,-0.67935336f,-0.0037157242f,-2.007392f,-0.5148658f,-0.14218758f,0f,0f,0f,1f] }
execute as @e[type=block_display,tag=canyon_windmill,tag=12] run data merge entity @s {interpolation_duration:1,start_interpolation:0,transformation:[0.008660254f,0f,-0.4f,0.19566987f,0.004755283f,-0.927051f,0.6589113f,-0.5172435f,-0.001545085f,-2.8531694f,-0.21409325f,-0.46281475f,0f,0f,0f,1f] }
