
execute as @e[type=text_display,tag=map_random] run data merge entity @s {text:'{"translate":"mcm.lobby.vote","with":[{"score":{"objective":"vote_count","name":"random"},"color":"#FFE700"}]}'}
execute as @e[type=text_display,tag=map1,tag=!disabled] run data merge entity @s {text:'{"translate":"mcm.lobby.vote","with":[{"score":{"objective":"vote_count","name":"map1"},"color":"#FFE700"}]}'}
execute as @e[type=text_display,tag=map2,tag=!disabled] run data merge entity @s {text:'{"translate":"mcm.lobby.vote","with":[{"score":{"objective":"vote_count","name":"map2"},"color":"#FFE700"}]}'}
execute as @e[type=text_display,tag=map3,tag=!disabled] run data merge entity @s {text:'{"translate":"mcm.lobby.vote","with":[{"score":{"objective":"vote_count","name":"map3"},"color":"#FFE700"}]}'}
execute as @e[type=text_display,tag=map4,tag=!disabled] run data merge entity @s {text:'{"translate":"mcm.lobby.vote","with":[{"score":{"objective":"vote_count","name":"map4"},"color":"#FFE700"}]}'}
execute as @e[type=text_display,tag=map5,tag=!disabled] run data merge entity @s {text:'{"translate":"mcm.lobby.vote","with":[{"score":{"objective":"vote_count","name":"map5"},"color":"#FFE700"}]}'}
execute as @e[type=text_display,tag=map6,tag=!disabled] run data merge entity @s {text:'{"translate":"mcm.lobby.vote","with":[{"score":{"objective":"vote_count","name":"map6"},"color":"#FFE700"}]}'}