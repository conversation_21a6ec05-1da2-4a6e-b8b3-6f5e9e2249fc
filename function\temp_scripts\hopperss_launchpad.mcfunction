execute unless block -1108 58 -1145 minecraft:hopper[facing=down,enabled=true] run setblock -1108 58 -1145 hopper[enabled=false] replace 
execute unless block -1108 58 -1049 minecraft:hopper[facing=down,enabled=true] run setblock -1108 58 -1049 hopper[enabled=false] replace 
execute unless block -1064 58 -1005 minecraft:hopper[facing=down,enabled=true] run setblock -1064 58 -1005 hopper[enabled=false] replace 
execute unless block -1035 51 -1272 minecraft:hopper[facing=down,enabled=true] run setblock -1035 51 -1272 hopper[enabled=false] replace 
execute unless block -1034 51 -1272 minecraft:hopper[facing=south,enabled=false] run setblock -1034 51 -1272 hopper[enabled=false] replace 
execute unless block -1035 51 -1270 minecraft:hopper[facing=down,enabled=true] run setblock -1035 51 -1270 hopper[enabled=false] replace 
execute unless block -1035 51 -1269 minecraft:hopper[facing=east,enabled=false] run setblock -1035 51 -1269 hopper[enabled=false] replace 
execute unless block -1034 51 -1269 minecraft:hopper[facing=down,enabled=true] run setblock -1034 51 -1269 hopper[enabled=false] replace 
execute unless block -1034 51 -1268 minecraft:hopper[facing=north,enabled=false] run setblock -1034 51 -1268 hopper[enabled=false] replace 
execute unless block -1035 52 -1272 minecraft:hopper[facing=down,enabled=true] run setblock -1035 52 -1272 hopper[enabled=false] replace 
execute unless block -1035 52 -1271 minecraft:hopper[facing=north,enabled=true] run setblock -1035 52 -1271 hopper[enabled=false] replace 
execute unless block -1034 52 -1269 minecraft:hopper[facing=down,enabled=false] run setblock -1034 52 -1269 hopper[enabled=false] replace 
execute unless block -1035 53 -1272 minecraft:hopper[facing=west,enabled=false] run setblock -1035 53 -1272 hopper[enabled=false] replace 
execute unless block -1028 58 -1005 minecraft:hopper[facing=down,enabled=true] run setblock -1028 58 -1005 hopper[enabled=false] replace 
execute unless block -965 59 -1233 minecraft:hopper[facing=down,enabled=true] run setblock -965 59 -1233 hopper[enabled=false] replace 
execute unless block -965 59 -1209 minecraft:hopper[facing=down,enabled=true] run setblock -965 59 -1209 hopper[enabled=false] replace 
execute unless block -968 58 -1005 minecraft:hopper[facing=down,enabled=true] run setblock -968 58 -1005 hopper[enabled=false] replace 
execute unless block -944 57 -1146 minecraft:hopper[facing=north,enabled=true] run setblock -944 57 -1146 hopper[enabled=false] replace 
execute unless block -923 58 -1145 minecraft:hopper[facing=down,enabled=true] run setblock -923 58 -1145 hopper[enabled=false] replace 
execute unless block -923 58 -1112 minecraft:hopper[facing=down,enabled=true] run setblock -923 58 -1112 hopper[enabled=false] replace 
execute unless block -923 58 -1106 minecraft:hopper[facing=down,enabled=true] run setblock -923 58 -1106 hopper[enabled=false] replace 
execute unless block -923 58 -1073 minecraft:hopper[facing=down,enabled=true] run setblock -923 58 -1073 hopper[enabled=false] replace 
execute unless block -923 58 -1049 minecraft:hopper[facing=down,enabled=true] run setblock -923 58 -1049 hopper[enabled=false] replace 
