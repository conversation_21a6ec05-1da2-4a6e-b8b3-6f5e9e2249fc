execute if score $windmilltimer CmdData matches 0 run function mcm:maps/canyon/windmill/windmill0
execute if score $windmilltimer CmdData matches 1 run function mcm:maps/canyon/windmill/windmill1
execute if score $windmilltimer CmdData matches 2 run function mcm:maps/canyon/windmill/windmill2
execute if score $windmilltimer CmdData matches 3 run function mcm:maps/canyon/windmill/windmill3
execute if score $windmilltimer CmdData matches 4 run function mcm:maps/canyon/windmill/windmill4
execute if score $windmilltimer CmdData matches 5 run function mcm:maps/canyon/windmill/windmill5
execute if score $windmilltimer CmdData matches 6 run function mcm:maps/canyon/windmill/windmill6
execute if score $windmilltimer CmdData matches 7 run function mcm:maps/canyon/windmill/windmill7
execute if score $windmilltimer CmdData matches 8 run function mcm:maps/canyon/windmill/windmill8
execute if score $windmilltimer CmdData matches 9 run function mcm:maps/canyon/windmill/windmill9
execute if score $windmilltimer CmdData matches 10 run function mcm:maps/canyon/windmill/windmill10
execute if score $windmilltimer CmdData matches 11 run function mcm:maps/canyon/windmill/windmill11
execute if score $windmilltimer CmdData matches 12 run function mcm:maps/canyon/windmill/windmill12
execute if score $windmilltimer CmdData matches 13 run function mcm:maps/canyon/windmill/windmill13
execute if score $windmilltimer CmdData matches 14 run function mcm:maps/canyon/windmill/windmill14
execute if score $windmilltimer CmdData matches 15 run function mcm:maps/canyon/windmill/windmill15
execute if score $windmilltimer CmdData matches 16 run function mcm:maps/canyon/windmill/windmill16
execute if score $windmilltimer CmdData matches 17 run function mcm:maps/canyon/windmill/windmill17
execute if score $windmilltimer CmdData matches 18 run function mcm:maps/canyon/windmill/windmill18
execute if score $windmilltimer CmdData matches 19 run function mcm:maps/canyon/windmill/windmill19
execute if score $windmilltimer CmdData matches 20 run function mcm:maps/canyon/windmill/windmill20
execute if score $windmilltimer CmdData matches 21 run function mcm:maps/canyon/windmill/windmill21
execute if score $windmilltimer CmdData matches 22 run function mcm:maps/canyon/windmill/windmill22
execute if score $windmilltimer CmdData matches 23 run function mcm:maps/canyon/windmill/windmill23
execute if score $windmilltimer CmdData matches 24 run function mcm:maps/canyon/windmill/windmill24
execute if score $windmilltimer CmdData matches 25 run function mcm:maps/canyon/windmill/windmill25
execute if score $windmilltimer CmdData matches 26 run function mcm:maps/canyon/windmill/windmill26
execute if score $windmilltimer CmdData matches 27 run function mcm:maps/canyon/windmill/windmill27
execute if score $windmilltimer CmdData matches 28 run function mcm:maps/canyon/windmill/windmill28
execute if score $windmilltimer CmdData matches 29 run function mcm:maps/canyon/windmill/windmill29
execute if score $windmilltimer CmdData matches 30 run function mcm:maps/canyon/windmill/windmill30
execute if score $windmilltimer CmdData matches 31 run function mcm:maps/canyon/windmill/windmill31
execute if score $windmilltimer CmdData matches 32 run function mcm:maps/canyon/windmill/windmill32
execute if score $windmilltimer CmdData matches 33 run function mcm:maps/canyon/windmill/windmill33
execute if score $windmilltimer CmdData matches 34 run function mcm:maps/canyon/windmill/windmill34
execute if score $windmilltimer CmdData matches 35 run function mcm:maps/canyon/windmill/windmill35
execute if score $windmilltimer CmdData matches 36 run function mcm:maps/canyon/windmill/windmill36
execute if score $windmilltimer CmdData matches 37 run function mcm:maps/canyon/windmill/windmill37
execute if score $windmilltimer CmdData matches 38 run function mcm:maps/canyon/windmill/windmill38
execute if score $windmilltimer CmdData matches 39 run function mcm:maps/canyon/windmill/windmill39
execute if score $windmilltimer CmdData matches 40 run function mcm:maps/canyon/windmill/windmill40
execute if score $windmilltimer CmdData matches 41 run function mcm:maps/canyon/windmill/windmill41
execute if score $windmilltimer CmdData matches 42 run function mcm:maps/canyon/windmill/windmill42
execute if score $windmilltimer CmdData matches 43 run function mcm:maps/canyon/windmill/windmill43
execute if score $windmilltimer CmdData matches 44 run function mcm:maps/canyon/windmill/windmill44
execute if score $windmilltimer CmdData matches 45 run function mcm:maps/canyon/windmill/windmill45
execute if score $windmilltimer CmdData matches 46 run function mcm:maps/canyon/windmill/windmill46
execute if score $windmilltimer CmdData matches 47 run function mcm:maps/canyon/windmill/windmill47
execute if score $windmilltimer CmdData matches 48 run function mcm:maps/canyon/windmill/windmill48
execute if score $windmilltimer CmdData matches 49 run function mcm:maps/canyon/windmill/windmill49
execute if score $windmilltimer CmdData matches 50 run function mcm:maps/canyon/windmill/windmill50
execute if score $windmilltimer CmdData matches 51 run function mcm:maps/canyon/windmill/windmill51
execute if score $windmilltimer CmdData matches 52 run function mcm:maps/canyon/windmill/windmill52
execute if score $windmilltimer CmdData matches 53 run function mcm:maps/canyon/windmill/windmill53
execute if score $windmilltimer CmdData matches 54 run function mcm:maps/canyon/windmill/windmill54
execute if score $windmilltimer CmdData matches 55 run function mcm:maps/canyon/windmill/windmill55
execute if score $windmilltimer CmdData matches 56 run function mcm:maps/canyon/windmill/windmill56
execute if score $windmilltimer CmdData matches 57 run function mcm:maps/canyon/windmill/windmill57
execute if score $windmilltimer CmdData matches 58 run function mcm:maps/canyon/windmill/windmill58
execute if score $windmilltimer CmdData matches 59 run function mcm:maps/canyon/windmill/windmill59

scoreboard players add $windmilltimer CmdData 1
execute if score $windmilltimer CmdData matches 60.. run scoreboard players set $windmilltimer CmdData 0
