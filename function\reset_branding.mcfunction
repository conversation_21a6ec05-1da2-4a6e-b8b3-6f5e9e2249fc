# reset_branding.mcfunction

# NOTE: this is not executed at all, except manually, in case the boss bar
# for Backstabbed! by Bagel Buddies branding needs an update.
# Be sure to set the version correctly.
bossbar remove mcm:branding

bossbar add mcm:branding {"text":"test"}
bossbar set mcm:branding name {"text":"Back","color":"#6dc6ff","extra":[{"text":"stabbed!","color":"#ff5f79"},{"text":" v1.3.4 ","color":"white"},{"text":"by ","color":"white"},{"text":"Bagel Buddies","color":"gold"}]}
bossbar set mcm:branding value 0
bossbar set mcm:branding color white
