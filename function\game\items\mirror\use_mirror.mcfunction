#> Mirror usage function
#> Detects the nearest player and reveals their identity (innocent/murderer)

# Reset carrot on a stick usage
function mcm:util/reset_carrot_on_stick

# Find the nearest player (excluding self)
execute as @s at @s positioned as @a[tag=queued,tag=!spectating,distance=0.1..,limit=1,sort=nearest] run function mcm:math/dist

# Store the distance for display
execute store result score $mirror_distance temp run scoreboard players get $out temp

# Get the nearest player's identity
execute as @s at @s run tag @a[tag=queued,tag=!spectating,distance=0.1..,limit=1,sort=nearest] add mirror_target

# Check if target is a murderer
execute if entity @a[tag=mirror_target,tag=murderer] run title @s actionbar [{"translate":"mcm.item.mirror.result.murderer","color":"red","bold":true},{"text":" (","color":"gray"},{"score":{"name":"$mirror_distance","objective":"temp"},"color":"gold"},{"text":"m)","color":"gray"}]
execute if entity @a[tag=mirror_target,tag=murderer] run playsound minecraft:block.glass.break player @s ~ ~ ~ 0.5 0.8

# Check if target is innocent
execute if entity @a[tag=mirror_target,tag=innocent,tag=!murderer] run title @s actionbar [{"translate":"mcm.item.mirror.result.innocent","color":"green","bold":true},{"text":" (","color":"gray"},{"score":{"name":"$mirror_distance","objective":"temp"},"color":"gold"},{"text":"m)","color":"gray"}]
execute if entity @a[tag=mirror_target,tag=innocent,tag=!murderer] run playsound minecraft:block.amethyst_block.chime player @s ~ ~ ~ 0.5 1.2

# If no valid target found
execute unless entity @a[tag=mirror_target] run title @s actionbar {"translate":"mcm.item.mirror.no_target","color":"gray","italic":true}
execute unless entity @a[tag=mirror_target] run playsound minecraft:block.glass.place player @s ~ ~ ~ 0.3 0.5

# Clean up tags
tag @a remove mirror_target

# Add cooldown to prevent spam (optional)
scoreboard players set @s carrot 0
