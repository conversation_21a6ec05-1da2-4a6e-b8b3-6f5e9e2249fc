

execute store result score $murderers GameRules run data get storage mcm:options options.murderers.value
execute store result score $roundtimer GameRules run data get storage mcm:options options.gametime.value

execute store result score $animate GameRules run data get storage mcm:options options.animations
execute store result score $autostart GameRules run data get storage mcm:options options.autostart
execute store result score $startscrap GameRules run data get storage mcm:options options.scrap_start
execute store result score $smart_murderers GameRules run data get storage mcm:options options.murderers.smart
execute store result score $murderer_ff GameRules run data get storage mcm:options options.murderer_ff
execute store result score $updatebossbar GameRules run data get storage mcm:options options.update_ingame_counts
execute store result score $darkness GameRules run data get storage mcm:options options.darkness
execute store result score $destroyguns GameRules run data get storage mcm:options options.recall_destroy_guns
