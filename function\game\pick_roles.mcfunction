#> Assign roles when grace period is over

#> Force murder assign for dubug reasons
tag @a[tag=force_murderer] add murderer

execute if entity @a[tag=force_murderer,tag=queued] run tellraw @a [{"text":"Murderer override is currently active!","color":"red","bold":true}]
execute if entity @a[tag=force_murderer,tag=queued] run tellraw @a [{"text":"Murderers: ","color":"red"},{"selector":"@a[tag=queued,tag=force_murderer]"}]


scoreboard players operation $num_murderers CmdData = $murderers GameRules
execute unless entity @a[tag=queued,tag=force_murderer] run function mcm:game/assign_murderer

execute if score $murderers GameRules matches ..1 run tellraw @a[tag=murderer] {"translate":"mcm.game.role","color":"gold","with":[{"translate":"mcm.game.murderer","color":"red"}]}
title @a[tag=murderer] title {"translate":"mcm.game.role","color":"gold","with":[{"translate":"mcm.game.murderer","color":"red"}]}
execute if score $murderers GameRules matches ..1 run title @a[tag=murderer] subtitle {"translate":"mcm.game.murderer.subtitle","color":"gold"}
schedule function mcm:game/murderertip 6s

execute if score $murderers GameRules matches 2 as @a[tag=murderer] at @s run title @s subtitle {"translate":"mcm.game.murderer2.subtitle", "color":"gold", "with" :[{"selector" : "@p[tag=murderer,distance=0.01..]", "color":"green"}]}
execute if score $murderers GameRules matches 2 as @a[tag=murderer] at @s run tellraw @s {"translate":"mcm.game.murderer2.msg","color":"gold","with":[{"selector":"@p[tag=murderer,distance=0.01..]", "color":"green"}, {"selector":"@s","color":"green"}, {"translate":"mcm.game.murderers","color":"gold"}]}

execute if score $murderers GameRules matches 3 as @a[tag=murderer] at @s run title @s subtitle {"translate":"mcm.game.murderer3.subtitle", "color":"gold", "with" :[{"selector" :"@p[tag=murderer,distance=0.01..]", "color":"green"},{"selector" :"@p[tag=murderer,distance=0.01..,sort=furthest]", "color":"green"}]}
execute if score $murderers GameRules matches 3 as @a[tag=murderer] at @s run tellraw @s {"translate":"mcm.game.murderer3.msg","color":"gold","with":[{"selector":"@p[tag=murderer,distance=0.01..]", "color":"green"}, {"selector":"@p[tag=murderer,distance=0.01..,sort=furthest]", "color":"green"}, {"selector":"@s","color":"green"}, {"translate":"mcm.game.murderers","color":"gold"}]}

# Murderer items
execute as @a[tag=murderer] run function mcm:items/loadouts/murderer
execute as @a[tag=murderer] run item replace entity @s hotbar.8 with netherite_scrap[custom_data={no_drop_on_death:1b,Tags:["KeyItem"]},custom_name='{"translate":"mcm.item.scrap","italic":false}',lore=['[{"translate":"mcm.item.scrap.lore","italic":false}]']]
# Give murderers their 1 free recall
tag @a[tag=murderer] add free_knife


# Gunner
execute as @a[tag=queued,tag=!murderer,limit=1,sort=random] at @s run tag @s add gunner
# Gun gets NoDrop because it's already in an inventory
execute as @a[tag=gunner] run function mcm:items/give {item: gun}
execute as @a[tag=gunner] at @s run tellraw @s {"translate":"mcm.game.role","color":"gold","with":[{"translate":"mcm.game.gunner","color":"dark_aqua"}]}
execute as @a[tag=gunner] at @s run title @s title {"translate":"mcm.game.role","color":"gold","with":[{"translate":"mcm.game.gunner","color":"dark_aqua"}]}
execute as @a[tag=gunner] at @s run title @s subtitle {"translate":"mcm.game.gunner.subtitle","color":"dark_gray"}
schedule function mcm:game/gunnertip 6s
#execute as @a[tag=gunner] at @s run item replace entity @s hotbar.8 with netherite_scrap[custom_model_data=1,custom_data={no_drop_on_death:1b,Tags:["KeyItem"]},custom_name='{"translate":"mcm.item.scrap","italic":false}',lore=['[{"translate":"mcm.item.scrap.lore","italic":false}]']]

# Innocent
execute as @a[tag=queued,tag=!murderer,tag=!gunner] at @s run tag @s add innocent
execute if score $startscrap GameRules matches 1.. run item replace entity @a[tag=innocent] hotbar.8 with netherite_scrap[custom_data={no_drop_on_death:1b,Tags:["KeyItem"]},custom_name='{"translate":"mcm.item.scrap","italic":false}',lore=['[{"translate":"mcm.item.scrap.lore","italic":false}]']]
tellraw @a[tag=innocent] {"translate":"mcm.game.role","color":"gold","with":[{"translate":"mcm.game.innocent","color":"light_purple"}]}
title @a[tag=innocent] title {"translate":"mcm.game.role","color":"gold","with":[{"translate":"mcm.game.innocent","color":"light_purple"}]}
title @a[tag=innocent] subtitle {"translate":"mcm.game.innocent.subtitle","color":"gold"}
schedule function mcm:game/innocenttip 6s
# Make it easier to track the gunner as an innocent
execute as @a[tag=queued,tag=gunner] at @s run tag @s add innocent

# Give everyone a spyglass in their 8th slot
item replace entity @a[tag=queued] hotbar.7 with spyglass[custom_data={NoDrop:1b,no_drop_on_death:1b}]

# Give one random innocent player a mirror
execute as @a[tag=innocent,tag=!murderer,tag=!gunner,limit=1,sort=random] run function mcm:items/give {item: mirror}
execute as @a[tag=innocent,tag=!murderer,tag=!gunner] if items entity @s container.* carrot_on_a_stick[custom_data~{mirror:1b}] run tag @s add mirror_holder
execute as @a[tag=mirror_holder] run tellraw @s [{"translate":"mcm.item.mirror.received","color":"aqua","bold":true}]
execute as @a[tag=mirror_holder] run playsound minecraft:block.amethyst_block.chime player @s ~ ~ ~ 1 1.5

scoreboard players set $pickedroles CmdData 1
playsound minecraft:block.beehive.enter ambient @a ~ ~ ~ 1 0 1

execute store result score $InnocentCount CmdData if entity @a[tag=innocent,tag=!spectating]
execute store result score $MurdererCount CmdData if entity @a[tag=murderer,tag=!spectating]